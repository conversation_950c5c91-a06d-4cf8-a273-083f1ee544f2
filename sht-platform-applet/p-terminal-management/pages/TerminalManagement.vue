<template>
  <div id="terminalManagement">
    <main>
      <p>
        终端总量<span>{{ aboutCount.allCount || 0 }}台</span>
      </p>
      <section class="dataCard">
        <div>
          <p>
            未绑定 <span>{{ aboutCount.unBoundCount }}</span>
          </p>
          <p>
            已绑定 <span>{{ aboutCount.boundCount }}</span>
          </p>
          <div :class="{ thenGetData: aboutCount.allCount }">
            <span :style="{ width: countFormat(aboutCount.unBoundCount, aboutCount.boundCount) + '%' }"></span>
          </div>
        </div>
        <div>
          <p>
            未激活 <span>{{ aboutCount.unActivateCount }}</span>
          </p>
          <p>
            已激活 <span>{{ aboutCount.activateCount }}</span>
          </p>
          <div :class="{ thenGetData: aboutCount.allCount }">
            <span :style="{ width: countFormat(aboutCount.unActivateCount, aboutCount.activateCount) + '%' }"></span>
          </div>
        </div>
      </section>

      <section class="menus">
        <template v-for="(i, index) in menus">
          <div v-if="i.show !== false" @click="toMenuItem(i.url)" :key="index">
            <image :src="i.img" alt="" />
            <span>{{ i.title }}</span>
          </div>
        </template>
      </section>
    </main>
  </div>
</template>

<script>
import { getAgentTerminalCount } from '../../http/api';

export default {
  name: 'TerminalManagement',
  data() {
    return {
      menus: [
        {
          img: require('../static/images/terminalQuery.png'),
          title: '我的终端',
          url: 'MineTerminal'
        },

        {
          img: require('../static/images/terminalTransfer.png'),
          title: '终端划拨',
          url: 'Stir'
        },
        {
          img: require('../static/images/notActive.png'),
          title: '未/伪激活',
          url: 'NotAndFakeActive'
        },
        {
          img: require('../static/images/modelStatistics.png'),
          title: '型号统计',
          url: 'EquipmentManagement'
        },
        {
          img: require('../static/images/terminalQuery.png'),
          title: '团队终端',
          url: 'MyTerminals'
        },

        {
          img: require('../static/images/terminalCallback.png'),
          title: '终端回拨',
          url: 'AgentRetrace'
        },
        {
          img: require('../static/images/terminalQuery.png'),
          title: '终端流量卡',
          url: 'TerminalSim'
        },
        {
          img: require('../static/images/terminalQuery.png'),
          title: '达标查询',
          url: 'SnReachInfo'
        },
        {
          img: require('../static/images/terminalQuery.png'),
          title: '活动调整',
          url: 'ChangeReachCashBackRules'
        },
        {
          show: !(typeof plus === 'object') || this.$store.state.isIosCheckPass == 1,
          img: require('../../static/images/home/<USER>'),
          title: '物料商城',
          url: 'MaterialStore'
        }
      ],
      aboutCount: {
        allCount: 0, //终端总量
        boundCount: 0, //已绑定
        unBoundCount: 0, //未绑定
        activateCount: 0, //已激活
        unActivateCount: 0 //未激活
      }
    };
  },
  onLoad() {
    this.getAboutCount();
  },
  methods: {
    countFormat(val, val2) {
      return Number(val / (val + val2)).toFixed(2) * 100;
    },
    toMenuItem(name) {
      this.$Router.push({ name });
    },
    getAboutCount() {
      getAgentTerminalCount().then(res => {
        if (res.code == '00') {
          this.aboutCount = res.data;
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
#terminalManagement {
  > main {
    overflow: hidden;
    min-height: 100%;
    padding: 0 30rpx;
    > p {
      display: flex;
      justify-content: space-between;
      margin: 36rpx 0;
      color: #666;
      > span {
        color: #0358b9;
        font-size: 34rpx;
        font-weight: 500;
      }
    }
    .dataCard {
      display: flex;
      justify-content: space-between;
      margin-bottom: 120rpx;
      > div {
        width: calc(50% - 15rpx);
        padding: 20rpx;
        background-color: #fff;
        border-radius: 10rpx;
        box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.12), 0 0 12rpx rgba(0, 0, 0, 0.04);
        > p {
          position: relative;
          margin: 0;
          margin-bottom: 14rpx;
          text-indent: 30rpx;
          font-size: 24rpx;
          color: #666;
          &::after {
            content: '';
            position: absolute;
            top: 8rpx;
            left: 0;
            width: 16rpx;
            height: 16rpx;
            border-radius: 50%;
          }
          &:first-of-type {
            &::after {
              background-color: orange;
            }
          }
          &:nth-of-type(2) {
            &::after {
              background-color: #2e7edc;
            }
          }
          > span {
            color: #333;
            margin-left: 0.1em;
          }
        }
        > div {
          display: flex;
          height: 20rpx;
          border-radius: 20rpx;
          background-color: #f8f8f8;
          > span {
            border-radius: 20rpx;
            background-color: orange;
          }
        }
        .thenGetData {
          background-color: #2e7edc;
        }
      }
    }

    .menus {
      display: flex;
      flex-wrap: wrap;
      > div {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 25%;
        margin-bottom: 60rpx;
        color: #666;
        font-size: 26rpx;
        > image {
          width: 52rpx;
          height: 52rpx;
          object-fit: contain;
          margin-bottom: 20rpx;
        }
      }
    }
  }
}
</style>
