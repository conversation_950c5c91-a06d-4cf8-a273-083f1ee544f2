<template>
    <div id="modelDetails">
        <u-sticky>
            <div class="search-bar">
                <view class="content">
                    <u-field :label-width="40" icon="search" :border-bottom="false" placeholder="请输入终端号" v-model="terminalNo" />
                </view>
            </div>
        </u-sticky>

        <no-content v-show="show" />

        <view class="list-data" v-show="!show">
            <section v-for="(m, index) in model" :key="index" @click="machineDetails(m.terminalNo, m.agentCode)">
                <view>
                    <p><span>终端号：</span> <text  user-select selectable>{{ m.terminalNo }}</text></p>
                    <p><span>代理商编号：</span> {{ m.agentCode }}</p>
                    <p><span>代理商名称：</span> {{ m.realName }}</p>
                </view>
                <p>
                    <u-icon name="arrow-right" color="darkgrey" />
                </p>
            </section>
        </view>

        <u-loadmore v-if="!show" :status="status" @loadmore="loadmore" />
    </div>
</template>

<script>
import { getTerminalPageList } from "../../../http/api";
import { debounce } from '../../../static/utils/date';

export default {
    name: "ModelDetails",
    data() {
        return {
            show: false,
            terminalNo: '',
            model: [],
            status: 'loading',
            total: 0,
        };
    },
    watch: {
        terminalNo: debounce(function () {
            this.getList();
        }, 700)
    },
    onLoad() {
        this.getList()
    },
    onReachBottom() {
        this.loadmore()
    },
    methods: {
        getList() {
            this.status = 'loading'
            getTerminalPageList({
                terminalType: this.$Route.query.terminalType,
                terminalNo: this.terminalNo,
                pageNo: 1,
                pageSize: 10,
            }).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.total;
                    uni.pageScrollTo({
                        scrollTop: 0
                    });
                    if (res.data.list.length != 0) {
                        this.show = false;
                        this.model = res.data.list;
                        if (this.model.length >= this.total) {
                            // 数据全部加载完成
                            this.status = 'nomore';
                        } else {
                            this.status = 'loadmore';
                        }
                    } else {
                        this.show = true;
                    }
                }
            });
        },
        loadmore() {
            if (this.status == 'nomore') return;
            this.status = 'loading'
            getTerminalPageList({
                terminalType: this.$Route.query.terminalType,
                terminalNo: this.terminalNo,
                pageNo: this.model.length / 10 + 1,
                pageSize: 10,
            }).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.total;
                    res.data.list.forEach((i) => {
                        this.model.push(i);
                    });
                }
                if (this.model.length >= this.total) {
                    this.status = 'nomore';
                } else {
                    this.status = 'loadmore';
                }
            }).catch((err) => {
                this.error = true;
            });
        },
        machineDetails(terminalNo, agentCode) {
            this.$Router.push({ name: "MachineDetails", params: { terminalNo, agentCode } });
        }
    }
};
</script>

<style lang="less" scoped>
#modelDetails {
    .list-data {
        > section {
            background: #fff;
            border-radius: 20rpx;
            padding: 30rpx;
            margin: 0 30rpx 20rpx;
            position: relative;
            display: flex;
            justify-content: space-between;
            > view {
                > p {
                    margin: 0;
                    > span {
                        color: #666666;
                    }
                    &:nth-of-type(2) {
                        margin: 30rpx 0;
                    }
                }
            }
            > p {
                flex-shrink: 0;
                margin-left: 20rpx;
            }
        }
    }
}
</style>