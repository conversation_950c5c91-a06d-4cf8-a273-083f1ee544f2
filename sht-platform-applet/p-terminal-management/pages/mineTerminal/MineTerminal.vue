<template>
  <div id="mine-terminal">
    <view class="filter-proup">
      <u-sticky>
        <view class="filter" @click="select">
          <text class="u-m-r-10" :class="{ 'has-filter': isHasTerm }"
            >筛选</text
          >
          <u-icon name="arrow-down" :color="isHasTerm ? '#4a67d6' : ''" />
        </view>
      </u-sticky>
      <u-popup v-model="showSelect" mode="bottom">
        <header>
          <span>筛选</span>
          <view>
            <u-button
              plain
              hover-class="none"
              @click="cancel"
              :hair-line="false"
              >取消</u-button
            >
          </view>
        </header>
        <div>
          <p>终端状态</p>
          <section>
            <u-button
              v-for="(t, index) in terStatus"
              :key="index"
              :hair-line="false"
              :class="t.value === agent.terStatus ? 'active' : 'inactive'"
              @click="agent.terStatus = t.value"
              >{{ t.text }}</u-button
            >
          </section>
        </div>
        <div>
          <p>激活状态</p>
          <section>
            <u-button
              v-for="(a, index) in actStatus"
              :key="index"
              :hair-line="false"
              :class="a.value === agent.actStatus ? 'active' : 'inactive'"
              @click="agent.actStatus = a.value"
              >{{ a.text }}</u-button
            >
          </section>
        </div>
        <p class="tool-btns">
          <u-button size="medium" @click="reset">重置</u-button>
          <u-button size="medium" type="primary" @click="getTeminalList(true)"
            >确定</u-button
          >
        </p>
      </u-popup>
    </view>
    <u-field
      label="支付通道"
      input-align="right"
      placeholder="选择支付通道"
      :value="payOrgCode"
      @click="showPayOrgCodes"
      :right-icon="`arrow-${payOrgCodesPicker ? 'up' : 'down'}`"
      disabled
    />
    <u-select
      v-model="payOrgCodesPicker"
      :list="payOrgCodes"
      label-name="name"
      value-name="code"
      @confirm="payOrgCodeConfirm"
      :default-value="
        payOrgCode ? [payOrgCodes.findIndex((i) => i.name === payOrgCode)] : []
      "
    />
    <div class="search-bar">
      <view class="content">
        <u-field
          :label-width="50"
          icon="search"
          :border-bottom="false"
          type="text"
          placeholder="请输入终端号"
          v-model="agent.terminalNo"
        />
      </view>
    </div>

    <main>
      <no-content v-show="show" />

      <view v-show="!show" class="list-data">
        <section v-for="(t, index) in teminals" :key="index">
          <div class="info">
            <div>
              <image src="../../static/images/terminal.png" alt="" />
              <p>
                <span
                  ><text user-select selectable>{{ t.terminalNo }}</text> ({{
                    t.payOrgCode | orgCodeFormat
                  }})</span
                >
                <span>{{
                  t.actStatus == 0
                    ? '不参与激活'
                    : t.actStatus == 1
                    ? '已激活'
                    : '未激活'
                }}</span>
              </p>
            </div>
            <p style="flex-shrink: 0">{{
              t.terStatus == 0 ? '入库' : t.terStatus == 1 ? '下拨' : '绑定'
            }}</p>
          </div>
          <div class="tools-btn">
            <span @click="toDetail(t.agentCode, t.terminalNo, t.payOrgCode)"
              >终端详情</span
            >
            <template v-if="t.terStatus == 2">
              <span
                v-if="t.actStatus != 0"
                @click="toActiveDetail(t.id, t.payOrgCode)"
                >激活概况</span
              >
              <span
                v-if="t.simFirstDeduction == 0 || t.simRoundDeduction == 0"
                @click="toSimDetail(t.id, t.payOrgCode)"
                >流量费详情</span
              >
            </template>
            <span
              v-if="t.terStatus != 2 && t.payOrgCode == '0020'"
              @click="
                handelViewCodePlate(t.agentCode, t.terminalNo, t.payOrgCode)
              "
              >查看码牌</span
            >
          </div>
        </section>
      </view>

      <u-loadmore
        v-if="!show"
        :status="status"
        @loadmore="getTeminalList(false)"
      />
    </main>

    <view v-if="showQRcard">
      <u-popup
        v-model="showQRcard"
        mode="center"
        :zoom="false"
        border-radius="14"
        height="auto"
        class="qrcard-proup"
      >
        <view class="code-place-container">
          <ViewCodePlateVue
            v-if="showQRcard"
            :payOrgCode="platePayOrgCode"
            :terminalNo="terminalNo"
            :qrUrl="qrUrl"
          />
          <view class="btn-class">
            <u-button shape="circle" size="medium" @click="showQRcard = false"
              >关闭</u-button
            >
          </view>
        </view>
      </u-popup>
    </view>
  </div>
</template>

<script>
  import { queryMyTerList, getChannel ,getMerchantQrCode} from '../../../http/api';
  import { debounce } from '../../../static/utils/date';
  import ViewCodePlateVue from './ViewCodePlate.vue';

  export default {
    components: {
      ViewCodePlateVue
    },
    name: 'MineTerminal',
    data() {
      return {
        payOrgCodesPicker: false,
        payOrgCodes: [],
        payOrgCode: '',
        showSelect: false,
        terStatus: [
          { text: '全部', value: '' },
          { text: '入库', value: 0 },
          { text: '下拨', value: 1 },
          { text: '绑定', value: 2 }
        ],
        actStatus: [
          { text: '全部', value: '' },
          { text: '不参与激活', value: 0 },
          { text: '已激活', value: 1 },
          { text: '未激活', value: 2 }
        ],
        initialAgent: null,
        agent: {
          agentCode: '',
          terminalNo: '',
          terStatus: '',
          actStatus: '',
          payOrgCode: '',
          pageNo: 1,
          pageSize: 20
        },
        show: false,
        teminals: [],
        total: null,
        status: 'loading',

        showQRcard: false,
        platePayOrgCode: '',
        terminalNo: '',
        qrUrl: ''
      };
    },
    watch: {
      'agent.terminalNo': debounce(function () {
        this.getTeminalList(true);
      }, 500)
    },
    computed: {
      isHasTerm() {
        return this.agent.terStatus !== '' || this.agent.actStatus !== '';
      }
    },
    onLoad() {
      this.agent.agentCode = this.$store.state.userInfo.agentCode;
      this.getTeminalList(true);
    },
    onReachBottom() {
      if (this.status == 'nomore') return;
      this.getTeminalList(false);
    },
    methods: {
      handelViewCodePlate(agentCode, terminalNo, payOrgCode) {
        this.platePayOrgCode = payOrgCode;
        this.terminalNo = terminalNo;

        getMerchantQrCode({
          terminalNo: this.terminalNo,
          payOrgCode: this.platePayOrgCode
        }).then((res) => {
          if (res.code == '00') {
            if (res.data) {
              this.qrUrl = res.data;
              this.showQRcard = true;
            } else {
              this.$u.toast('无可用于生成二维码的链接!');
            }
          }
        });
      },
      showPayOrgCodes() {
        this.payOrgCodes = [];
        getChannel().then((res) => {
          if (res.code == '00') {
            if (res.data.length) {
              this.payOrgCodes = [{ name: '全部', code: '' }, ...res.data];
              this.payOrgCodesPicker = true;
            } else {
              this.$u.toast('暂无可选支付通道！');
            }
          }
        });
      },
      payOrgCodeConfirm([{ label, value }]) {
        this.payOrgCode = label === '全部' ? '' : label;
        this.agent.payOrgCode = value;
        this.payOrgCodesPicker = false;

        this.getTeminalList(true);
      },
      getTeminalList(isInquire) {
        if (JSON.stringify(this.agent) == JSON.stringify(this.initialAgent)) {
          this.showSelect = false;
          return;
        }
        this.agent.pageNo = isInquire ? 1 : this.agent.pageNo + 1;
        this.status = 'loading';
        queryMyTerList(this.agent).then((res) => {
          if (res.code == '00') {
            this.showSelect = false;
            if (res.data.list.length != 0) {
              this.show = false;
              this.total = res.data.total;
              isInquire
                ? (this.teminals = res.data.list)
                : res.data.list.forEach((i) => {
                    this.teminals.push(i);
                  });
              isInquire &&
                uni.pageScrollTo({
                  scrollTop: 0
                });
            } else {
              this.show = true;
            }
            if (this.teminals.length >= this.total) {
              // 数据全部加载完成
              this.status = 'nomore';
            } else {
              this.status = 'loadmore';
            }
          }
        });
      },
      select() {
        this.initialAgent = JSON.parse(JSON.stringify(this.agent));
        this.showSelect = true;
      },
      cancel() {
        this.showSelect = false;
        this.agent = JSON.parse(JSON.stringify(this.initialAgent));
      },
      reset() {
        this.agent = {
          agentCode: this.$store.state.userInfo.agentCode,
          terStatus: '',
          payOrgCode: this.agent.payOrgCode,
          actStatus: '',
          pageNo: 1,
          pageSize: 20
        };
      },
      toDetail(agentCode, terminalNo, payOrgCode) {
        this.$Router.push({
          name: 'TerminalDetail',
          params: { agentCode, terminalNo, payOrgCode }
        });
      },
      toActiveDetail(id, payOrgCode) {
        this.$Router.push({ name: 'ActiveDetail', params: { id, payOrgCode } });
      },
      toSimDetail(id, payOrgCode) {
        this.$Router.push({ name: 'SimDetail', params: { id, payOrgCode } });
      }
    }
  };
</script>

<style lang="less" scoped>
  #mine-terminal {
    .filter-proup {
      /deep/ .u-btn--bold-border {
        border: none;
      }
      .filter {
        text-align: center;
        line-height: 80rpx;
        background-color: #fff;
        border-bottom: 1rpx solid #e5e5e5;
        .has-filter {
          color: #4a67d6;
        }
      }
      header {
        line-height: 100rpx;
        text-align: center;
        position: relative;
        > span {
          font-size: 32rpx;
        }
        > view {
          position: absolute;
          right: 0;
          top: 0;
          height: 100rpx;
          display: flex;
          align-items: center;
        }
      }
      div {
        padding: 0 30rpx;
        > p {
          margin: 20rpx 0;
        }
        /deep/ section {
          display: flex;
          justify-content: space-between;

          .u-size-default {
            height: 60rpx;
            font-size: 24rpx;
          }
          .active {
            border: 1px solid #5972c3;
            border-radius: 10rpx;
            .u-btn--default {
              color: tomato;
              background-color: transparent;
            }
          }
          .inactive {
            background: #f8f8f9;
            border: 0;
            border-radius: 10rpx;
            .u-btn--default {
              background-color: transparent;
            }
          }
        }
      }
      .tool-btns {
        text-align: center;
        margin: 50rpx 0;
        display: flex;
        justify-content: space-around;
        /deep/ .u-size-medium {
          padding: 0 60rpx;
          height: 66rpx;
        }
      }
    }
    > main {
      .list-data {
        padding: 0 20rpx;
        > section {
          background: white;
          padding: 30rpx;
          margin-bottom: 20rpx;
          border-radius: 14rpx;
          .info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            p {
              margin: 0;
            }
            > div {
              display: flex;
              > image {
                width: 60rpx;
                height: 60rpx;
                margin-right: 30rpx;
              }
              > p {
                > span {
                  display: block;
                  &:nth-of-type(1) {
                    margin-bottom: 10rpx;
                  }
                  &:nth-of-type(2) {
                    font-size: 24rpx;
                  }
                }
              }
            }
          }
          .tools-btn {
            display: flex;
            padding-top: 20rpx;
            margin-top: 20rpx;
            border-top: 1px solid #e5e5e5;
            color: #004ea9;
            > span {
              flex: 1;
              text-align: center;
              border-right: 1px solid #e5e5e5;
              &:last-of-type {
                border: none;
              }
            }
          }
        }
      }
    }

    .qrcard-proup {
      /deep/ .u-mode-center-box {
        border-radius: 0 !important;
        background-color: transparent;
      }
      .code-place-container {
        width: 100vw;
      }
    }
  }

  .btn-class {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 6px;
  }
</style>
