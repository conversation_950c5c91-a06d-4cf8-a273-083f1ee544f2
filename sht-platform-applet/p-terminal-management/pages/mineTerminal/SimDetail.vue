<template>
    <div>
        <main>

            <header>
                <div>
                    <span>终端号</span>
                    <text user-select selectable>{{terminalNo}}</text>
                </div>
                <div>
                    <span>终端型号</span>
                    <span>{{terminalType}}</span>
                </div>
                  <p>
                <span>支付通道</span>
                <span>{{$Route.query.payOrgCode | orgCodeFormat}}</span>
            </p>
            </header>

            <no-content v-show="show" />

            <section v-for="(i,index) in  list" :key="index">
                <p>
                    <span>缴费金额:</span>
                    <span>{{i.payAmount}}元</span>
                </p>
                <div>
                    <p>
                        <span>缴费状态:</span>
                        <span>{{payStatus[i.payStatus]}}</span>
                    </p>
                    <span v-if="i.cashBackStatus != 4  && i.payStatus == 2" @click="toEntryInfo">入账信息</span>
                </div>
                <p>
                    <span>扣费类型:</span>
                    <span>{{deductionType[i.deductionType]}}</span>
                </p>
                <p>
                    <span>扣费申请时间:</span>
                    <span>{{i.endTime ? dateFormat(i.endTime) : '--'}}</span>
                </p>
                <p>
                    <span>更新时间:</span>
                    <span>{{i.updateTime ? dateFormat(i.updateTime) : '--'}}</span>
                </p>
                <p>
                    <span>返现状态:</span>
                    <span>{{cashBackStatus[i.cashBackStatus]}}</span>
                </p>
            </section>
        </main>
    </div>
</template>

<script>
import { dateFormat } from "../../../static/utils/date.js";
import { getTerSimDetail } from '../../../http/api'

export default {
    data() {
        return {
            terminalNo: '',
            terminalType: '',
            list: [],
            show: false,
            deductionType: ['首次', '循环'],
            payStatus: ['未缴费', '缴费中', '缴费成功', '缴费失败'],
            cashBackStatus: ['未返现', '返现中', '返现成功', '返现失败', '无需返现']
        };
    },
    onLoad() {
        this.getListData()
    },
    methods: {
        async getListData() {
            const { data } = await getTerSimDetail(this.$Route.query.id)
            this.list = data.list || []
            this.show = !this.list.length

            const { terminalNo, terminalType } = data
            this.terminalNo = terminalNo
            this.terminalType = terminalType
        },
        toEntryInfo() {
            this.$Router.push({ name: 'EntryInfo', params: { id: this.$Route.query.id } })
        },
        dateFormat
    }
};
</script>

<style lang="scss" scoped>
main {
    min-height: 100vh;
    padding: 20rpx 20rpx 0;
    background-color: #f3f5f7;
    > header {
        padding: 0 30rpx;
        margin-bottom: 20rpx;
        border-radius: 10rpx;
        background-color: #8daabb;
        color: #ffffff;
        > div {
            display: flex;
            padding: 30rpx 0;
            > span {
                &:first-child {
                    font-weight: bold;
                }
            }
            &:first-of-type {
                flex-direction: column;
                border-bottom: 2rpx solid #ffffff;
                > span {
                    &:last-child {
                        margin-top: 8rpx;
                    }
                }
            }
            &:last-of-type {
                justify-content: space-between;
            }
        }
    }
    > section {
        padding: 30rpx;
        margin-bottom: 20rpx;
        background-color: #fff;
        border-radius: 16rpx;
        p {
            margin: 0;
            span {
                &:first-of-type {
                    display: inline-block;
                    min-width: calc(6em + 30rpx);
                    color: #8799a3;
                }
            }
            &:not(:last-of-type) {
                margin-bottom: 10rpx;
            }
        }
        > div {
            display: flex;
            margin-bottom: 10rpx;
            > p {
                flex: 1;
            }
            > span {
                color: #004ea9;
            }
        }
    }
}
</style>
