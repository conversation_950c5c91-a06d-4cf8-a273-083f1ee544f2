<template>
  <div id="terminalSim">
    <main>
      <u-field
        label="支付通道"
        input-align="right"
        placeholder="选择支付通道"
        :value="payOrgCodeText"
        @click="showPayOrgCodes"
        :right-icon="`arrow-${payOrgCodesPicker ? 'up' : 'down'}`"
        disabled
      />
      <u-select
        v-model="payOrgCodesPicker"
        :list="payOrgCodes"
        label-name="name"
        value-name="code"
        @confirm="payOrgCodeConfirm"
        :default-value="payOrgCodeText ? [payOrgCodes.findIndex(i => i.name === payOrgCodeText)] : []"
      />
      <u-search
        v-model="inputTerminalNo"
        :show-action="true"
        action-text="搜索"
        placeholder="输入终端号查询达标情况"
        height="70"
        bg-color="#f3f5f7"
        :action-style="{ color: '#004ea9' }"
        @custom="onSearch"
        @search="onSearch"
      />

      <section>
        <div v-show="info.hasOwnProperty('custId')" class="base-info">
          <p>
            商户名称 <span>{{ info.custName || '-' }}</span>
          </p>
          <p>
            商户号 <span>{{ info.custId || '-' }}</span>
          </p>
          <p>
            达标计算方式 <span>{{ info.cashCalculateType === 1 ? '时间优先达标' : '金额优先达标' }}</span>
          </p>
          <p>
            达标累计金额 <span>{{ info.reachTotalAmt }}</span>
          </p>
          <p>
            达标交易类型 <span>{{ formatTransactionType(info.transactionType) }}</span>
          </p>
        </div>
        <div class="rules">
          <section v-for="(i, key) in info.reachRuleList || []" :key="key">
            <p>
              规则级别 <span>{{ i.thisLevel ? '我的' : '服务商' }}</span>
            </p>
            <p>
              代理商编号 <span>{{ i.agentCode }}</span>
            </p>
            <p>
              达标标准(元) <span>{{ i.reachStandAmt }}</span>
            </p>
            <p>
              活动天数(天) <span>{{ i.activityDays }}</span>
            </p>
            <p>
              返现标准(元) <span>{{ i.cashStandAmt }}</span>
            </p>
            <p>
              返现金额(未扣服务费率) <span>{{ i.cashAmt }}</span>
            </p>
            <p>
              达标状态
              <span :style="[{ color: `${statusColor[i.reachStandardStatus]}` }]">{{
                reachStandardStatus[i.reachStandardStatus] || '-'
              }}</span>
            </p>
            <p>
              返现状态 <span :style="[{ color: `${i.cashState ? 'green' : '#666'}` }]">{{ i.cashState ? '已返现' : '待返现' }}</span>
            </p>
            <p>
              序号 <span>{{ i.ruleType }}</span>
            </p>
          </section>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import { querySnReachDetail, getChannel } from '../../http/api';
export default {
  data() {
    return {
      info: {},
      payOrgCodesPicker: false,
      payOrgCodes: [],
      payOrgCode: '',
      payOrgCodeText: '',
      inputTerminalNo: '',
      transactionType: {
        借记卡: 0,
        信用卡: 1,
        云闪付: 2,
        扫码: 3,
        借记卡封顶: 4,
        信用卡特惠: 5,
        银联扫码: 6,
        信用卡秒到: 7,
        '无卡(贷记)': 8,
        '无卡(借记)': 9,
        '支付宝大额': 10,
      },
      reachStandardStatus: ['未达标', '已达标', '不再达标'],
      statusColor: ['#666', 'green', 'brown']
    };
  },
  methods: {
    showPayOrgCodes() {
      this.payOrgCodes = [];
      getChannel().then(res => {
        if (res.code == '00') {
          if (res.data.length != 0) {
            this.payOrgCodes = res.data;
            this.payOrgCodesPicker = true;
          } else {
            this.$u.toast('暂无可选支付通道！');
          }
        }
      });
    },
    payOrgCodeConfirm([{ label, value }]) {
      this.payOrgCodeText = label;
      this.payOrgCode = value;
      this.inputTerminalNo = '';
      this.payOrgCodesPicker = false;
    },
    async onSearch(value) {
      if (!this.payOrgCode) return this.$u.toast('请选择支付通道！');
      if (!value) return this.$u.toast('请输入终端号！');

      const { data = {} } = await querySnReachDetail(this.payOrgCode, value);
      this.info = data;
    },
    formatTransactionType(val) {
      if (!val) return '-';
      const arrType = val.split(',');
      arrType.forEach((a, index) => {
        Object.keys(this.transactionType).forEach(t => {
          if (this.transactionType[t] == a) {
            arrType[index] = t;
          }
        });
      });
      return arrType.join(',');
    }
  }
};
</script>

<style lang="less" scoped>
#terminalSim {
  min-height: 100%;
  background-color: #f3f5f7;
  > main {
    > section {
      margin: 30rpx;
      .base-info {
        padding: 30rpx;
        border-radius: 16rpx;
        background-color: #fff;
        > p {
          display: flex;
          justify-content: space-between;
          margin: 0;
          font-weight: bolder;
          color: #333;
          > span {
            width: calc(100% - 6em - 30rpx);
            font-weight: normal;
            color: #666;
          }
          & + p {
            margin-top: 20rpx;
          }
        }
      }
      .rules {
        padding: 30rpx;
        > section {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          &:not(:last-of-type) {
            margin-bottom: 30rpx;
            border-bottom: 2rpx dashed rgb(177, 176, 176);
          }
          > p {
            display: flex;
            flex-direction: column;
            width: calc(50% - 30rpx);
            margin: 0 0 30rpx 0;
            font-size: 24rpx;
            font-weight: 500;
            word-break: break-all;
            > span {
              margin-top: 8rpx;
              font-weight: normal;
              color: #666;
            }
          }
        }
      }
    }
  }
}
</style>
