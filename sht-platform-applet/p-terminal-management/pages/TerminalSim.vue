<template>
    <div id="terminalSim">
        <main>
            <u-field label='支付通道' input-align="right" placeholder="选择支付通道" :value="payOrgCodeText" @click="showPayOrgCodes" :right-icon="`arrow-${payOrgCodesPicker ? 'up' : 'down'}`" disabled />
            <u-select v-model="payOrgCodesPicker" :list="payOrgCodes" label-name="name" value-name="code" @confirm="payOrgCodeConfirm" :default-value=" payOrgCodeText ?[payOrgCodes.findIndex(i=>i.name === payOrgCodeText)] : []" />
            <u-search v-model="inputTerminalNo" :show-action="true" action-text="搜索" placeholder='输入终端号查询流量卡' height="70" bg-color="#f3f5f7" :action-style="{'color':'#004ea9'}" @custom="onSearch" @search="onSearch" />

            <section v-if="simNo">
                <p>
                    <span >终端号</span>
                    <text user-select selectable>{{terminalNo}}</text>
                </p>
                <p>
                    <span>流量卡号</span>
                    <span>{{simNo}}</span>
                </p>
            </section>
            <p v-if="showEmpty"> 未查询到相关信息 </p>
        </main>
    </div>
</template>

<script>
import { getTerSimNoDetail, getChannel } from '../../http/api'
export default {
    data() {
        return {
            payOrgCodesPicker: false,
            payOrgCodes: [],
            payOrgCode: "",
            payOrgCodeText: "",
            inputTerminalNo: '',
            terminalNo: '',
            simNo: '',
            showEmpty: false
        };
    },
    methods: {
        showPayOrgCodes() {
            this.payOrgCodes = [];
            getChannel().then((res) => {
                if (res.code == "00") {
                    if (res.data.length != 0) {
                        this.payOrgCodes = res.data
                        this.payOrgCodesPicker = true;
                    } else {
                        this.$u.toast("暂无可选支付通道！");
                    }
                }
            });
        },
        payOrgCodeConfirm([{ label, value }]) {
            this.payOrgCodeText = label
            this.payOrgCode = value
            this.payOrgCodesPicker = false
        },
        async onSearch(value) {
            if (!this.payOrgCode) return this.$u.toast('请选择支付通道！')
            if (!value) return this.$u.toast('请输入终端号！')

            const { data } = await getTerSimNoDetail({ payOrgCode: this.payOrgCode, terminalNo: value })
            const { terminalNo = '', simNo = '' } = data
            this.terminalNo = terminalNo
            this.simNo = simNo
            this.showEmpty = !simNo
        }
    }
};
</script>

<style lang="less" scoped>
#terminalSim {
    height: 100%;
    background-color: #fff;
    > main {
        padding: 15px;
        > section {
            margin: 30px 0;
            background-color: #f3f5f7;
            border-radius: 8px;
            > p {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 15px;
                margin: 0;
                > span {
                    &:first-child {
                        font-weight: bolder;
                        color: #666;
                    }
                }
            }
        }
        > p {
            margin: 30px;
            text-align: center;
            color: #888;
        }
    }
}
</style>
