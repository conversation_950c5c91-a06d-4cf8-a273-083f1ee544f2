<template>
    <div id="agent-retrace">
        <main>
            <u-sticky>
                <div class="search-bar">
                    <view class="content">
                        <u-field :label-width="40" icon="search" :border-bottom="false" type="text" placeholder="输入代理商名称" v-model="agentName" />
                    </view>
                </div>
            </u-sticky>

            <no-content v-show="show" />
            <section v-show="!show">
                <p v-for="(a, index) in agents" :key="index" @click="retrace(a.agentCode)">
                    <span>{{ a.name }}</span>
                    <span>{{ a.agentCode }}</span>
                </p>
            </section>
        </main>
    </div>
</template>

<script>
import { getDirRealAgent } from '../../http/api';
import { debounce } from '../../static/utils/date';

export default {
    name: 'AgentRetrace',
    data() {
        return {
            agentName: '',
            show: false,
            agents: []
        }
    },
    onLoad() {
        this.getAgentList();
    },
    watch: {
        agentName: debounce(function () {
            this.getAgentList();
        }, 500)
    },
    methods: {
        getAgentList() {
            getDirRealAgent(this.agentName).then(res => {
                if (res.code == '00') {
                    if (res.data.length != 0) {
                        this.show = false;
                        this.agents = res.data;
                    } else {
                        this.show = true;
                    }
                }
            })
        },
        retrace(agentCode) {
            this.$Router.push({ name: 'Retrace', params: { agentCode } });
        }
    }
}
</script>

<style lang="less" scoped>
#agent-retrace {
    > main {
        > section {
            > p {
                height: 80rpx;
                line-height: 80rpx;
                background-color: white;
                border-bottom: solid 1rpx #f4f4f5;
                padding-left: 60rpx;
                margin: 0;
                > span {
                    margin-right: 40rpx;
                }
            }
        }
    }
}
</style>