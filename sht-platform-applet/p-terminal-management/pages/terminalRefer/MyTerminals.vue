<template>
    <div id="my-terminals">
        <main>
            <header>
                <p>
                    <span>我的终端数</span>
                    <span>{{ myData.totalTerCount }}</span>
                </p>
                <p>
                    <span>激活率</span>
                    <span>{{ myData.totalActRatio }}%</span>
                </p>
                <p>
                    <span>已划拨终端</span>
                    <span>{{ myData.allowedTerCount }}</span>
                </p>
            </header>
            <section>
                <p>代理商</p>
                <p>终端台数</p>
                <p>已绑定</p>
                <p>参与激活</p>
                <p>已激活</p>
                <p>激活率</p>
                <p></p>
            </section>
            <view class="list-data">
                <section @click="teminalLists(agentCode, '')">
                    <p>我的</p>
                    <p>{{ myData.terCount }}</p>
                    <p>{{ myData.terBindCount }}</p>
                    <p>{{ myData.joinActivateCount }}</p>
                    <p>{{ myData.terActivcateCount }}</p>
                    <p>{{ myData.activateRatio }}%</p>
                    <p>
                        <u-icon name="arrow-right" color="#999" />
                    </p>
                </section>
                <section v-for="(a, index) in agentsData" :key="index" @click="teminalLists(a.agentCode, a.agentName)">
                    <p>{{ a.agentName }}</p>
                    <p>{{ a.terCount }}</p>
                    <p>{{ a.terBindCount }}</p>
                    <p>{{ a.joinActivateCount }}</p>
                    <p>{{ a.terActivcateCount }}</p>
                    <p>{{ a.activateRatio }}%</p>
                    <p>
                        <u-icon name="arrow-right" color="#999" />
                    </p>
                </section>
            </view>

            <u-loadmore :status="status" @loadmore="loadmore" />
        </main>
    </div>
</template>

<script>
import { queryMyTerDetails, queryMyAgentTerSummary } from '../../../http/api'

export default {
    name: "MyTerminals",
    data() {
        return {
            myData: {
                totalTerCount: '',        //我的总终端数
                totalActRatio: '',         //我的总激活率
                allowedTerCount: '',        //我的总划拨数量
                terCount: '',         //本级终端数量
                terBindCount: '',     //本级已绑定终端数量
                joinActivateCount: '',     //本级参与激活数量
                terActivcateCount: '',   //本级已激活终端数量
                activateRatio: ''   //本级激活率
            },
            agentsData: [],
            total: 0,
            status: 'loading'

        }
    },
    computed: {
        agentCode() {
            return this.$store.state.userInfo.agentCode
        }
    },
    onLoad() {
        this.getData();
    },
    onReachBottom() {
        this.loadmore()
    },
    methods: {
        getData() {
            queryMyTerDetails().then(res => {
                if (res.code == '00') {
                    this.myData = {
                        totalTerCount: res.data.totalTerCount,
                        totalActRatio: res.data.totalActRatio,
                        allowedTerCount: res.data.allowedTerCount,
                        terCount: res.data.terCount,
                        terBindCount: res.data.terBindCount,
                        joinActivateCount: res.data.joinActivateCount,
                        terActivcateCount: res.data.terActivcateCount,
                        activateRatio: res.data.activateRatio
                    }
                }
            })
            queryMyAgentTerSummary({ pageNo: 1, pageSize: 10 }).then(res => {
                if (res.code == '00') {
                    this.agentsData = res.data.list;
                    this.total = res.data.total;
                    if (this.agentsData.length >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                }
            })
        },
        loadmore() {
            if (this.status == 'nomore') return;
            this.status = 'loading'
            queryMyAgentTerSummary({ pageNo: this.agentsData.length / 10 + 1, pageSize: 10 }).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.total;
                    res.data.list.forEach((i) => {
                        this.agentsData.push(i);
                    })
                }
                if (this.agentsData.length >= this.total) {
                    // 数据全部加载完成
                    this.status = 'nomore';

                } else {
                    this.status = 'loadmore';
                }
            })
        },
        teminalLists(agentCode, agentName) {
            this.$Router.push({ name: 'TeminalLists', params: { agentCode, agentName } });
        }
    }
}
</script>

<style lang="less" scoped>
#my-terminals {
    > main {
        padding-top: 20rpx;
        > header {
            height: 160rpx;
            border-radius: 10rpx;
            background-color: white;
            margin: 0 20rpx 40rpx;
            display: flex;
            align-items: center;
            justify-content: space-around;
            > p {
                text-align: center;
                > span {
                    display: block;
                    &:nth-of-type(1) {
                        color: #909090;
                        margin-bottom: 40rpx;
                    }
                    &:nth-of-type(2) {
                        font-size: 36rpx;
                    }
                }
            }
        }
        .list-data {
            margin-top: 20rpx;
        }
        section {
            height: 120rpx;
            background-color: white;
            border-bottom: solid 2rpx #f3f3f3;
            display: flex;
            justify-content: space-between;
            align-items: center;
            &:nth-of-type(1) {
                margin-bottom: 20rpx;
            }
            > p {
                text-align: center;
                &:nth-of-type(1) {
                    width: 25%;
                    text-align: left;
                    padding-left: 20rpx;
                }
                &:nth-of-type(2),
                &:nth-of-type(4) {
                    width: 16%;
                }
                &:nth-of-type(3),
                &:nth-of-type(5),
                &:nth-of-type(6) {
                    width: 13%;
                }
                &:nth-of-type(7) {
                    width: 5%;
                    text-align: left;
                    > img {
                        width: 20rpx;
                        height: 20rpx;
                    }
                }
            }
        }
    }
}
</style>