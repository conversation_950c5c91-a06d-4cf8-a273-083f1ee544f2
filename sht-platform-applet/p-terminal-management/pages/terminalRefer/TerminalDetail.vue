<template>
    <div id="terminalDetail">
        <main>
            <div>
                <p>
                    <span>支付通道</span>
                    <span>{{$Route.query.payOrgCode | orgCodeFormat}}</span>
                </p>
                <p>
                    <span>终端型号</span>
                    <span>{{ terminalDetail.model }}</span>
                </p>
                <p>
                    <span>终端号</span>
                    <span><text  user-select selectable>{{ terminalDetail.terminalNo }}</text></span>
                </p>
                <p>
                    <span>终端状态</span>
                    <span>{{ terminalDetail.status == "0" ? "入库" : terminalDetail.status == "1" ? "下拨" : "绑定" }}</span>
                </p>
                <p>
                    <span>代理商编号</span>
                    <span>{{ terminalDetail.agentCode }}</span>
                </p>
                <p v-if="terminalDetail.merchantCode != null">
                    <span>商户编号</span>
                    <span><text user-select selectable>{{ terminalDetail.merchantCode }}</text></span>
                </p>
            </div>
            <div>
                <p>
                    <span>入库时间</span>
                    <span>{{ terminalDetail.storageTime }}</span>
                </p>
                <p>
                    <span>出库时间</span>
                    <span>{{ terminalDetail.deliveryTime }}</span>
                </p>
            </div>
            <div v-show="terminalDetail.simRate != null || terminalDetail.cycleRate != null">
                <p>sim卡返现规则</p>
                <p v-if="terminalDetail.simRate != null">
                    <span>sim卡首次返现比例</span>
                    <span>{{ terminalDetail.simRate }}</span>
                </p>
                <p v-if="terminalDetail.cycleRate != null">
                    <span>sim卡循环返现比例</span>
                    <span>{{ terminalDetail.cycleRate }}</span>
                </p>
            </div>
            <div style="margin-bottom: 0" v-show=" terminalDetail.frozenAmount != null || terminalDetail.activationCashBackAmount != null || terminalDetail.serviceFee != null">
                <p>激活返现规则</p>
                <p>
                    <span>激活标准</span>
                    <span>{{ terminalDetail.frozenAmount | toDecimal2 }}元</span>
                </p>
                <p>
                    <span>激活返现金额</span>
                    <span>{{ terminalDetail.activationCashBackAmount | toDecimal2 }}元</span>
                </p>
                <p>
                    <span>冻结服务费</span>
                    <span>{{ terminalDetail.serviceFee | toDecimal2 }}元</span>
                </p>
            </div>
            <div v-if="terminalDetail.ruleList.length">
                <p>达标返现规则</p>
                <section v-for="(r, index) in terminalDetail.ruleList" :key="index">
                    <p>规则{{ r.ruleNo }}</p>
                    <p>
                        <span>达标交易金额</span>
                        <span>{{ r.standardTransactionAmount | toDecimal2 }}元</span>
                    </p>
                    <p>
                        <span>达标返现金额</span>
                        <span>{{ r.standardCashBackAmount | toDecimal2 }}元</span>
                    </p>
                    <p>
                        <span>活动天数</span>
                        <span>{{ r.activeDays }}天</span>
                    </p>
                </section>
            </div>
        </main>
    </div>
</template>

<script>
import { getTerminalDetail } from "../../../http/api";
import { toDecimal2 } from '../../../static/utils/date';

export default {
    name: "TerminalDetail",
    filters: {
        toDecimal2
    },
    data() {
        return {
            terminalDetail: {
                model: "",
                terminalNo: "",
                status: 0,
                agentCode: "",
                merchantCode: "",
                simRate: "",
                storageTime: "",
                deliveryTime: "",
                frozenAmount: "",
                activationCashBackAmount: "",
                serviceFee: "",
                ruleList: [],
            },
        };
    },
    onLoad() {
        getTerminalDetail({
            agentCode: this.$Route.query.agentCode,
            terminalNo: this.$Route.query.terminalNo,
            payOrgCode: this.$Route.query.payOrgCode,
        }).then((res) => {
            if (res.code == "00") {
                this.terminalDetail = res.data;
            }
        });
    },
};
</script>

<style lang="less" scoped>
#terminalDetail {
    height: 100%;
    padding-top: 20rpx;
    main {
        min-height: 100%;
        > div {
            background-color: white;
            padding-top: 0.2rpx;
            margin-bottom: 20rpx;
            > p,
            > div > p,
            section > p {
                display: flex;
                color: #545556;
                font-size: 0.9rem;
                border-bottom: 1px solid #e5e5e5;
                margin: 0 30rpx;
                padding: 30rpx 0;
                &:last-of-type {
                    border: 0;
                }
                > span {
                    &:nth-of-type(1) {
                        width: 30%;
                        color: #666666;
                    }
                    &:nth-of-type(2) {
                        flex: 1;
                        color: #000;
                    }
                }
            }
            > div {
                border-top: 1px solid #e5e5e5;
                padding-top: 20rpx;
            }
            &:nth-of-type(5) {
                padding-bottom: 20rpx;
                margin-bottom: 0;
                > p {
                    padding: 20rpx 30rpx;
                    background: #f3f5f7;
                    margin: 0;
                }
                > section {
                    margin: 20rpx 30rpx;
                    margin-bottom: 0;
                    background: #f3f5f7;
                    border-radius: 20rpx;
                }
            }
        }
    }
}
</style>