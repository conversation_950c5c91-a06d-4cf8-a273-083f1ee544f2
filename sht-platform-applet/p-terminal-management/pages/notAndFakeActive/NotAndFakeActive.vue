<template>
    <div id="notAndFakeActive">
        <main>
            <u-sticky>
                <view class="top-search">
                    <!-- <u-field label='支付通道' input-align="right" :border-bottom="false" placeholder="选择支付通道" :value="payOrgCode" @click="showPayOrgCodes" :right-icon="`arrow-${payOrgCodesPicker ? 'up' : 'down'}`" disabled />
                    <u-select v-model="payOrgCodesPicker" :list="payOrgCodes" label-name="name" value-name="code" @confirm="payOrgCodeConfirm" :default-value="payOrgCode ? [payOrgCodes.findIndex(i=>i.name === payOrgCode)] : []" /> -->
                    <section class="searchBar">
                        <u-icon name="search" size="36" />
                        <view>
                            <u-field :border-bottom="false" placeholder="请输入服务商名称" v-model="ajaxParams.agentName" />
                        </view>
                    </section>
                </view>
            </u-sticky>

            <no-content v-if="show" />
            <section class="listData" v-if="!show">
                <section v-for="(i,index) in listData" :key="index" @click="toDetail(i)">
                    <div>
                        <image :src="i.agentType == 0 ? thisLevelIcon : nextLevelIcon" alt="" />
                    </div>
                    <div>
                        <div>
                            <p><strong>{{i.agentName || i.agentCode}}</strong> </p>
                            <span :class="'agentType'+i.agentType">{{i.agentType == 0 ? "我的" :"服务商"}}</span>
                        </div>
                        <div>
                            <p> <span>未激活: {{i.unActiviteCount}}台</span></p>
                            <p> <span>伪激活: {{i.activiteCount}}台</span></p>
                        </div>
                    </div>
                </section>
            </section>

            <u-loadmore v-if="!show" :status="status" @loadmore="loadmore" />
        </main>
    </div>
</template>

<script>
import { debounce } from "@/static/utils/date";
import { getCurentAgentActiviDetail, getAgentActiviList, getChannel } from '../../../http/api';

export default {
    name: 'NotAndFakeActive',
    data() {
        return {
            payOrgCodesPicker: false,
            payOrgCodes: [],
            // payOrgCode: "",
            status: 'loading',
            total: null,
            show: false,
            thisLevelIcon: require('../../static/images/thisLevel.png'),
            nextLevelIcon: require('../../static/images/subordinate.png'),
            agentCodeOrName: '',
            curentAgentDetail: null,
            listData: [],
            ajaxParams: {
                payOrgCode: "",
                agentName: '', //代理商编号或代理商名称
                pageNo: 1, //当前页
                pageSize: 10 //条数
            }
        };
    },
    onLoad() {
        this.getPageData();
        this.$watch(
            "ajaxParams.agentName",
            debounce(function () {
                this.getPageData()
            }, 700)
        );
    },
    onReachBottom() {
        this.loadmore()
    },
    methods: {
        showPayOrgCodes() {
            this.payOrgCodes = [];
            getChannel().then((res) => {
                if (res.code == "00") {
                    if (res.data.length) {
                        this.payOrgCodes = [{ name: "全部", code: '' }, ...res.data]
                        this.payOrgCodesPicker = true;
                    } else {
                        this.$u.toast("暂无可选支付通道！");
                    }
                }
            });
        },
        payOrgCodeConfirm([{ label, value }]) {
            this.payOrgCode = label === '全部' ? '' : label
            this.ajaxParams.payOrgCode = value
            this.payOrgCodesPicker = false

            this.getPageData()
        },
        toDetail(item) {
            this.$Router.push({ name: 'NotAndFakeActiveDetail', params: item })
        },
        async getPageData() {
            await getCurentAgentActiviDetail(this.ajaxParams.payOrgCode).then(res => {
                if (res.code == "00") {
                    this.curentAgentDetail = res.data;
                }
            })
            this.status = 'loading'
            this.ajaxParams.pageNo = 1;
            getAgentActiviList(this.ajaxParams).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.total;
                    this.listData = res.data.list || [];
                    if (this.ajaxParams.agentName == '') {
                        this.curentAgentDetail && this.listData.unshift(this.curentAgentDetail);
                    }
                    uni.pageScrollTo({
                        scrollTop: 0
                    });
                    if (this.listData.length == 0) {
                        this.show = true;
                    } else {
                        this.show = false;
                    }
                    if (((this.curentAgentDetail && this.ajaxParams.agentName == '') ? this.listData.length - 1 : this.listData.length) >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }

                }
            });
        },
        loadmore() {
            if (this.status == 'nomore') return;
            this.status = 'loading'
            this.ajaxParams.pageNo = this.ajaxParams.pageNo + 1;
            getAgentActiviList(this.ajaxParams).then((res) => {
                if (res.code == "00") {
                    this.total = res.data.total;
                    res.data.list.forEach((i) => {
                        this.listData.push(i);
                    });
                    this.loading = false;
                    if (((this.curentAgentDetail && this.ajaxParams.agentName == '') ? this.listData.length - 1 : this.listData.length) >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                }
            });
        },

    }
};
</script>

<style lang="less" scoped>
#notAndFakeActive {
    > main {
        min-height: 100%;
        .top-search {
            padding:20rpx 30rpx;
            background-color: #f3f5f7;
        }
        .searchBar {
            display: flex;
            align-items: center;
            height: 70rpx;
            padding: 0 30rpx;
            background-color: #fff;
            border-radius: 60rpx;
            > view {
                flex: 1;
            }
            /deep/ .u-label {
                display: none;
            }
            /deep/ .u-field {
                padding: 28rpx 20rpx 28rpx 10rpx;
            }
        }
        .listData {
            padding: 0 30rpx;
            > section {
                display: flex;
                align-items: center;
                padding: 30rpx;
                margin-bottom: 24rpx;
                border-radius: 14rpx;
                background-color: #fff;
                box-shadow: 0 2rpx 10rpx 0 rgba(0, 0, 0, 0.1);
                > div {
                    display: flex;
                    flex-direction: column;
                    &:first-of-type {
                        align-items: center;
                        color: #666;
                        > image {
                            width: 84rpx;
                            height: 84rpx;
                            border-radius: 50%;
                        }
                    }
                    &:last-of-type {
                        flex: 1;
                        margin-left: 30rpx;
                        > div {
                            display: flex;
                            justify-content: space-between;
                            align-items: flex-start;
                            > p {
                                margin: 0;
                                > span {
                                    font-size: 26rpx;
                                    color: rgb(218, 143, 4);
                                }
                            }
                            > span {
                                flex-shrink: 0;
                                margin-left: 10rpx;
                                padding: 4rpx 20rpx;
                                color: #333;
                                background-color: #f3f5f7;
                                border-radius: 8rpx;
                                font-size: 26rpx;
                            }
                            .agentType0 {
                                color: rgb(15, 128, 207);
                            }
                            .agentType1 {
                                color: rgb(5, 56, 155);
                            }

                            &:last-of-type {
                                margin-top: 14rpx;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
