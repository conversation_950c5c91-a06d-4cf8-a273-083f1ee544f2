<template>
    <div id="banner-advertisement">
        <main>
            <image :src="src" mode="widthFix" />
        </main>
    </div>
</template>

<script>
export default {
    name: 'BannerAdvertisement',
    data() {
        return {
            title: ['合规展业', '远离非法套现', '远离洗钱', '警惕“人头费”'],
            src: ''
        }
    },
    computed: {

    },
    onReady() {
        const index = Number(this.$Route.query.index) + 1
        this.src = require('../static/images/notice-' + index + '.png')

        uni.setNavigationBarTitle({ title: this.title[this.$Route.query.index] });
    }
}
</script>

<style lang="less">
#banner-advertisement {
    main {
        min-height: 100vh;
        font-size: 0;
        >image{
            width: 100%;
        }
    }
}
</style>