<template>
    <div id="merchantsAccessNetwork">
        <main>
            <div class="content">
                <view>
                    <u-field disabled :border-bottom="false" label="渠道" placeholder="请选择渠道" right-icon="arrow-down-fill" :value="channel" @click="showChannels" />
                    <u-select v-model="channelsPicker" mode="single-column" :list="channels" value-name="val" label-name="val" @confirm="channelConfirm" />

                    <u-field disabled :border-bottom="false" label="名称" placeholder="请选择费率名称" right-icon="arrow-down-fill" :value="name" @click="showNames" />
                    <u-select v-model="namesPicker" mode="single-column" :list="names" value-name="val" label-name="val" @confirm="nameConfirm" />
                </view>

                <section v-if="showQr">
                    <h2>商户注册</h2>
                    <div>
                        <create-qrcode ref="qrcode" :val="qrUrl" :size="320" usingComponents />
                    </div>
                </section>
            </div>
            <image class="bg" src="../../static/images/home/<USER>" mode="scaleToFill" />
        </main>
    </div>
</template>

<script>
import { getChannel, getMerchantsUrlList, } from "../../http/api";
import CreateQrcode from "tki-qrcode"

export default {
    name: "MerchantsAccessNetwork",
    components: {
        CreateQrcode,
    },
    data() {
        return {
            channelsPicker: false,
            channels: [],
            channelCodes: [],
            channel: "",
            namesPicker: false,
            names: [],
            urls: [],
            name: "",
            showQr: false,
            qrUrl: "",
        };
    },
    methods: {
        showChannels() {
            this.channels = [];
            this.channelCodes = [];
            getChannel().then((res) => {
                if (res.code == "00") {
                    if (res.data.length != 0) {
                        res.data.forEach((i) => {
                            this.channels.push({ val: i.name });
                            this.channelCodes.push(i.code);
                        });
                        this.channelsPicker = true;
                    } else {
                        uni.showToast({
                            title: '暂无可选渠道！',
                            icon: 'none'
                        });
                    }
                }
            });
        },
        channelConfirm(val) {
            this.channel = val[0].value;
            this.channelsPicker = false;
        },
        showNames() {
            this.names = [];
            this.urls = [];
            if (this.channel != "") {
                getMerchantsUrlList(
                    this.channelCodes[this.channels.findIndex(i => i.val == this.channel)]
                ).then((res) => {
                    if (res.code == "00") {
                        if (res.data.length != 0) {
                            res.data.forEach((i) => {
                                this.names.push({ val: i.name });
                                this.urls.push(i.url);
                            });
                            this.namesPicker = true;
                        } else {
                            uni.showToast({
                                title: '该渠道暂无可选费率！',
                                icon: 'none'
                            });
                        }
                    }
                });
            } else {
                uni.showToast({
                    title: '请先选择渠道！',
                    icon: 'none'
                });
            }
        },
        nameConfirm(val) {
            this.name = val[0].value;
            this.qrUrl = this.urls[this.names.findIndex(i => i.val == this.name)];
            this.namesPicker = false
            this.showQr = true
            this.$nextTick(() => {
                this.$refs.qrcode._makeCode()
            })
        },
    },
};
</script>

<style lang="less" scoped>
#merchantsAccessNetwork {
    height: 100%;
    > main {
        height: 100%;
        display: flex;
        align-content: center;
        justify-content: center;
        .bg {
            position: absolute;
            z-index: -1;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .content {
            position: relative;
            flex: 1;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 10rpx;
            margin: 120rpx 30rpx;
            padding: 0 30rpx;
            display: flex;
            flex-direction: column;

            /deep/ .u-field {
                margin-top: 30rpx;
                border-radius: 8rpx;
                background-color: #fff;
            }
            > section {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                text-align: center;
                > div {
                    background: #fff;
                    border-radius: 20rpx;
                    width: 350rpx;
                    height: 350rpx;
                    /deep/ .tki-qrcode {
                        image {
                            margin-top: 16rpx;
                        }
                    }
                }
                > h2 {
                    letter-spacing: 4rpx;
                    font-weight: 500;
                    font-size: 1.3rem;
                    color: #fff;
                }
            }
        }
    }
}
</style>