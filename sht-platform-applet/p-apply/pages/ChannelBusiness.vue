<template>
    <div id="channelBusiness">
        <u-cell-group :border="false">
            <u-cell-item title="商户快速入网" v-if="menuStatus.merFastAccess" @click="toLink('MerchantsAccessNetwork')" />
            <u-cell-item title="商户APP下载" v-if="menuStatus.merAppDownload" @click="toLink('MerchantsAppDownload')" />
            <u-cell-item title="商户费率修改" v-if="menuStatus.merRateEdit" @click="toLink('ChangeMerchantRate')" />
            <u-cell-item title="商户链接拓展" v-if="menuStatus.merLinkExpand" @click="toLink('MerchantsUrlExpand')" />
            <!-- <u-cell-item title="商户押金返还明细" v-if="menuStatus.merDepositRefund" @click="toLink('MerchantsDepRetDetails',{payChannelNo})" />
            <u-cell-item title="渠道提现" v-if="menuStatus.channelMerWithdraw" @click="toLink('ChannelWithdrawal',{payChannelNo})" /> -->
        </u-cell-group>
    </div>
</template>

<script>
import { getChannelCode, getMenuViewStatus } from "../../http/api";
export default {
    name: "ChannelBusiness",
    data() {
        return {
            payChannelNo: "",
            menuStatus: {
                merFastAccess: false, //商户快速入网 
                merAppDownload: false, //商户App下载 
                merRateEdit: false, //商户费率修改 
                merLinkExpand: false, //商户链接拓展 
                merDepositRefund: false, //商户押金返还明细 
                channelMerWithdraw: false, //渠道提现 
            }
        };
    },
    onLoad() {
        getMenuViewStatus().then(res => {
            this.menuStatus = Object.assign(this.menuStatus, res.data || this.menuStatus)
        })

        // if (this.$store.state.userInfo.agentLevel == 1) {
        //     getChannelCode().then((res) => {
        //         if (res.code == "00") {
        //             if (res.data != null) {
        //                 this.payChannelNo = res.data;
        //             }
        //         }
        //     });
        // }
    },
    methods: {
        toLink(name, params = {}) {
            this.$Router.push({ name, params });
        }
    },
};
</script>

<style lang="less" scoped>
#channelBusiness {
    padding-top: 20rpx;
}
</style>