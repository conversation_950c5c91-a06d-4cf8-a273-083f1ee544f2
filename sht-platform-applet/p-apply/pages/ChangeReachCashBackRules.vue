<template>
    <div id="changeReachCashBackRules" :style="{'padding-bottom': (showButton ? 100 : 0) + 'rpx' }">
        <u-form :model="terminal" ref="uForm" label-width="auto" class="u-form">
            <u-form-item label="终端号" prop="terminalNo">
                <u-input placeholder="请输入终端号" v-model="terminal.terminalNo" />
            </u-form-item>
            <u-form-item label="支付通道" prop="payOrgCode">
                <u-input type="select" :select-open="payOrgCodesPicker" placeholder="请选择支付通道" :value="payOrgCode" @click="showPayOrgCodes" />
            </u-form-item>
            <u-select v-model="payOrgCodesPicker" :list="payOrgCodes" label-name="name" value-name="code" @confirm="payOrgCodeConfirm" />
            <u-field disabled label="代理商" right-icon="arrow-down-fill" placeholder="请选择下级代理商" :value="agent" :rules="[{ required: true, message: '必选' }]" @click="showAgents" />
            <u-select v-model="agentsPicker" mode="single-column" :list="agents" @confirm="agentConfirm" />

            <section v-if="showActCashbackRule">
                <p>激活返现规则</p>
                <div>
                    <u-form-item label="激活标准" prop="freezeAmount">
                        <u-input type="digit" placeholder="请输入激活标准" v-model="terminal.freezeAmount" />
                    </u-form-item>
                    <u-form-item label="激活返现金额" prop="reachActivityAmount">
                        <u-input type="digit" placeholder="请输入激活返现金额" v-model="terminal.reachActivityAmount" />
                    </u-form-item>
                    <u-form-item label="服务费设置">
                        <u-input type="digit" placeholder="服务费设置" v-model="terminal.serviceAmount" disabled />
                    </u-form-item>
                </div>
            </section>

            <section v-if="showSimReturnRule || showSimCycleReturnRule">
                <p>sim卡返现规则</p>
                <div v-if="showSimReturnRule">
                    <u-form-item label="sim卡首次返现比例(%)" prop="simCashbackRate" key="simCashbackRate">
                        <u-input type="digit" placeholder="请输入首次返现比例" v-model="terminal.simCashbackRate" />
                    </u-form-item>
                </div>
                <div v-if="showSimCycleReturnRule">
                    <u-form-item label="sim卡循环返现比例(%)" prop="cycleCashbackRate" key="cycleCashbackRate">
                        <u-input type="digit" placeholder="请输入循环返现比例" v-model="terminal.cycleCashbackRate" />
                    </u-form-item>
                </div>
            </section>

            <section v-if="showReaCashbackRule">
                <div class="dbfx-add">
                    <p>达标返现规则</p>
                    <p @click="addRule">新增</p>
                </div>
                <section class="last-section">
                    <div v-for="(t, index) in terminal.terCashList" :key="index">
                        <p class="dbfx-delete">
                            <span>规则{{ t.ruleType }}</span>
                            <u-icon v-if="t.source == 1" name="trash" size="32" @click="deleteRule(index)" />
                        </p>
                        <u-form-item label="活动金额">
                            <u-input type="digit" placeholder="请输入活动金额" v-model="t.activityAmount" :rules="[
                { pattern, message: '必须符合金额规则且不得超过2位小数' },
              ]" />
                        </u-form-item>
                        <u-form-item label="活动天数">
                            <u-input type="number" placeholder="请输入活动天数" v-model="t.activityDays" :rules="[{ required: true, message: '必填' }]" />
                        </u-form-item>
                        <u-form-item label="达标返现金额" :border-bottom="false">
                            <u-input type="digit" placeholder="请输入达标返现金额" v-model="t.reachCashbackAmount" :rules="[
                { pattern, message: '必须符合金额规则且不得超过2位小数' },
              ]" />
                        </u-form-item>

                    </div>
                </section>
            </section>

            <footer v-if="showButton" class="custom-button">
                <button @click="submit">确 认</button>
            </footer>
        </u-form>
    </div>
</template>

<script>
import {
    getCashBackAgent,
    getCashBackRules,
    changeCashBackRules,
    getChannel
} from "../../http/api";

const pattern = /^([1-9]\d{0,}|0|0\.\d{0,1}[1-9]|[1-9]\d{0,}\.\d{0,1}[1-9])$/;
const rules = {
    terminalNo: [{ required: true, message: '必填' }],
    freezeAmount: [{ pattern, message: '必须符合金额规则且不得超过2位小数' }],
    reachActivityAmount: [{ pattern, message: '必须符合金额规则且不得超过2位小数' }],
    simCashbackRate: [{ pattern, message: '必须大于等于0、小于等于100且不得超过2位小数' }],
    cycleCashbackRate: [{ pattern, message: '必须大于等于0、小于等于100且不得超过2位小数' }],
}
export default {
    name: "ChangeReachCashBackRules",
    data() {
        return {
            agentsPicker: false,
            agents: [],
            agent: "",
            payOrgCodesPicker: false,
            payOrgCodes: [],
            payOrgCode: "",
            showActCashbackRule: false,
            showReaCashbackRule: false,
            showSimReturnRule: false,
            showSimCycleReturnRule: false,
            showButton: true,
            terminal: {
                payOrgCode: '',
                terminalNo: "",
                toAgentCode: "",
                actReturn: "",
                freezeAmount: "",
                reachActivityAmount: "",
                serviceAmount: "",
                cashReturn: "",
                terCashList: [
                    {
                        ruleType: 1,
                        activityAmount: "",
                        activityDays: "",
                        reachCashbackAmount: "",
                        source: 0,
                    },
                ],
                simReturn: "",
                simCashbackRate: "",
                cycleReturn: "",
                cycleCashbackRate: "",
            },
        };
    },
    onLoad() { },
    onReady() {
        this.$refs.uForm.setRules(rules);
    },
    methods: {
        showPayOrgCodes() {
            this.payOrgCodes = [];
            getChannel().then((res) => {
                if (res.code == "00") {
                    if (res.data.length) {
                        this.payOrgCodes = res.data
                        this.payOrgCodesPicker = true;
                    } else {
                        this.$u.toast("暂无可选支付通道！");
                    }
                }
            });
        },
        payOrgCodeConfirm([{ label, value }]) {
            this.payOrgCode = label
            this.terminal.payOrgCode = value
            this.payOrgCodesPicker = false
        },
        showAgents() {
            if (this.terminal.terminalNo != "") {
                if (this.terminal.payOrgCode == "") {
                    return this.$u.toast("请选择支付通道！");
                }
                this.agents = [];
                getCashBackAgent(this.terminal.terminalNo,this.terminal.payOrgCode).then((res) => {
                    if (res.code == "00") {
                        if (res.data.length != 0) {
                            res.data.forEach((i) => {
                                this.agents.push({ label: i.name + "（" + i.agentCode + "）", value: i.agentCode });
                            });
                            this.agentsPicker = true;
                        } else {
                            uni.showToast({
                                title: '该支付通道下此终端暂未绑定代理商！',
                                icon: 'none'
                            });
                        }
                    }
                });
            } else {
                uni.showToast({
                    title: '请先填写终端号！',
                    icon: 'none'
                });
            }
        },
        agentConfirm(val) {
            const label = val[0].label;
            const bracketStart = label.lastIndexOf("（")

            this.agent = label.substring(0, bracketStart)
            this.terminal.toAgentCode = val[0].value;
            this.agentsPicker = false;
            getCashBackRules({
                payOrgCode: this.terminal.payOrgCode,
                terminalNo: this.terminal.terminalNo,
                toAgentCode: this.terminal.toAgentCode,
            }).then((res) => {
                if (res.code == "00") {
                    this.terminal.simReturn = res.data.simReturn;
                    this.terminal.cycleReturn = res.data.cycleReturn;
                    this.terminal.actReturn = res.data.actReturn;
                    this.terminal.cashReturn = res.data.cashReturn;
                    if (res.data.simReturn == 0) {
                        this.showSimReturnRule = true;
                        this.terminal.simCashbackRate = res.data.simCashbackRate;
                    } else {
                        this.showSimReturnRule = false;
                    }
                    if (res.data.cycleReturn == 0) {
                        this.showSimCycleReturnRule = true;
                        this.terminal.cycleCashbackRate = res.data.cycleCashbackRate;
                    } else {
                        this.showSimCycleReturnRule = false;
                    }
                    if (res.data.actReturn == 1 && res.data.cashReturn == 1) {
                        this.showButton = false;
                        this.showActCashbackRule = false;
                        this.showReaCashbackRule = false;
                        uni.showToast({
                            title: '当前终端并未参与返现规则！',
                            icon: 'none'
                        });
                    } else {
                        this.showButton = true;
                        if (res.data.actReturn == 0) {
                            this.showActCashbackRule = true;
                            this.terminal.freezeAmount = res.data.activateRule.freezeAmount;
                            this.terminal.reachActivityAmount =
                                res.data.activateRule.reachActivityAmount;
                            this.terminal.serviceAmount = res.data.activateRule.serviceAmount;
                        } else {
                            this.showActCashbackRule = false;
                        }
                        if (res.data.cashReturn == 0) {
                            this.showReaCashbackRule = true;
                            this.terminal.terCashList = [];
                            res.data.cashbackRule.forEach((i) => {
                                this.terminal.terCashList.push({
                                    ruleType: i.ruleType,
                                    activityAmount: i.activityAmount,
                                    activityDays: i.activityDays,
                                    reachCashbackAmount: i.reachCashbackAmount,
                                    source: 0,
                                });
                            });
                            if (
                                this.$store.state.userInfo.agentLevel == 1 ||
                                this.$store.state.userInfo.agentLevel + 1 ==
                                res.data.cashbackRule.agentLevel
                            ) {
                                this.readonly = false;
                            } else {
                                this.readonly = true;
                            }
                        } else {
                            this.showReaCashbackRule = false;
                        }
                    }
                }
            });
        },
        addRule() {
            this.terminal.terCashList.push({
                ruleType: this.terminal.terCashList.length + 1,
                activityAmount: "",
                activityDays: "",
                reachCashbackAmount: "",
                source: 1,
            });
        },
        deleteRule(index) {
            uni.showModal({
                content: "是否确认删除规则" + (index + 1) + "？",
                success: (res) => {
                    if (res.confirm) {
                        this.terminal.terCashList.splice(index, 1);
                        this.terminal.terCashList.forEach((i, index) => {
                            i.ruleType = index + 1;
                        });
                    }
                }
            });
        },
        submit() {
            if (this.agent == '') {
                return uni.showToast({
                    title: '请选择下级代理商',
                    icon: 'none'
                });
            }
            this.$refs.uForm.validate(valid => {
                if (valid) {
                    changeCashBackRules(this.terminal).then((res) => {
                        if (res.code == "00") {
                            uni.showToast({
                                title: res.message,
                                icon: 'none'
                            });
                            setTimeout(() => {
                                this.$Router.back(1);
                            }, 1500);
                        }
                    });
                }
            })
        },
    },
};
</script>

<style lang="less" scoped>
#changeReachCashBackRules {
    padding-top: 20rpx;
    /deep/ .u-form {
        background-color: #fff;
        .u-form-item {
            padding: 20rpx 28rpx;
            line-height: 0;
            input {
                height: 48rpx;
                min-height: 48rpx !important;
            }
        }
        > section {
            > section {
                > div {
                    border-radius: 20rpx;
                    background: #f3f5f7;
                    &:not(:last-of-type) {
                        margin-bottom: 20rpx;
                    }
                    > p {
                        height: 94rpx;
                        text-align: right;
                        padding: 20rpx 0;
                        position: relative;
                        border-bottom: 2rpx solid #ebecec;
                        > span {
                            line-height: 54rpx;
                            position: absolute;
                            left: 0;
                        }
                    }
                }
            }
            p {
                padding: 20rpx 30rpx;
                color: #222222;
                font-size: 24rpx;
                background-color: #efefef;
            }
            .dbfx-add {
                display: flex;
                justify-content: space-between;
                align-items: center;
                background-color: #efefef;
                p:last-child {
                    padding: 2rpx 16rpx;
                    margin-right: 30rpx;
                    color: rgb(0, 78, 169);
                    border-radius: 8rpx;
                    border: 2rpx solid rgb(0, 78, 169);
                    font-size: 24rpx;
                }
            }
            .dbfx-delete {
                margin: 0 30rpx;
                line-height: 54rpx;
                background-color: transparent;
            }
        }
        .last-section {
            background-color: #fff;
            padding: 20rpx 30rpx;
        }
        .custom-button {
            position: fixed;
            bottom: 0;
            z-index: 9;
            margin-top: 30rpx;
        }
    }
}
</style>