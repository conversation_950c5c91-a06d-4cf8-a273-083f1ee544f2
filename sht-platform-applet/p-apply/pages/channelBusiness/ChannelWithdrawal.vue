<template>
    <div id="channelWithdrawal">
        <main>
            <div @click="showBankCard = true">
                <section>
                    <image src="../../../static/images/home/<USER>" alt="" />
                    <div class="s-right" v-if="bankCards.length != 0">
                        <div>
                            <p>{{ defaultBankCard.bankName }}</p>
                            <p>
                                尾号
                                {{
                                    defaultBankCard.bankAccountNoMask.substring(
                                        defaultBankCard.bankAccountNoMask.length - 4
                                    )
                                    }}储蓄卡
                            </p>
                        </div>
                        <p>选择其他银行卡>></p>
                    </div>
                    <h4 v-if="bankCards.length == 0" style="color: #fff">
                        暂无结算卡，请点此添加
                    </h4>
                </section>
            </div>
            <div>
                <section>
                    <p>余额</p>
                    <p>￥{{ balance }}</p>
                </section>
                <section>
                    <p>提现金额</p>
                    <p>
                        <view>
                            <u-field label="￥" :label-width="60" :border-bottom="false" placeholder="请输入现金金额" v-model="money" />
                        </view>
                        <span @click="all">全部提现</span>
                    </p>
                    <view v-show="!showValidMoney" class="error-message">必须符合金额规则且不得超过2位小数</view>
                </section>
                <section>
                    <p>
                        可提现金额: <span>{{ balance }}元</span>
                    </p>
                </section>
            </div>

            <footer>
                <div>
                    <span>提现金额<span>(不得低于{{ minSingleAmount }}元且不得高于{{maxSingleAmount}}元)</span></span>
                </div>
                <view style="margin:50rpx">
                    <u-button type='primary' color="#004EA9" @click="submit">去提现</u-button>
                </view>
                <p><span @click="record">提现记录</span></p>
            </footer>

            <u-popup v-model="showBankCard" mode="bottom" border-radius="30" @closed="setDefaultCardCancel" class="u-proup">
                <view class="proup-content">
                    <div class="toolbar">
                        <text @click="showBankCard = false">取消</text>
                        <text @click="setDefaultCardConfirm"> 确认</text>
                    </div>
                    <div class="line" />
                    <p v-if="bankCards.length">选择下列银行卡，点击确认可设置默认结算卡</p>
                    <scroll-view v-if="bankCards.length" style="height:320rpx" scroll-y>
                        <u-radio-group v-model="bankCardIndex">
                            <u-cell-group :border="false">
                                <u-cell-item v-for="(b, index) in bankCards" :border-bottom="false" :arrow="false" :key="index" clickable @click="bankCardSelect(index, b.bankAccountType)">
                                    <template #title>
                                        <p>
                                            {{ b.bankName }}
                                            <span class="bankType">({{ b.bankAccountType == 1 ? "对公" : "对私" }})</span>
                                        </p>
                                        <p class="info">{{ b.bankAccountNoMask }}</p>
                                    </template>
                                    <template #right-icon>
                                        <u-radio :name="index" />
                                    </template>
                                </u-cell-item>
                            </u-cell-group>
                        </u-radio-group>
                    </scroll-view>
                    <p class="tips">
                        注：对公/对私均只能添加一张结算卡，若需修改某结算卡信息直接重新添加即可
                    </p>
                    <div class="line" />
                    <div class="addBank" @click="addBankCard">
                        <u-icon name="plus" />
                        <span>添加银行卡</span>
                    </div>
                </view>
            </u-popup>
        </main>
    </div>
</template>

<script>
import {
    getWalletBalance,
    getBankCard,
    queryWithdrawLimit,
    setDefaultBankCard,
    channelWithdraw,
} from "../../../http/api";

export default {
    name: "ChannelWithdrawal",
    data() {
        return {
            balance: "0.00",
            defaultBankCard: {
                bankName: "",
                bankAccountNoMask: "",
            },
            showBankCard: false,
            bankCards: [],
            bankCardIndex: null,
            selectBankCard: {
                index: null,
                bankAccountType: null,
            },
            minSingleAmount: "0",
            maxSingleAmount: "0",
            pattern: /^([1-9]\d{0,}|0\.\d{0,1}[1-9]|[1-9]\d{0,}\.\d{0,1}[1-9])$/,
            money: null,
        };
    },
    computed: {
        showValidMoney() {
            if (this.money) {
                return /^([1-9]\d{0,}|0\.\d{0,1}[1-9]|[1-9]\d{0,}\.\d{0,1}[1-9])$/.test(this.money)
            } else {
                return true
            }
        }
    },
    onLoad() {
        getWalletBalance(this.$Route.query.payChannelNo).then((res) => {
            if (res.code == "00") {
                this.balance = res.data;
            }
        });
        queryWithdrawLimit(this.$Route.query.payChannelNo).then((res) => {
            if (res.code == "00") {
                this.minSingleAmount = res.data.minSingleAmount;
                this.maxSingleAmount = res.data.maxSingleAmount;
            }
        });
    },
    onShow() {
        getBankCard({ payChannelNo: this.$Route.query.payChannelNo }).then(
            (res) => {
                if (res.code == "00") {
                    if (res.data.length != 0) {
                        this.defaultBankCard = {
                            bankName: res.data.find((i) => i.isDefaultWithdraw == 1).bankName,
                            bankAccountNoMask: res.data.find((i) => i.isDefaultWithdraw == 1)
                                .bankAccountNoMask,
                        };
                        this.selectBankCard = {
                            index: res.data.findIndex((i) => i.isDefaultWithdraw == 1),
                            bankAccountType: res.data.find((i) => i.isDefaultWithdraw == 1)
                                .bankAccountType,
                        };
                        this.bankCards = res.data;
                        this.bankCardIndex = res.data.findIndex(
                            (i) => i.isDefaultWithdraw == 1
                        );
                    }
                }
            }
        );
    },
    methods: {
        record() {
            this.$Router.push({ name: "WithdrawalRecord", params: { payChannelNo: this.$Route.query.payChannelNo } });
        },
        bankCardSelect(index, bankAccountType) {
            this.bankCardIndex = index;
            this.selectBankCard = {
                index: index,
                bankAccountType: bankAccountType,
            };
        },
        setDefaultCardCancel() {
            this.bankCardIndex = this.bankCards.findIndex(
                (i) => i.bankAccountNoMask == this.defaultBankCard.bankAccountNoMask
            );
        },
        setDefaultCardConfirm() {
            setDefaultBankCard({
                payChannelNo: this.$Route.query.payChannelNo,
                bankAccountType: this.selectBankCard.bankAccountType,
            }).then((res) => {
                if (res.code == "00") {
                    this.defaultBankCard = {
                        bankName: this.bankCards[this.selectBankCard.index].bankName,
                        bankAccountNoMask: this.bankCards[this.selectBankCard.index]
                            .bankAccountNoMask,
                    };
                    this.bankCardIndex = this.selectBankCard.index;
                    uni.showToast({
                        title: res.message,
                        icon: 'none'
                    });
                } else {
                    this.bankCardIndex = this.bankCards.findIndex(
                        (i) => i.bankAccountNoMask == this.defaultBankCard.bankAccountNoMask
                    );
                }
            });
            this.showBankCard = false;
        },
        addBankCard() {
            this.$Router.push({ name: "AddChannelBankCard", params: { payChannelNo: this.$Route.query.payChannelNo } });
            this.showBankCard = false;
        },
        all() {
            if (
                parseFloat(this.balance) >= parseFloat(this.minSingleAmount) &&
                parseFloat(this.balance) <= parseFloat(this.maxSingleAmount)
            ) {
                this.money = this.balance;
            } else {
                uni.showToast({
                    title: '当前余额超出可提现金额范围！',
                    icon: 'none'
                });
            }
        },
        submit() {
            if (!this.showValidMoney) { return };
            if (
                parseFloat(this.balance) >= parseFloat(this.minSingleAmount) &&
                parseFloat(this.balance) <= parseFloat(this.maxSingleAmount)
            ) {
                channelWithdraw({
                    payChannelNo: this.$Route.query.payChannelNo,
                    amount: this.money,
                }).then((res) => {
                    if (res.code == "00") {
                        this.money = null;
                        uni.showToast({
                            title: res.message,
                            icon: 'none'
                        });
                        getWalletBalance(this.$Route.query.payChannelNo).then((res) => {
                            if (res.code == "00") {
                                this.balance = res.data;
                            }
                        });
                    }
                });
            } else {
                uni.showToast({
                    title: '提现余额超出可提现金额范围！',
                    icon: 'none'
                });
            }
        },
    },
};
</script>

<style lang="less" scoped>
#channelWithdrawal {
    padding-top: 20rpx;
    height: 100%;
    > main {
        min-height: 100%;
        background-color: #fff;
        > div {
            &:nth-of-type(1),
            &:nth-of-type(2) {
                background-color: white;
                margin: 0 30rpx;
                padding: 20rpx;
            }
            &:nth-of-type(2) {
                margin-top: 20rpx;
                background: #f3f5f7;
                border-radius: 20rpx;
                > section {
                    &:not(:nth-of-type(2)) {
                        display: flex;
                        justify-content: space-between;
                    }
                    &:first-child {
                        > p {
                            padding-bottom: 20rpx;
                            &:first-child {
                                font-weight: 500;
                                color: #222222;
                            }
                            &:last-child {
                                color: #ce0010;
                            }
                        }
                        border-bottom: 1rpx solid #e5e5e5;
                    }
                    &:nth-of-type(2) {
                        padding: 20rpx 0;
                        border-bottom: 2rpx solid #e5e5e5;
                        > p {
                            &:nth-last-of-type(2) {
                                display: flex;
                                align-items: center;
                                margin-top: 40rpx;
                                /deep/ .u-label {
                                    font-size: 60rpx;
                                }
                                > span {
                                    color: #004ea9;
                                }
                            }
                        }
                        .error-message {
                            font-size: 24rpx;
                            line-height: 24rpx;
                            color: #fa3534;
                            margin-top: 12rpx;
                            padding-left: 80rpx;
                        }
                    }
                    &:last-child {
                        > p {
                            color: #666;
                            font-size: 24rpx;
                            margin: 20rpx 0 0;
                            span {
                                color: #000;
                            }
                        }
                    }
                }
            }
            &:nth-of-type(1) {
                background-image: url("../../../static/images/common/bankBgm.png");
                background-size: 100% 100%;
                > section {
                    display: flex;
                    align-items: center;
                    color: #fff;
                    height: 100rpx;
                    h4 {
                        font-weight: 400;
                    }

                    > image {
                        width: 52rpx;
                        height: 52rpx;
                        margin-right: 30rpx;
                    }
                    p {
                        margin: 0;
                        padding: 0;
                    }
                    .s-right {
                        flex: 1;
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-end;
                        > div {
                            p {
                                &:nth-of-type(1) {
                                    letter-spacing: 2rpx;
                                }
                                &:nth-of-type(2) {
                                    font-size: 0.85rem;
                                    margin-top: 12rpx;
                                }
                            }
                        }
                        > p {
                            font-size: 0.85rem;
                        }
                    }
                }
            }
        }
        .proup-content {
            .toolbar {
                display: flex;
                justify-content: space-between;
                padding: 20rpx 30rpx;
                text {
                    &:last-of-type {
                        color: #004ea9;
                    }
                }
            }
            > p {
                padding: 20rpx 30rpx;
                color: #666666;
                border-bottom: 1rpx solid #f8f8f8;
            }
            .tips {
                font-size: 24rpx;
                color: #999;
                border: none;
                border-top: 1rpx solid #f8f8f8;
            }
            .addBank {
                display: flex;
                align-items: center;
                justify-content: center;
                color: #e60012;
                padding: 30rpx 0;
            }
        }
        footer {
            text-align: center;
            > div {
                background-color: white;
                padding: 30rpx;
                > span {
                    color: #999999;
                    font-size: 24rpx;
                }
            }
            > p {
                color: #222222;
                span {
                    padding: 8rpx 10rpx;
                }
            }
        }
    }

    .line {
        width: 100%;
        height: 20rpx;
        background: #f8f8f8;
    }
}
</style>