<template>
  <div id="merchantsUrlExpand">
    <u-form :model="merchantRate" ref="uForm" class="u-form">
      <u-form-item label="渠道" :label-width="200">
        <u-input
          type="select"
          :select-open="channelsPicker"
          placeholder="请选择渠道"
          :value="channel"
          @click="showChannels"
          :clearable="false"
        />
      </u-form-item>
      <u-select
        v-model="channelsPicker"
        mode="single-column"
        :list="channels"
        value-name="val"
        label-name="val"
        @confirm="channelConfirm"
      />

      <p>
        <u-form-item label="商户链接名称" :label-width="200" prop="name">
          <u-input type="text" placeholder="请输入链接名称" v-model="merchantRate.name" />
        </u-form-item>
      </p>
      <p v-if="merchantRate.payChannelNo == '0020'">
        <u-form-item label="商户注册链接" :label-width="200" prop="url">
          <u-input type="text" placeholder="请输入商户注册链接" v-model="merchantRate.url" />
        </u-form-item>
      </p>
      <template v-else>
        <div>
          <u-form-item label="信用卡手续费费率(%)" prop="cFeeRate">
            <u-input type="digit" placeholder="请填写" v-model="merchantRate.cFeeRate" :clearable="false" />
          </u-form-item>
        </div>
        <div>
          <u-form-item
            v-if="merchantRate.payChannelNo == '0019' || merchantRate.payChannelNo == '0021'"
            label="特惠费率(%)"
            prop="thCreditFeeRate"
          >
            <u-input type="digit" placeholder="请填写" v-model="merchantRate.thCreditFeeRate" :clearable="false" />
          </u-form-item>
        </div>
        <div>
          <u-form-item label="借记卡手续费费率(%)" prop="dFeeRate">
            <u-input type="digit" placeholder="请填写" v-model="merchantRate.dFeeRate" :clearable="false" />
          </u-form-item>
          <u-form-item label="借记卡手续费最大值(元)" prop="dFeeMax">
            <u-input type="number" placeholder="请填写" v-model="merchantRate.dFeeMax" :clearable="false" />
          </u-form-item>
        </div>
        <div>
          <u-form-item :label="(isDianYin ? '扫码' : '微信手续费费率') + '(%)'" prop="wechatPayFeeRate">
            <u-input type="digit" placeholder="请填写" v-model="merchantRate.wechatPayFeeRate" :clearable="false" />
          </u-form-item>
          <u-form-item v-if="!isDianYin" label="支付宝手续费费率(%)" prop="alipayFeeRate">
            <u-input type="digit" placeholder="请填写" v-model="merchantRate.alipayFeeRate" :clearable="false" />
          </u-form-item>
        </div>
        <div>
          <u-form-item :label="(isDianYin ? '云闪付' : ' 云闪付信用卡手续费费率') + '(%)'" prop="ycFeeRate">
            <u-input type="digit" placeholder="请填写" v-model="merchantRate.ycFeeRate" :clearable="false" />
          </u-form-item>
          <u-form-item v-if="!isDianYin" label="云闪付借记卡手续费费率(%)" prop="ydFeeRate">
            <u-input type="digit" placeholder="请填写" v-model="merchantRate.ydFeeRate" :clearable="false" />
          </u-form-item>
        </div>
      </template>
    </u-form>

    <footer class="custom-button">
      <button @click="submit">确 认</button>
    </footer>
  </div>
</template>

<script>
import { getMerchantUrlChannel, addMerchantUrl } from '../../../http/api';

const pattern = /^0\.\d{0,2}[1-9]$/;
const costs = ['cFeeRate', 'dFeeRate', 'wechatPayFeeRate', 'alipayFeeRate', 'ycFeeRate', 'ydFeeRate', 'thCreditFeeRate'];
const rules = {
  name: [
    {
      required: true,
      message: '必填'
    }
  ],
  url: [
    {
      required: true,
      message: '必填'
    }
  ],
  dFeeMax: [
    {
      required: true,
      message: '必填'
    },
    {
      pattern: /^(([1-9][0-9]*)|0|(([0]\.\d{1,3}|[1-9][0-9]*\.\d{1,3})))$/,
      message: '不得超过3位小数'
    }
  ]
};
costs.forEach(
  c =>
    (rules[c] = [
      {
        pattern,
        message: '必填、大于0小于1且不得超过3位小数'
      }
    ])
);

export default {
  name: 'MerchantsUrlExpand',
  data() {
    return {
      channelsPicker: false,
      channels: [],
      channelCodes: [],
      channel: '',
      merchantRate: {
        payChannelNo: '',
        name: '',
        url: '',
        cFeeRate: '0.6',
        dFeeRate: '0.5',
        dFeeMax: '20',
        wechatPayFeeRate: '0.35',
        alipayFeeRate: '0.35',
        ycFeeRate: '0.35',
        ydFeeRate: '0.35',
        thCreditFeeRate: '0.00'
      }
    };
  },
  computed: {
    isDianYin() {
      return this.merchantRate.payChannelNo == '0007';
    }
  },
  onLoad() {},
  onReady() {
    this.$refs.uForm.setRules(rules);
  },
  methods: {
    showChannels() {
      this.channels = [];
      this.channelCodes = [];
      getMerchantUrlChannel().then(res => {
        if (res.code == '00') {
          if (res.data.length != 0) {
            res.data.forEach(i => {
              this.channels.push({
                val: i.channelName
              });
              this.channelCodes.push(i.channelCode);
            });
            this.channelsPicker = true;
          } else {
            uni.showToast({
              title: '暂无可选渠道！',
              icon: 'none'
            });
          }
        }
      });
    },
    channelConfirm(val) {
      this.channel = val[0].value;
      this.merchantRate.payChannelNo = this.channelCodes[this.channels.findIndex(i => i.val == this.channel)];
      this.channelsPicker = false;
    },
    submit() {
      if (this.channel) {
        this.$refs.uForm.validate(valid => {
          if (valid) {
            if (this.isDianYin) {
              this.merchantRate.alipayFeeRate = this.merchantRate.ydFeeRate = '0';
            }
            addMerchantUrl(this.merchantRate).then(res => {
              if (res.code == '00') {
                uni.showToast({
                  title: res.message,
                  icon: 'none'
                });
                setTimeout(() => {
                  this.$Router.back(1);
                }, 1000);
              }
            });
          }
        });
      } else {
        uni.showToast({
          title: '请选择渠道！',
          icon: 'none'
        });
      }
    }
  }
};
</script>

<style lang="less" scoped>
#merchantsUrlExpand {
  height: 100%;
  padding-top: 20rpx;

  .u-form {
    /deep/ .u-form-item {
      padding: 20rpx 28rpx;
      line-height: 0;
      background-color: #fff;
    }

    > p {
      margin: 0;
      margin-bottom: 20rpx;
    }

    > div {
      margin-bottom: 20rpx;

      /deep/ .u-form-item {
        .u-form-item--left {
          flex: 1 !important;
        }

        .u-form-item--right {
          flex: none;
          width: 140rpx;
          border-radius: 6rpx;
          background: #eaeef1;

          input {
            text-align: center;
            color: #4e77d9;
          }
        }

        .u-form-item__message {
          text-align: right;
        }
      }

      &:last-of-type {
        margin-bottom: 102rpx;
      }
    }
  }

  .custom-button {
    position: fixed;
    bottom: 0;
  }
}
</style>
