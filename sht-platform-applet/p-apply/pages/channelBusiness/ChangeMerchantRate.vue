<template>
  <div id="changeMerchantRate">
    <u-form :model="merchantRate" ref="uForm" class="u-form">
      <u-form-item label="渠道" :label-width="150">
        <u-input type="select" :select-open="channelsPicker" placeholder="请选择渠道" :value="channel" @click="showChannels" />
      </u-form-item>
      <u-select
        v-model="channelsPicker"
        mode="single-column"
        :list="channels"
        value-name="val"
        label-name="val"
        @confirm="channelConfirm"
      />
      <u-form-item label="商户" :label-width="150">
        <u-input placeholder="请选择商户(可填写再搜索)" v-model="merchant" />
        <u-icon slot="right" name="search" size="40" color="#666" @click="showMerchants" />
      </u-form-item>
      <u-select
        v-model="merchantsPicker"
        mode="single-column"
        :list="merchants"
        value-name="value"
        label-name="text"
        @confirm="merchantConfirm"
      />
      <u-form-item label="SN" :label-width="150" v-if="isSnRequired">
        <u-input type="select" :select-open="snPicker" placeholder="SN" :value="merchantRate.terminalNo" @click="showTerminals" />
      </u-form-item>
      <u-select v-model="snPicker" mode="single-column" :list="terminalNoList" value-name="val" label-name="val" @confirm="snConfirm" />

      <div class="titleInfo">商户费率修改</div>
      <section>
        <div key="creditFeeRate">
          <u-form-item label="信用卡费率(%)" prop="creditFeeRate">
            <u-input :clearable="false" type="digit" placeholder="请填写" v-model="merchantRate.creditFeeRate" />
          </u-form-item>
        </div>
        <div v-if="supportCreditThSwitch" key="yhFeeRate">
          <u-form-item
            :label="`${
              merchantRate.payChannelNo === '0016' ||
              merchantRate.payChannelNo === '0018' ||
              merchantRate.payChannelNo === '0019' ||
              merchantRate.payChannelNo === '0021' ||
              merchantRate.payChannelNo === '0033'
                ? '特惠'
                : merchantRate.payChannelNo === '0017'
                ? '享惠'
                : '优惠'
            }费率(%)`"
            prop="yhFeeRate"
          >
            <u-input :clearable="false" type="digit" placeholder="请填写" v-model="merchantRate.yhFeeRate" />
          </u-form-item>
        </div>
        <div key="debitFeeRate">
          <u-form-item label="借记卡费率(%)" prop="debitFeeRate">
            <u-input type="digit" placeholder="请填写" v-model="merchantRate.debitFeeRate" :clearable="false" />
          </u-form-item>
          <u-form-item v-if="merchantRate.payChannelNo !== '0019'" label="借记卡手续费最大值(元)" prop="debitFeeMax">
            <u-input type="number" placeholder="请填写" v-model="merchantRate.debitFeeMax" :clearable="false" />
          </u-form-item>
        </div>
        <div key="scanFeeRate">
          <u-form-item label="扫码费率(%)" prop="scanFeeRate">
            <u-input type="digit" placeholder="请填写" v-model="merchantRate.scanFeeRate" :clearable="false" />
          </u-form-item>
        </div>
        <div key="nfcFeeRate">
          <u-form-item label="云闪付费率(%)" prop="nfcFeeRate">
            <u-input type="digit" placeholder="请填写" v-model="merchantRate.nfcFeeRate" :clearable="false" />
          </u-form-item>
        </div>
        <div v-if="supportAlipayLargerSwitch" key="alipayLargerRate">
          <u-form-item label="支付宝大额费率(%)" prop="alipayLargerRate">
            <u-input type="digit" placeholder="选填" v-model="merchantRate.alipayLargerRate" :clearable="false" />
          </u-form-item>
        </div>
        <div v-if="supportD0FeeSwitch" key="d0Fee">
          <u-form-item label="D0单笔(元)" prop="d0Fee">
            <u-input type="digit" placeholder="请填写" v-model="merchantRate.d0Fee" :clearable="false" />
          </u-form-item>
        </div>
        <div v-if="supportD0RateSwitch" key="d0Rate">
          <u-form-item label="D0费率(%)" prop="d0Rate">
            <u-input type="digit" placeholder="请填写" v-model="merchantRate.d0Rate" :clearable="false" />
          </u-form-item>
        </div>
      </section>

      <footer class="custom-button">
        <button @click="submit">确 认</button>
      </footer>
    </u-form>
  </div>
</template>

<script>
import { getMerchantUrlChannel, getMerchantList, queryTerminalNoList, getMerchantRate, changeMerchantRate } from '../../../http/api';

const pattern = /^0\.\d{0,2}[1-9]$/;
const costs = ['creditFeeRate', 'yhFeeRate', 'debitFeeRate', 'scanFeeRate', 'nfcFeeRate','d0Rate'];
const rules = {
  debitFeeMax: [
    { required: true, message: '必填' },
    { pattern: /^(([1-9][0-9]*)|0|(([0]\.\d{1,3}|[1-9][0-9]*\.\d{1,3})))$/, message: '不得超过3位小数' }
  ],
  d0Fee: [
    { required: true, message: '必填' },
    { pattern: /^(([1-9][0-9]*)|0|(([0]\.\d{1,3}|[1-9][0-9]*\.\d{1,3})))$/, message: '不得超过3位小数' }
  ]
};
costs.forEach(c => (rules[c] = [{ pattern, message: '必填、大于0小于1且不得超过3位小数' }]));
rules.alipayLargerRate = [
  {
    validator: function (rule, val) {
      if (!val && val !== 0) return true;
      return pattern.test(val);
    },
    message: '大于0、小于1且不得超过3位小数'
  }
];
rules.d0Rate = [
  {
    validator: function (rule, val) {
      if (!val && val !== 0) return true;
      return pattern.test(val);
    },
    message: '大于0、小于1且不得超过3位小数'
  }
];

export default {
  name: 'ChangeMerchantRate',
  data() {
    return {
      channelsPicker: false,
      channels: [],
      channelCodes: [],
      channel: '',
      merchantsPicker: false,
      merchants: [],
      merchant: '',
      isSnRequired: true,
      snPicker: false,
      terminalNoList: [],
      merchantRate: {
        payChannelNo: '',
        merchId: '',
        terminalNo: '',
        alipayLargerRate: '',
        d0Fee: '0',
        d0Rate: '0.00',
        creditFeeRate: '0.00',
        yhFeeRate: '0.00',
        debitFeeRate: '0.00',
        debitFeeMax: '0',
        scanFeeRate: '0.00',
        nfcFeeRate: '0.00'
      },
      supportAlipayLargerSwitch: false,
      supportCreditThSwitch: false,
      supportD0FeeSwitch: false,
      supportD0RateSwitch: false,
      switchs: []
    };
  },
  onLoad() {},
  onReady() {
    this.$refs.uForm.setRules(rules);
  },
  computed: {
    showYhFeeRate() {
      return ['0016', '0017', '0018', '0019', '0021', '0033'].includes(this.merchantRate.payChannelNo);
    }
  },
  methods: {
    showChannels() {
      this.channels = [];
      this.channelCodes = [];
      this.switchs = [];
      getMerchantUrlChannel().then(res => {
        if (res.code == '00') {
          if (res.data.length != 0) {
            res.data.forEach(i => {
              this.channels.push({ val: i.channelName });
              this.channelCodes.push(i.channelCode);
              this.switchs.push({ supportAlipayLargerSwitch: i.supportAlipayLargerSwitch, supportCreditThSwitch: i.supportCreditThSwitch ,
                supportD0FeeSwitch: i.supportD0FeeSwitch, supportD0RateSwitch: i.supportD0RateSwitch
              });
            });
            this.channelsPicker = true;
          } else {
            uni.showToast({
              title: '暂无可选渠道！',
              icon: 'none'
            });
          }
        }
      });
    },
    channelConfirm(val) {
      this.channel = val[0].value;
      this.merchantRate.payChannelNo = this.channelCodes[this.channels.findIndex(i => i.val == this.channel)];
      this.channelsPicker = false;
      this.merchant = this.merchantRate.merchId = this.merchantRate.terminalNo = '';

      const switchItem = this.switchs[this.channels.findIndex(i => i.val == this.channel)] || {};
      this.supportCreditThSwitch = switchItem.supportCreditThSwitch;
      this.supportAlipayLargerSwitch = switchItem.supportAlipayLargerSwitch;
      this.supportD0FeeSwitch = switchItem.supportD0FeeSwitch;
      this.supportD0RateSwitch = switchItem.supportD0RateSwitch;
    },
    showMerchants() {
      this.merchants = [];
      getMerchantList(this.merchant, this.merchantRate.payChannelNo).then(res => {
        if (res.code == '00') {
          if (res.data.length != 0) {
            res.data.forEach(i => {
              this.merchants.push({ text: i.merchantName + '-' + i.merchantId, value: i.merchantId, extra: i.merchantName });
            });
            this.merchantsPicker = true;
          } else {
            uni.showToast({
              title: '暂无可选商户！',
              icon: 'none'
            });
          }
        }
      });
    },
    merchantConfirm(val) {
      this.merchant = val[0].extra;
      this.merchantRate.merchId = val[0].value;
      this.merchantsPicker = false;
      this.merchantRate.terminalNo = '';
      if (this.merchantRate.payChannelNo != '') {
        this.getTerminals();
      }
    },
    showTerminals() {
      if (!(this.merchantRate.payChannelNo != '' && this.merchantRate.merchId != '')) {
        uni.showToast({
          title: '请先选择渠道和商户',
          icon: 'none'
        });
        return;
      }
      this.snPicker = true;
    },
    getTerminals() {
      this.terminalNoList = [];
      const { payChannelNo, merchId } = this.merchantRate;
      queryTerminalNoList({ payChannelNo, merchId }).then(res => {
        if (res.code == '00') {
          if (res.data.length != 0) {
            res.data.forEach(i => {
              this.terminalNoList.push({ val: i });
            });
            this.isSnRequired = true;
            this.merchantRate.terminalNo = res.data[0];
            this.getRate();
          } else {
            this.isSnRequired = false;
            this.getRate();
          }
        }
      });
    },
    snConfirm(val) {
      this.merchantRate.terminalNo = val[0].value || '';
      this.snPicker = false;
      if (this.merchantRate.terminalNo) {
        this.getRate();
      }
    },
    getRate() {
      getMerchantRate({
        payChannelNo: this.merchantRate.payChannelNo,
        merchId: this.merchantRate.merchId,
        terminalNo: this.merchantRate.terminalNo
      }).then(res => {
        if (res.code == '00') {
          this.merchantRate = {
            payChannelNo: this.merchantRate.payChannelNo,
            merchId: this.merchantRate.merchId,
            terminalNo: this.merchantRate.terminalNo,
            creditFeeRate: res.data.creditFeeRate,
            yhFeeRate: res.data.yhFeeRate,
            debitFeeRate: res.data.debitFeeRate,
            alipayLargerRate: res.data.alipayLargerRate,
            d0Fee: res.data.d0Fee,
            d0Rate: res.data.d0Rate,
            scanFeeRate: res.data.scanFeeRate,
            nfcFeeRate: res.data.nfcFeeRate,
            debitFeeMax: res.data.debitFeeMax
          };
        }
      });
    },
    submit() {
      if (this.merchantRate.payChannelNo != '' && this.merchantRate.merchId != '') {
        if (this.isSnRequired && this.merchantRate.terminalNo === '') {
          uni.showToast({
            title: '未选择SN！',
            icon: 'none'
          });
          return;
        }
        this.$refs.uForm.validate(valid => {
          if (valid) {
            if (this.merchantRate.payChannelNo == '0019') {
              this.merchantRate.debitFeeMax = '20';
            }
            if (!this.supportCreditThSwitch) {
              this.merchantRate.yhFeeRate = null;
            }
            if (!this.supportAlipayLargerSwitch) {
              this.merchantRate.alipayLargerRate = null;
            }
            if (!this.supportD0FeeSwitch) {
              this.merchantRate.d0Fee = null;
            }
            if (!this.supportD0RateSwitch) {
              this.merchantRate.d0Rate = null;
            }
            changeMerchantRate(this.merchantRate).then(res => {
              if (res.code == '00') {
                uni.showToast({
                  title: res.message,
                  icon: 'none'
                });
              }
            });
          }
        });
      } else {
        uni.showToast({
          title: this.merchantRate.payChannelNo == '' ? '未选择渠道！' : '未选择商户！',
          icon: 'none'
        });
      }
    }
  }
};
</script>

<style lang="less" scoped>
#changeMerchantRate {
  padding-top: 20rpx;
  .u-form {
    /deep/ .u-form-item {
      padding: 20rpx 28rpx;
      line-height: 0;
      background-color: #fff;
    }

    .titleInfo {
      margin: 2vw 4.26667vw;
      font-size: 24rpx;
      color: #222222;
    }
    > section {
      padding-bottom: 102rpx;
      > div {
        margin-bottom: 20rpx;
        &:last-of-type {
          margin-bottom: 0;
        }
      }
      /deep/ .u-form-item {
        .u-form-item--left {
          flex: 1 !important;
        }
        .u-form-item--right {
          flex: none;
          width: 140rpx;
          border-radius: 6rpx;
          background: #eaeef1;
          input {
            text-align: center;
            color: #4e77d9;
          }
        }
        .u-form-item__message {
          text-align: right;
        }
      }
    }
    .custom-button {
      position: fixed;
      bottom: 0;
      z-index: 3;
    }
  }
}
</style>
