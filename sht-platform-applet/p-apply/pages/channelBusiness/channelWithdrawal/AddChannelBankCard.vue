<template>
    <div id="addChannelBankCard">
        <u-form :model="bankCard" ref="uForm" class="u-form" :label-width="200">
            <u-form-item label="银行卡号" prop="bankAccountNo">
                <u-input placeholder="请输入银行卡号" v-model="bankCard.bankAccountNo" />
            </u-form-item>

            <u-form-item label="开户人姓名" prop="bankAccountName">
                <u-input placeholder="请输入开户人姓名" v-model="bankCard.bankAccountName" />
            </u-form-item>

            <u-form-item label="银行账户类型">
                <u-radio-group v-model="bankCard.bankAccountType" shape="square">
                    <u-radio :name="1">对公</u-radio>
                    <u-radio :name="2">对私</u-radio>
                </u-radio-group>
            </u-form-item>

            <u-form-item label="开户行地区">
                <u-input type="select" :select-open="showSites" placeholder="请选择开户行地区" v-model="site" @click="getSite" />
            </u-form-item>
            <u-picker v-model="showSites" mode="multiSelector" :defaultSelector="defaultSelector" :range="sites" range-key="text" @columnchange="siteChange" @confirm="siteFinish" />

            <u-form-item label="银行名称">
                <u-input placeholder="请输入银行名称" v-model="bankName" />
            </u-form-item>

            <u-form-item label="开户行支行">
                <u-input type="textarea" disabled :select-open="branchBanksPicker" placeholder="请选择开户行支行" :value="branchBankName" @click="getBranchBanks" />
                <u-icon slot="right" name="arrow-down-fill" />
            </u-form-item>
            <u-select v-model="branchBanksPicker" :list="branchBanks" @confirm="branchBankConfirm" />

            <view class="submit-btn">
                <u-button type="primary" @click="submit">确 认</u-button>
            </view>
        </u-form>
    </div>
</template>

<script>
import {
    getBranchBankProvince,
    getBranchBankCity,
    getBranchBankBankType,
    getBranchBankList,
    addChannelBankCard,
} from "../../../../http/api";

const rules = {
    bankAccountNo: [{ required: true, message: '必填' }],
    bankAccountName: [{ required: true, message: '必填' }],
}
export default {
    name: "AddChannelBankCard",
    data() {
        return {
            showSites: false,
            sites: [],
            site: "",
            province: "",
            city: "",
            bankName: "",
            typeCode: "",
            branchBanksPicker: false,
            branchBanks: [],
            branchBankName: "",
            bankCard: {
                payChannelNo: '',
                bankAccountNo: "",
                bankAccountName: "",
                bankAccountType: 1,
                bankCode: "",
            },
            defaultSelector: [0, 0]
        };
    },
    onLoad() {
        this.bankCard.payChannelNo = this.$Route.query.payChannelNo
    },
    onReady() {
        this.$refs.uForm.setRules(rules);
    },
    methods: {
        getSite() {
            getBranchBankProvince({
                payChannelNo: this.$Route.query.payChannelNo,
            }).then((res) => {
                if (res.code == "00") {
                    this.sites = [[], []];
                    this.sites[0].push({
                        text: '请选择开户行地区',
                        value: -1
                    });
                    res.data.forEach((i) => {
                        this.sites[0].push({
                            text: i.cityName,
                            value: i.cityCode
                        });
                    });

                    this.defaultSelector = [0, 0]
                    this.showSites = true;
                }
            });
        },
        siteChange({ column, index }) {
            this.defaultSelector.splice(column, 1, index)

            if (column == 0) {
                this.sites.splice(1, 1, [])

                this.sites[0][index].value !== -1 && getBranchBankCity({
                    payChannelNo: this.$Route.query.payChannelNo,
                    parentCode: this.sites[0][index].value,
                }).then((res) => {
                    if (res.code == "00") {
                        res.data.forEach((i) => {
                            this.sites[1].push({
                                text: i.cityName,
                                value: i.cityCode,
                            })
                        });
                        this.defaultSelector.splice(1, 1, 0)
                    }
                });
            }
        },
        siteFinish(val) {
            this.site = val.map((i, index) => this.sites[index][i].text).join("/");
            this.province = this.sites[0][val[0]].value;
            this.city = this.sites[1][val[1]].value;
        },
        getBranchBanks() {
            if (this.site != "" && this.bankName != "") {
                getBranchBankBankType({
                    payChannelNo: this.$Route.query.payChannelNo,
                    typeName: this.bankName,
                }).then((res) => {
                    if (res.code == "00") {
                        getBranchBankList({
                            payChannelNo: this.$Route.query.payChannelNo,
                            province: this.province,
                            city: this.city,
                            typeCode: res.data[0].typeCode,
                        }).then((resp) => {
                            if (resp.code == "00") {
                                this.branchBanks = [];
                                resp.data.forEach((i) => {
                                    this.branchBanks.push({ label: i.bankName, value: i.bankCode });
                                });
                                this.branchBanksPicker = true;
                            }
                        });
                    }
                });
            } else {
                uni.showToast({
                    title: '开户行地区或银行名称不能为空！',
                    icon: 'none'
                });
            }
        },
        branchBankConfirm(val) {
            this.branchBankName = val[0].label;
            this.bankCard.bankCode = val[0].value
            this.branchBanksPicker = false;
        },
        submit() {
            this.$refs.uForm.validate(valid => {
                if (valid) {
                    if (this.bankCard.bankCode === '') {
                        return uni.showToast({
                            title: '请选择开户行支行！',
                            icon: 'none'
                        });
                    }
                    addChannelBankCard(this.bankCard).then((res) => {
                        if (res.code == "00") {
                            uni.showToast({
                                title: res.message,
                                icon: 'none'
                            });
                            setTimeout(() => {
                                this.$Router.back(1);
                            }, 1500);
                        }
                    });
                }
            })

        },
    },
};
</script>

<style lang="less" scoped>
#addChannelBankCard {
    padding-top: 20rpx;
    .u-form {
        /deep/ .u-form-item {
            padding: 20rpx 28rpx;
            line-height: 0;
            background-color: #fff;
        }
    }
    > div {
        width: 100%;
        height: 20rpx;
        background: #f8f8f8;
    }
    .submit-btn {
        padding: 100rpx 80rpx;
    }
}
</style>