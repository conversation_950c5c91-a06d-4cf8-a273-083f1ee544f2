<template>
    <div id="withdrawalRecord">
        <main>
            <u-sticky>
                <view class="tabs">
                    <u-tabs :list="tabList" :is-scroll="false" :current="active" bg-color="transparent" font-size="26" @change="changeRemitStatus" />
                </view>
            </u-sticky>

            <section class="filter-menu">
                <view>
                    <view class="filter-time">
                        <u-field disabled :border-bottom="false" :value="startDate" placeholder="开始时间" @click="showStartPicker = true" />
                        <u-picker v-model="showStartPicker" title="开始时间" :show-time-tag="false" mode="time" @confirm="onConfirmStart" />
                        <span>—</span>
                        <u-field disabled :border-bottom="false" :value="endDate" placeholder="结束时间" @click="showEndPicker = true" />
                        <u-picker v-model="showEndPicker" title="结束时间" :show-time-tag="false" mode="time" @confirm="onConfirmEnd" />
                    </view>
                    <view class="filter-btn">
                        <button @click="getRecordsList">确 定</button>
                    </view>
                </view>
            </section>

            <no-content v-if="show" />
            <view class="list-data" v-show="!show">
                <section>
                    <section v-for="(r, index) in records" :key="index">
                        <div>
                            <div>
                                <div class="item-l">
                                    <p>
                                        提现金额：<span>{{ r.amount | toDecimal2 }}元</span>
                                    </p>
                                    <p>{{ r.createTime | dateFormat }}</p>
                                </div>
                                <p class="item-r" :class="'remitStatus' + r.remitStatus ">
                                    {{
                                        r.remitStatus == 0
                                        ? "未清算"
                                        : r.remitStatus == 1
                                        ? "已清算"
                                        : r.remitStatus == 2
                                        ? "清算中"
                                        : "清算失败"
                                    }}
                                </p>
                            </div>
                            <p v-if="r.remitStatus == 3">
                                失败原因：{{ r.withdrawFailedReason }}
                            </p>
                        </div>
                    </section>
                </section>
            </view>

            <u-loadmore v-if="!show" :status="status" @loadmore="loadmore" />
        </main>
        
        <u-back-top :scrollTop="scrollTop" />

    </div>
</template>

<script>
import { dateFormat, toDecimal2 } from "../../../../static/utils/date";
import { getChannelWithdrawRecord } from "../../../../http/api";

export default {
    name: "WithdrawalRecord",
    filters: {
        dateFormat,
        toDecimal2
    },
    data() {
        return {
            remitStatus: null,
            active: 0,
            showStartPicker: false,
            showEndPicker: false,
            startDate: dateFormat(new Date()).substring(0, 10),
            endDate: dateFormat(new Date()).substring(0, 10),
            show: false,
            records: [],
            status: 'loading',
            page: null,
            scrollTop: 0,
            tabList: [{ name: '全部' }, { name: '未清算' }, { name: '已清算' }, { name: '清算中' }, { name: '清算失败' }]
        };
    },
    onLoad() {
        this.getRecordsList();
    },
    onPageScroll(e) {
        this.scrollTop = e.scrollTop;
    },
    onReachBottom() {
        this.loadmore()
    },
    methods: {
        getRecordsList() {
            this.status = 'loading'
            this.changeRemitStatusType();
            getChannelWithdrawRecord({
                payChannelNo: this.$Route.query.payChannelNo,
                startDate: this.startDate == "" ? null : this.startDate,
                endDate: this.endDate == "" ? null : this.endDate,
                remitStatus: this.remitStatus,
                pageNumber: 1,
                pageSize: 10,
            }).then((res) => {
                if (res.code == "00") {
                    if (res.data.list.length != 0) {
                        this.show = false;
                        this.page = res.data.total;
                        this.records = res.data.list;
                        uni.pageScrollTo({
                            scrollTop: 0
                        });
                        if (this.records.length >= this.page) {
                            // 数据全部加载完成
                            this.status = 'nomore';
                        } else {
                            this.status = 'loadmore';
                        }
                    } else {
                        this.show = true;
                    }
                }
            });
        },
        changeRemitStatus(index) {
            this.active = index
            this.getRecordsList();
        },
        changeRemitStatusType() {
            this.remitStatus = this.active == 0 ? null : this.active - 1;
        },
        onConfirmStart(val) {
            this.startDate = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.showStartPicker = false;
        },
        onConfirmEnd(val) {
            this.endDate = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.showEndPicker = false;
        },
        loadmore() {
            if (this.status == 'nomore') return;
            this.status = 'loading'
            getChannelWithdrawRecord({
                payChannelNo: this.$Route.query.payChannelNo,
                startDate: this.startDate == "" ? null : this.startDate,
                endDate: this.endDate == "" ? null : this.endDate,
                remitStatus: this.remitStatus,
                pageNumber:
                    this.records.length % 10 > 0
                        ? Math.ceil(this.records.length / 10)
                        : this.records.length / 10 + 1,
                pageSize: 10,
            }).then((res) => {
                if (res.code == "00") {
                    if (res.data.list.length != 0) {
                        this.show = false;
                        this.page = res.data.total;
                        res.data.list.forEach((i) => {
                            this.records.push(i);
                        });
                    } else {
                        this.show = true;
                    }
                    if (this.records.length >= this.page) {
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
#withdrawalRecord {
    main {
        .tabs {
            background-color: #fff;
            border-bottom: 1rpx solid #e5e5e5;
        }
        .filter-menu {
            padding: 20rpx 30rpx;
            background-color: #fff;
            > view {
                background-color: #f3f5f7;
                border-radius: 14rpx;
            }
            .filter-time {
                display: flex;
                align-items: center;
                /deep/ .u-label {
                    display: none;
                }
                /deep/ .u-field__input-wrap {
                    text-align: center;
                }
            }
            .filter-btn {
                padding: 20rpx 80rpx;
                button {
                    height: 66rpx;
                    line-height: 66rpx;
                    background-color: #4a67d6;
                    color: white;
                    border: none;
                    font-size: 28rpx;
                    border-radius: 30rpx;
                }
            }
        }
        .list-data {
            > section {
                padding: 0 30rpx;
                background-color: white;
                > section {
                    padding: 20rpx 0;
                    display: flex;
                    align-items: center;
                    position: relative;
                    border-bottom: 2rpx solid #e5e5e5;
                    &:nth-of-type(1) {
                        margin-top: 10rpx;
                    }
                    > section {
                        text-align: left;
                        flex: 1;
                        > p {
                            &:nth-of-type(1) {
                                font-size: 1.1rem;
                                margin: 0 0 10rpx 0;
                            }
                            &:nth-of-type(2),
                            &:nth-of-type(3),
                            &:nth-of-type(4) {
                                font-size: 0.8rem;
                                color: #9b9b9b;
                                margin: 0;
                            }
                        }
                    }
                    > div {
                        flex: 1;
                        > div {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            text-align: left;
                            .item-l {
                                p {
                                    margin: 0;
                                    &:nth-of-type(1) {
                                        > span {
                                            color: #ce0010;
                                            margin: 0 10rpx;
                                            font-weight: 500;
                                        }
                                    }
                                    &:nth-of-type(2) {
                                        margin-top: 6rpx;
                                        color: #999999;
                                        font-size: 24rpx;
                                    }
                                    &:nth-of-type(3),
                                    &:nth-of-type(4) {
                                        font-size: 0.85rem;
                                        color: #9b9b9b;
                                    }
                                }
                            }
                            .item-r {
                                width: 138rpx;
                                height: 38rpx;
                                line-height: 38rpx;
                                text-align: center;
                                border-radius: 6rpx;
                                font-size: 24rpx;
                                color: #fff;
                            }
                            .remitStatus0 {
                                background-color: #ffcd00;
                            }
                            .remitStatus1 {
                                background-color: #004ea9;
                            }
                            .remitStatus2 {
                                background-color: #999999;
                            }
                            .remitStatus3 {
                                background-color: #f3f5f7;
                                color: #989a9c;
                            }
                        }
                        > p {
                            text-align: left;
                            margin: 6rpx 0;
                            font-size: 26rpx;
                            color: rgb(75, 73, 73);
                        }
                    }
                }
            }
        }
    }
}
</style>