<template>
    <div id="merchantsAppDownload">
        <main>
            <div class="content">
                <view>
                    <u-field disabled :border-bottom="false" label="渠道" placeholder="请选择渠道" right-icon="arrow-down-fill" :value="channel" @click="showChannels" />
                    <u-select v-model="channelsPicker" mode="single-column" :list="channels" value-name="val" label-name="val" @confirm="channelConfirm" />
                </view>

                <section v-if="showQr">
                    <h2>APP下载</h2>
                    <div>
                        <create-qrcode ref="qrcode" :val="qrUrl" :size="320" />
                    </div>
                </section>
            </div>
            <image class="bg" src="../../../static/images/home/<USER>" mode="scaleToFill" />
        </main>
    </div>
</template>

<script>
import { getChannel, getMerchantUrl } from "../../../http/api";
import CreateQrcode from "tki-qrcode"

export default {
    name: "MerchantsAppDownload",
    components: {
        CreateQrcode
    },
    data() {
        return {
            channelsPicker: false,
            channels: [],
            channelCodes: [],
            channel: "",
            showQr: false,
            qrUrl: "",
        };
    },
    methods: {
        showChannels() {
            this.channels = [];
            this.channelCodes = [];
            getChannel().then((res) => {
                if (res.code == "00") {
                    if (res.data.length != 0) {
                        res.data.forEach((i) => {
                            this.channels.push({ val: i.name });
                            this.channelCodes.push(i.code);
                        });
                        this.channelsPicker = true;
                    } else {
                        uni.showToast({
                            title: '暂无可选渠道！',
                            icon: 'none'
                        });
                    }
                }
            });
        },
        channelConfirm(val) {
            this.channel = val[0].value;
            this.channelsPicker = false;
            getMerchantUrl({
                channelCode: this.channelCodes[this.channels.findIndex(i => i.val == this.channel)],
            }).then((res) => {
                if (res.code == "00") {
                    this.qrUrl = res.data;
                    this.showQr = true;
                    this.$nextTick(() => {
                        this.$refs.qrcode._makeCode()
                    })
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
#merchantsAppDownload {
    height: 100%;
    > main {
        height: 100%;
        display: flex;
        align-content: center;
        justify-content: center;
        .bg {
            position: absolute;
            z-index: -1;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .content {
            flex: 1;
            margin: 120rpx 30rpx;
            padding: 0 30rpx;
            display: flex;
            flex-direction: column;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 10rpx;
            /deep/ .u-field {
                margin-top: 60rpx;
                border-radius: 8rpx;
                background-color: #fff;
            }
            > section {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                text-align: center;
                > div {
                    background: #fff;
                    border-radius: 20rpx;
                    width: 350rpx;
                    height: 350rpx;
                    /deep/ .tki-qrcode {
                        image {
                            margin-top: 15rpx;
                        }
                    }
                }
                > h2 {
                    letter-spacing: 4rpx;
                    font-weight: 500;
                    font-size: 1.3rem;
                    color: #fff;
                }
            }
        }
    }
}
</style>