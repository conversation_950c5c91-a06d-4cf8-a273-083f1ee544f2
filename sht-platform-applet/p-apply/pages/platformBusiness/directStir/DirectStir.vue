<template>
  <div id="direct-stir">
    <u-form class="u-form" :label-width="250">
      <u-form-item label="下拨方式">
        <u-input type="select" :select-open="stirTypesPicker" label="下拨方式" :value="stirType" @click="stirTypesPicker = true" />
      </u-form-item>
      <u-select v-model="stirTypesPicker" mode="single-column" :list="stirTypes" value-name="val" label-name="val" @confirm="stirTypeConfirm" />

      <u-form-item label="支付通道" prop="payOrgCode">
        <u-input type="select" :select-open="payOrgCodesPicker" placeholder="请选择支付通道" :value="payOrgCode" @click="showPayOrgCodes" />
      </u-form-item>
      <u-select v-model="payOrgCodesPicker" :list="payOrgCodes" label-name="name" value-name="code" @confirm="payOrgCodeConfirm" />

      <u-form-item v-if="!rangeStir" label="选择终端">
        <u-input type="select" :select-open="teminalNosPicker" :value="teminalAmount" @click="selectTeminalNos(true)" />
      </u-form-item>
      <!-- select teminals proup -->
      <u-popup v-if="!rangeStir" mode="bottom" v-model="teminalNosPicker">
        <div class="u-picker__toolbar">
          <text class="picker__cancel" @click="teminalNosPicker = false">取消</text>
          <text class="picker__confirm" @click="selectConfirm">确认</text>
        </div>
        <div class="filter-bysn">
          <u-field v-model="rStirTeminalsParams.terminalNo" label="SN编号" placeholder="输入SN编号查询">
            <template #right>
              <u-button size="mini" type="primary" @click="selectTeminalNos(false)">搜索</u-button>
            </template>
          </u-field>
        </div>
        <scroll-view :scroll-top="scrollTop" style="height: 40vh" scroll-y="true" @scrolltolower="loadmore">
          <checkbox-group @change="checkboxGroupChange">
            <u-cell-group :border="false">
              <label v-for="(t, index) in teminalNos" :key="index">
                <u-cell-item :border-bottom="false" :title="t" :arrow="false">
                  <checkbox style="transform: scale(0.8)" slot="right-icon" :value="t" :checked="teminalNosSelect.length && teminalNosSelect.indexOf(t) != -1" />
                </u-cell-item>
              </label>
            </u-cell-group>
          </checkbox-group>
        </scroll-view>
      </u-popup>

      <u-form-item label="代理商">
        <u-input placeholder="请选择下级代理商" v-model="agent" />
        <u-icon slot="right" name="search" size="40" color="#888" @click="showAgents" />
      </u-form-item>
      <u-select v-model="agentsPicker" mode="single-column" :list="agents" value-name="val" label-name="val" @confirm="agentConfirm" />

      <u-form-item v-if="rangeStir" label="开始终端号">
        <u-input placeholder="请输入开始终端号" v-model="startTerminalNo" @change="getRulesCount" />
      </u-form-item>
      <u-form-item v-if="rangeStir" label="结束终端号">
        <u-input placeholder="请输入结束终端号" v-model="endTerminalNo" @change="getRulesCount" />
      </u-form-item>
      <u-form-item v-if="rangeStir" label="终端数量">
        <u-input placeholder="请输入终端数量" v-model="terminalCount" />
      </u-form-item>

      <footer class="custom-button">
        <button @click="submit">确认</button>
      </footer>
    </u-form>
  </div>
</template>

<script>
import { getTerList, getSubAgents, rangeStir, selectStir } from '../../../../http/api-direct'
import { getChannel } from '../../../../http/api'

export default {
  name: 'DirectStir',
  data() {
    return {
      payOrgCodesPicker: false,
      payOrgCodes: [],
      payOrgCode: '',
      stirTypesPicker: false,
      stirTypes: [{ val: '选择下拨' }, { val: '区间下拨' }],
      stirType: '选择下拨',
      rangeStir: false,
      teminalNosPicker: false,
      teminalNos: [],
      teminalNosSelect: [],
      teminalAmount: '已选下拨终端0台',
      agentsPicker: false,
      agents: [],
      agent: '',
      terminal: {
        payOrgCode: '',
        toAgentCode: '',
        terDoc: '',
        startTerminalNo: '',
        endTerminalNo: '',
        terminalCount: ''
      },
      finished: false,
      total: null,
      rStirTeminalsParams: {
        payOrgCode: '',
        terminalNo: '',
        pageNo: 1, //页数
        pageSize: 20 //每页的条数
      },
      scrollTop: 0
    }
  },
  onLoad() {},
  methods: {
    showPayOrgCodes() {
      this.payOrgCodes = []
      getChannel().then(res => {
        if (res.code == '00') {
          if (res.data.length) {
            this.payOrgCodes = res.data
            this.payOrgCodesPicker = true
          } else {
            this.$u.toast('暂无可选支付通道！')
          }
        }
      })
    },
    payOrgCodeConfirm([{ label, value }]) {
      this.payOrgCode = label
      this.terminal.payOrgCode = this.rStirTeminalsParams.payOrgCode = value
      this.payOrgCodesPicker = false
      this.teminalNosSelect = []
      this.terDoc = ''
      this.teminalAmount = '已选下拨终端0台'
      this.terminal.toAgentCode = this.agent = ''
    },
    stirTypeConfirm(val) {
      const value = val[0].value
      this.stirType = value
      if (value == '选择下拨') {
        this.rangeStir = false
      } else {
        this.rangeStir = true
      }
      this.stirTypesPicker = false
    },
    checkboxGroupChange(e) {
      this.teminalNosSelect = e.detail.value
    },
    selectTeminalNos(isInit) {
      if (!this.payOrgCode) {
        return this.$u.toast('请先选择支付通道！')
      }
      if (isInit) {
        this.rStirTeminalsParams.terminalNo = ''
      }
      this.rStirTeminalsParams.pageNo = 1
      getTerList(this.rStirTeminalsParams).then(res => {
        if (res.code == '00') {
          if (res.data.list.length != 0) {
            this.teminalNos = res.data.list
            this.total = res.data.total
            this.scrollTop = 0
            if (this.teminalNos.length >= this.total) {
              // 数据全部加载完成
              this.finished = true
            } else {
              this.finished = false
            }
            this.teminalNosPicker = true
          } else {
            uni.showToast({
              title: '暂无可下拨终端！',
              icon: 'none'
            })
          }
        }
      })
    },
    loadmore() {
      if (this.finished) return
      this.rStirTeminalsParams.pageNo += 1
      getTerList(this.rStirTeminalsParams).then(res => {
        if (res.code == '00') {
          this.total = res.data.total
          res.data.list.forEach(i => {
            this.teminalNos.push(i)
          })
          if (this.teminalNos.length >= this.total) {
            // 数据全部加载完成
            this.finished = true
          }
        }
      })
    },
    selectConfirm() {
      this.teminalAmount = '已选下拨终端' + this.teminalNosSelect.length + '台'
      if (this.teminalNosSelect.length != 0) {
        this.terminal.terDoc = ''
        this.teminalNosSelect.forEach(i => {
          if (this.terminal.terDoc == '') {
            this.terminal.terDoc = i
          } else {
            this.terminal.terDoc = this.terminal.terDoc + ',' + i
          }
        })
      }
      this.teminalNosPicker = false
    },
    showAgents() {
      this.agents = []
      getSubAgents(this.agent).then(res => {
        if (res.code == '00') {
          if (res.data.length != 0) {
            this.agents = res.data.map(i => {
              return { val: i.name + '（' + i.agentCode + '）' }
            })
            this.agentsPicker = true
          } else {
            uni.showToast({
              title: '暂无下级代理商！',
              icon: 'none'
            })
          }
        }
      })
    },
    agentConfirm(val) {
      const value = val[0].value
      const bracketStart = value.lastIndexOf('（')
      const bracketEnd = value.lastIndexOf('）')

      this.agent = value.substring(0, bracketStart)
      this.terminal.toAgentCode = value.substring(bracketStart + 1, bracketEnd)
      this.agentsPicker = false
    },
    submit() {
      if (this.terminal.toAgentCode !== '') {
        if (this.stirType == '选择下拨') {
          if (this.terminal.terDoc == '') {
            uni.showToast({
              title: '终端未选择！',
              icon: 'none'
            })
          } else {
            this.stir()
          }
        } else {
          if (!this.payOrgCode) {
            return this.$u.toast('请选择支付通道！')
          }
          if (
            (!/^[0-9]+$/.test(this.terminal.startTerminalNo) || !/^[0-9]+$/.test(this.terminal.endTerminalNo)) &&
            (this.terminal.startTerminalNo != this.terminal.endTerminalNo || this.terminal.terminalCount != 1)
          ) {
            uni.showToast({
              title: '终端编号不为纯数字时只能单台下拨！',
              icon: 'none'
            })
          } else {
            this.stir()
          }
        }
      } else {
        uni.showToast({
          title: '未选择代理商！',
          icon: 'none'
        })
      }
    },
    stir() {
      const stir = this.stirType == '选择下拨' ? selectStir : rangeStir
      stir(this.terminal).then(res => {
        if (res.code == '00') {
          uni.showToast({
            title: res.message,
            icon: 'none'
          })
          setTimeout(() => {
            this.$Router.back(11)
          }, 1000)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
#direct-stir {
  padding-top: 20rpx;
  .u-form {
    /deep/ .u-form-item {
      padding: 20rpx 28rpx;
      line-height: 0;
      background-color: #fff;

      input {
        height: 48rpx;
        min-height: 48rpx !important;
      }
    }
  }
  .u-picker__toolbar {
    display: flex;
    justify-content: space-between;
    padding: 26rpx 32rpx;
    border-bottom: 2rpx soild #e5e5e5;
    .picker__confirm {
      color: #576b95;
    }
  }
  /deep/ checkbox .wx-checkbox-input {
    border-radius: 50% !important;
  }

  /deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked {
    color: #004ea9;
  }
  .custom-button {
    margin-top: 100rpx;
  }
}
</style>