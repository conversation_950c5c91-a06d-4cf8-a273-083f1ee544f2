<template>
  <div id="cashBackModel">
    <main>
      <view class="filter-header">
        <u-dropdown ref="uDropdown" :close-on-click-mask="false" :close-on-click-self="false" @open="openFilterHeader">
          <u-dropdown-item title="筛选">
            <view class="filter-form">
              <u-field
                disabled
                label="政策类型"
                placeholder="请选择政策类型"
                right-icon="arrow-down-fill"
                :value="modelTypeVal"
                @click="selectModelType"
              />
              <u-field
                label="支付通道"
                placeholder="选择支付通道"
                :value="payOrgCode"
                @click="showPayOrgCodes"
                :right-icon="`arrow-${payOrgCodesPicker ? 'up' : 'down'}`"
                disabled
              />

              <div class="btnTools">
                <u-button size="medium" @click="toReset">重置</u-button>
                <u-button color="#004ea9" type="primary" size="medium" @click="$refs.uDropdown.close(), getPageData(true)">确定</u-button>
              </div>
            </view>
          </u-dropdown-item>
        </u-dropdown>
        <view v-if="agentLevel == 1" class="add-model" @click="addModel">新增政策</view>
      </view>
      <u-sticky>
        <option-tab opts="本级,服务商" :current="currentTab" @select="select" />
      </u-sticky>
      <no-content v-if="show" />

      <section class="list-data" v-show="!show">
        <div class="model-item" v-for="(m, index) in modelList" :key="index">
          <p>
            政策名称: <span style="font-weight: 500">{{ m.modelName }}</span>
          </p>
          <p v-if="m.payOrgCode">
            支付通道: <span>{{ m.payOrgCode | orgCodeFormat }}</span>
          </p>
          <!-- <p>
                        返现类型: <span>{{ m.cashbackType == 1 ? '优惠费率返现' : m.cashbackType == 2 ? '商户提现手续费' : m.cashbackType == 3 ? '商户管理费' : m.cashbackType == 4 ? '网申信用卡核卡' : m.cashbackType == 5 ? '网申信用卡激活': '' }}</span>
                    </p> -->
          <p>
            计费方式: <span>{{ m.feeType == 1 ? '固定金额' : m.feeType == 2 ? '百分比' : '--' }}</span>
          </p>
          <p>
            返现{{ m.feeType == 1 ? '金额(元)' : m.feeType == 2 ? '比例(%)' : '' }}: <span>{{ m.cashbackAmount }}</span>
          </p>

          <p v-if="params.selType == 1">
            代理商名称: <span>{{ m.agentName }}</span>
          </p>
          <p v-if="params.selType == 1">
            代理商编号: <span>{{ m.agentCode }}</span>
          </p>
          <div class="tools-btn">
            <span
              v-if="params.selType == 0 && optPmsState"
              @click="batchSetRule(m.modelType, m.cashbackAmount, m.cashbackType, m.feeType, m.payOrgCode)"
              >开通服务商</span
            >
            <span
              v-if="params.selType == 0 && agentLevel == 1"
              @click="openEditCashBackRule(m.id, m.modelType, m.cashbackAmount, m.modelName, m.feeType)"
              >编辑</span
            >
            <span v-if="params.selType == 1 && agentLevel == 1" class="close-polic" @click="deleteCashBackRule(m.id, m.modelName)"
              >关闭政策</span
            >
            <span v-if="params.selType == 1" @click="openEditCashBack(m.id, m.modelType, m.cashbackAmount, m.feeType)">编辑</span>
          </div>
        </div>
      </section>

      <u-loadmore v-if="!show" :status="status" @loadmore="getPageData(false)" />
    </main>
    <u-select v-model="showModelType" mode="single-column" :list="modelType" @confirm="modelTypeConfirm" />
    <u-select
      v-model="payOrgCodesPicker"
      :list="payOrgCodes"
      label-name="name"
      value-name="code"
      @confirm="payOrgCodeConfirm"
      :default-value="payOrgCode ? [payOrgCodes.findIndex(i => i.name === payOrgCode)] : []"
    />

    <!-- 批量开通 -->
    <u-popup v-model="showBatchSetRule" mode="bottom">
      <div class="u-picker__toolbar">
        <text class="picker__cancel" @click="showBatchSetRule = false">取消</text>
        <text class="picker__confirm" @click="submitBatch">确认</text>
      </div>
      <!-- <p class="tips" style="padding-bottom:0">提示: 此金额为同政策类型下直属下级代理返现金额最小值!</p> -->
      <u-field
        v-if="batchOpen.feeType === 1"
        label="返现金额"
        :placeholder="`请填写返现金额(不得大于${cashbackAmountMax}元)`"
        v-model="batchOpen.cashbackAmount"
      />
      <u-field v-else label="返现比例" :placeholder="`单位%, 范围0-${cashbackAmountMax}`" v-model="batchOpen.cashbackAmount" />
      <p class="tips">已开通代理商不可选中</p>
      <scroll-view style="height: 36vh" scroll-y="true">
        <checkbox-group @change="checkboxGroupChange">
          <u-cell-group :border="false">
            <label v-for="(t, index) in dirAgentList" :key="index">
              <u-cell-item :border-bottom="false" border-top :title="t.agentCode + '-' + t.agentName" :arrow="false">
                <checkbox
                  :disabled="t.openState == 1"
                  style="transform: scale(0.8)"
                  slot="right-icon"
                  :value="t.agentCode"
                  :checked="batchOpen.agentCodeList.length && batchOpen.agentCodeList.indexOf(t.agentCode) != -1"
                />
              </u-cell-item>
            </label>
          </u-cell-group>
        </checkbox-group>
      </scroll-view>
    </u-popup>

    <!-- 一代修改返现规则 -->
    <u-modal ref="uModalRule" v-model="showEditCashBackRule" show-cancel-button title="修改" @confirm="submitCashBackRule" async-close>
      <u-form :model="editCashBackRule" ref="uFormRule" class="u-form" label-width="auto">
        <section v-if="showEditCashBackRule">
          <u-form-item label="政策名称" prop="modelName">
            <u-input v-model="editCashBackRule.modelName" placeholder="请输入政策名称" />
          </u-form-item>
          <u-form-item v-if="feeType === 1" label="返现金额(元)" prop="cashbackAmount">
            <u-input type="digit" v-model="editCashBackRule.cashbackAmount" placeholder="请输入返现金额" />
          </u-form-item>
          <u-form-item v-else label="返现比例" prop="cashbackAmount">
            <u-input type="digit" v-model="editCashBackRule.cashbackAmount" placeholder="单位%, 范围0-100" />
          </u-form-item>
        </section>
      </u-form>
    </u-modal>

    <!-- 修改下级返现金额 -->
    <u-modal ref="uModal" v-model="showEditCashBack" show-cancel-button title="修改" @confirm="submitCashBack" async-close>
      <u-form :model="editCashBack" ref="uForm" class="u-form" label-width="auto">
        <section v-if="showEditCashBack">
          <u-form-item v-if="feeType === 1" label="返现金额(元)" prop="cashbackAmount">
            <u-input type="digit" v-model="editCashBack.cashbackAmount" placeholder="请输入返现金额" />
          </u-form-item>
          <u-form-item v-else label="返现比例" prop="cashbackAmount">
            <u-input type="digit" v-model="editCashBack.cashbackAmount" placeholder="单位%, 范围0-100" />
          </u-form-item>
        </section>
      </u-form>
    </u-modal>
  </div>
</template>

<script>
import OptionTab from '../../../../components/OptionTab.vue';

import {
  getRulesByPage,
  batchSetRule,
  getDirAgentList,
  batchSetAmount,
  getOptPmsState,
  delRuleInfoById,
  getAllRules,
  editRule,
  getChannel
} from '../../../../http/api';

const pattern = /^([1-9]\d{0,}|0|0\.\d{0,1}[1-9]|[1-9]\d{0,}\.\d{0,1}[1-9])$/;
const pattern2 = /^(\d{1,2}(\.\d{1,4})?|100)$/;

export default {
  name: 'CashBackModel',
  components: {
    OptionTab
  },
  data() {
    return {
      payOrgCodesPicker: false,
      payOrgCodes: [],
      payOrgCode: '',
      showBatchSetRule: false,
      total: null,
      show: false,
      modelList: [],
      dirAgentList: [],
      batchOpen: {
        feeType: '', // 计费方式 1-固定金额，2-百分比(显示 单位% 范围0-100)
        cashbackAmount: '', //返现金额 ,必填
        modelType: '', //政策类型,必填
        businessModelType: '',
        payOrgCode: '',
        agentCodeList: [] //选择要开通的代理商编号列表,必填
      },
      cashbackAmountMax: '99999',
      editCashBack: {
        id: '',
        cashbackAmount: '', //返现金额,必填
        modelType: '' //政策类型,必填
      },
      editCashBackRule: {
        id: '',
        modelName: '',
        cashbackAmount: '', //返现金额,必填
        modelType: '' //政策类型,必填
      },
      feeType: '',
      modelTypeVal: '',
      modelType: [],
      showModelType: false,
      showEditCashBack: false,
      showEditCashBackRule: false,
      oldParams: null,
      optPmsState: false,
      params: {
        pageNo: 1,
        pageSize: 10,
        modelType: '',
        payOrgCode: '',
        selType: 0 //查询类型 默认为:0  0-表示查询自己规则 1-查询直属代理 2-全部下级
      },
      status: 'loading',
      currentTab: 0
    };
  },
  computed: {
    agentLevel() {
      return this.$store.state.userInfo.agentLevel;
    },
    rules() {
      return {
        modelName: [{ required: true, message: '必填' }],
        cashbackAmount: [
          { required: true, message: '必填' },
          this.feeType === 1
            ? { pattern, message: '必须符合金额规则且不得超过2位小数' }
            : { pattern: pattern2, message: '只允许范围0-100内最多4位小数的数字' }
        ]
      };
    }
  },
  onLoad() {
    getOptPmsState().then(res => {
      if (res.code == '00') {
        this.optPmsState = !res.data.optPmsState;
      }
    });
  },
  onShow() {
    this.oldParams = null;
    this.getPageData(true);
  },
  onReachBottom() {
    this.getPageData(false);
  },
  methods: {
    showPayOrgCodes() {
      this.payOrgCodes = [];
      getChannel().then(res => {
        if (res.code == '00') {
          if (res.data.length) {
            this.payOrgCodes = [{ name: '全部', code: '' }, ...res.data];
            this.payOrgCodesPicker = true;
          } else {
            this.$u.toast('暂无可选支付通道！');
          }
        }
      });
    },
    payOrgCodeConfirm([{ label, value }]) {
      this.payOrgCode = label === '全部' ? '' : label;
      this.params.payOrgCode = value;
      this.payOrgCodesPicker = false;
    },
    select(data) {
      if (this.currentTab == data) return;
      this.params.selType = this.currentTab = data;
      this.oldParams = null;
      this.getPageData(true);
    },
    modelTypeConfirm(val) {
      const { label, value } = val[0];
      this.modelTypeVal = label;
      this.params.modelType = value;
      this.showModelType = false;
    },
    selectModelType() {
      getAllRules().then(res => {
        if (res.code == '00') {
          this.modelType = [];
          const { cashbackRuleList = [] } = res.data;
          if (cashbackRuleList.length) {
            cashbackRuleList.forEach(m => {
              this.modelType.push({ label: m.modelName, value: m.modelType });
            });
          }
          this.showModelType = true;
        }
      });
    },
    checkboxGroupChange(e) {
      this.batchOpen.agentCodeList = e.detail.value;
    },
    openFilterHeader() {
      this.oldParams = JSON.parse(JSON.stringify(this.params));
    },
    toReset() {
      this.params.modelType = this.modelTypeVal = '';
      this.params.payOrgCode = this.payOrgCode = '';
    },
    addModel() {
      this.$Router.push({ name: 'AddCashBackModel' });
    },
    submitCashBackRule() {
      this.$refs.uModalRule.clearLoading();
      this.$refs.uFormRule.validate(valid => {
        if (valid) {
          editRule(this.editCashBackRule).then(res => {
            if (res.code == '00') {
              uni.showModal({
                showCancel: false,
                content: '修改成功'
              });
              this.oldParams = null;
              this.getPageData(true);
            }
            this.showEditCashBackRule = false;
          });
        }
      });
    },
    submitCashBack() {
      this.$refs.uModal.clearLoading();
      this.$refs.uForm.validate(valid => {
        if (valid) {
          batchSetAmount(this.editCashBack).then(res => {
            if (res.code == '00') {
              uni.showModal({
                showCancel: false,
                content: '修改成功'
              });
              this.oldParams = null;
              this.getPageData(true);
            }
            this.showEditCashBack = false;
          });
        }
      });
    },
    openEditCashBackRule(id, modelType, cashbackAmount, modelName, feeType) {
      this.editCashBackRule = Object.assign(this.editCashBackRule, { id, modelType, cashbackAmount, modelName });
      this.feeType = feeType;
      this.showEditCashBackRule = true;
      this.$nextTick(() => {
        this.$refs.uFormRule.setRules(this.rules);
        this.$refs.uFormRule.resetFields();
      });
    },
    openEditCashBack(id, modelType, cashbackAmount, feeType) {
      this.editCashBack = Object.assign(this.editCashBack, { id, modelType, cashbackAmount });
      this.feeType = feeType;
      this.showEditCashBack = true;
      this.$nextTick(() => {
        this.$refs.uForm.setRules(this.rules);
        this.$refs.uForm.resetFields();
      });
    },
    deleteCashBackRule(id, name) {
      uni.showModal({
        content: '此功能关闭该二级代理及其所有服务商，请确认！',
        success: res => {
          if (res.confirm) {
            delRuleInfoById(id).then(res => {
              if (res.code == '00') {
                uni.showToast({
                  title: '政策已关闭',
                  icon: 'none'
                });
                this.oldParams = null;
                this.getPageData(true);
              }
            });
          }
        }
      });
    },
    submitBatch() {
      if (this.batchOpen.feeType === 1) {
        if (
          !this.batchOpen.cashbackAmount ||
          Number(this.batchOpen.cashbackAmount) > Number(this.cashbackAmountMax) ||
          Number(this.batchOpen.cashbackAmount) < 0
        ) {
          return uni.showModal({
            showCancel: false,
            content: `返现金额必填且不得大于${this.cashbackAmountMax}小于0`
          });
        }
      } else {
        if (
          !this.batchOpen.cashbackAmount ||
          !pattern2.test(this.batchOpen.cashbackAmount) ||
          Number(this.batchOpen.cashbackAmount) > Number(this.cashbackAmountMax)
        ) {
          return uni.showModal({
            showCancel: false,
            content: `返现比例必填且只允许范围0-${this.cashbackAmountMax}内最多4位小数的数字`
          });
        }
      }

      if (!this.batchOpen.agentCodeList.length) {
        return uni.showToast({
          title: '未选择代理商!',
          icon: 'none'
        });
      }
      batchSetRule(this.batchOpen).then(res => {
        if (res.code == '00') {
          uni.showModal({
            showCancel: false,
            content: '开通成功'
          });
          this.oldParams = null;
          this.getPageData(true);
        }
        this.showBatchSetRule = false;
      });
    },
    batchSetRule(modelType, cashbackAmount, businessModelType, feeType, payOrgCode) {
      this.cashbackAmountMax = cashbackAmount;
      this.batchOpen = Object.assign(this.batchOpen, {
        modelType,
        businessModelType,
        cashbackAmount: '',
        agentCodeList: [],
        feeType,
        payOrgCode
      });
      getDirAgentList({modelType, businessModelType, payOrgCode}).then(res => {
        if (res.code == '00') {
          this.dirAgentList = res.data;
          this.showBatchSetRule = true;
        }
      });
    },
    getPageData(isInquire) {
      if (!isInquire && this.status == 'nomore') return;
      if (isInquire && JSON.stringify(this.oldParams) == JSON.stringify(this.params)) {
        return;
      }
      this.status = 'loading';
      this.params.pageNo = isInquire ? 1 : this.params.pageNo + 1;
      getRulesByPage(this.params).then(res => {
        if (res.code == '00') {
          if (res.data.list.length != 0) {
            this.show = false;
            this.total = res.data.total;

            !isInquire
              ? res.data.list.forEach(i => {
                  this.modelList.push(i);
                })
              : (this.modelList = res.data.list);
            isInquire &&
              uni.pageScrollTo({
                scrollTop: 0
              });
          } else {
            this.show = true;
          }
          if (this.modelList.length >= this.total) {
            // 数据全部加载完成
            this.status = 'nomore';
          } else {
            this.status = 'loadmore';
          }
        }
      });
    }
  }
};
</script>

<style lang="less">
@import '../../../../static/css/game.less';

#cashBackModel {
  height: 100%;
  main {
    .filter-header {
      position: relative;
      .add-model {
        position: absolute;
        right: 40rpx;
        top: 50%;
        z-index: 12;
        transform: translateY(-50%);
        color: #004ea9;
      }
    }
    .list-data {
      padding: 20rpx 30rpx 0;
      .model-item {
        background: #fff;
        border-radius: 20rpx;
        padding: 20rpx 30rpx;
        margin-bottom: 20rpx;
        > p {
          margin: 0 0 10rpx;
          color: #888;
          > span {
            color: #444;
          }
        }
        > span {
          font-size: 24rpx;
          color: #999;
        }
        .tools-btn {
          display: flex;
          padding-top: 20rpx;
          border-top: 1px solid #e5e5e5;
          color: #004ea9;
          > span {
            flex: 1;
            text-align: center;
            border-right: 1px solid #e5e5e5;
            &:last-of-type {
              border: none;
            }
          }
          .close-policy {
            color: #e54d42;
          }
        }
      }
    }
  }
  .u-picker__toolbar {
    display: flex;
    justify-content: space-between;
    padding: 26rpx 32rpx;
    border-bottom: 1px soild #e5e5e5;
    .picker__confirm {
      color: #576b95;
    }
  }
  /deep/ checkbox .wx-checkbox-input {
    border-radius: 50% !important;
  }

  /deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked {
    color: #004ea9;
  }
  .tips {
    padding: 3vw;
    color: darkgray;
    font-size: 24rpx;
    span {
      color: #333;
    }
  }

  form {
    button {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 50%;
      z-index: 1;
    }
  }

  .u-form {
    section {
      padding: 0 30rpx;
    }
  }
  /deep/ .u-cell {
    padding: 20rpx 30rpx;
  }
}
</style>
