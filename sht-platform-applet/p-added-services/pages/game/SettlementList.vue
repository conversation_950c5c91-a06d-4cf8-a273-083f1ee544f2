<template>
    <div id="settlementList">
        <main>
            <u-sticky>
                <header class="filter-header">
                    <u-dropdown ref="uDropdown" :close-on-click-mask="false" :close-on-click-self="false" @open="openFilterHeader">
                        <u-dropdown-item title="筛选">
                            <view class="filter-form">
                                <u-field v-model="params.agentName" label="代理商名称" placeholder="请输入代理商名称" />
                                <u-field v-model="params.agentCode" label="代理商编号" placeholder="请输入代理商编号" />
                                <u-field v-model="params.channelOrderNo" label="渠道订单号" placeholder="请输入渠道订单号" />
                                <u-radio-group v-model="params.accountEntryStatus">
                                    <u-radio name="0" shape="square">未入账</u-radio>
                                    <u-radio name="2" shape="square">入账成功</u-radio>
                                    <u-radio name="1" shape="square">入账失败</u-radio>
                                    <u-radio :name="null" shape="square">全部</u-radio>
                                </u-radio-group>
                                <section class="filtrate-time">
                                    <u-field disabled :border-bottom="false" :value="startTime" placeholder="开始时间" @click="showStartPicker = true" />
                                    <span>—</span>
                                    <u-field disabled :border-bottom="false" :value="endTime" placeholder="结束时间" @click="showEndPicker = true" />
                                </section>
                                <div class="btnTools">
                                    <u-button size="medium" @click="toReset">重置</u-button>
                                    <u-button size="medium" color="#004ea9" type="primary" @click="$refs.uDropdown.close(), getPageData(true)">确定</u-button>
                                </div>
                            </view>
                        </u-dropdown-item>
                    </u-dropdown>
                </header>
            </u-sticky>

            <no-content v-if="show" />

            <view class="list-data" v-show="!show">
                <div class="card" v-for="(m, index) in pageData" :key="index">
                    <div class="tag">
                        <u-tag size="mini" :type="m.accountEntryStatus == 0 ? 'primary' : m.accountEntryStatus == 2 ? 'success' : m.accountEntryStatus == 1 ? 'error' : ''" :text="m.accountEntryStatus == 0 ? '未入账' : m.accountEntryStatus == 2 ? '入账成功' : m.accountEntryStatus == 1 ? `入账失败` : ''" />
                    </div>
                    <div class="right">
                        <span>{{ m.createTime | dateFormat }}</span>
                        <p>结算金额(元):<span class="money">{{ m.settleAmount | toDecimal2 }} </span></p>
                        <p>代理商名称:<span>{{ m.agentName }} </span></p>
                        <p>代理商编号:<span>{{ m.agentCode }} </span></p>
                        <p>渠道订单号: <span>{{ m.channelOrderNo }}</span></p>
                    </div>
                </div>
            </view>

            <u-loadmore v-if="!show" :status="status" @loadmore="getPageData(false)" />

            <u-picker v-model="showStartPicker" title="开始时间" :show-time-tag="false" mode="time" @confirm="onConfirmStart" />
            <u-picker v-model="showEndPicker" title="结束时间" :show-time-tag="false" mode="time" @confirm="onConfirmEnd" />
        </main>
    </div>
</template>

<script>
import { dateFormat, toDecimal2 } from "../../../static/utils/date";
import { agentSettle } from "../../../http/api";

export default {
    name: "SettlementList",
    filters: {
        dateFormat,
        toDecimal2
    },
    data() {
        return {
            total: null,
            show: false,
            showStartPicker: false,
            showEndPicker: false,
            startTime: "",
            endTime: "",
            oldParams: null,
            params: {
                startTime: "",
                endTime: "",
                pageNo: 1,
                pageSize: 10,
                agentName: "",
                agentCode: "",
                agentType: null,
                channelOrderNo: "",
                postType: null,
                accountEntryStatus: null,
            },

            pageData: [],
            status: 'loading'
        };
    },
    onLoad() {
        this.getPageData(true);
    },
    onReachBottom() {
        this.getPageData(false);
    },
    methods: {
        openFilterHeader() {
            this.oldParams = JSON.parse(JSON.stringify(this.params))
        },
        toReset() {
            var oldData = this.$options.data();
            this.params = oldData.params;
            this.startTime = this.endTime = "";
        },
        onConfirmStart(val) {
            this.startTime = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.showStartPicker = false;
        },
        onConfirmEnd(val) {
            this.endTime = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.showEndPicker = false;
        },
        getPageData(isInquire) {
            this.params.startTime =
                this.startTime == "" ? null : this.startTime + " 00:00:00";
            this.params.endTime =
                this.endTime == "" ? null : this.endTime + " 23:59:59";

            if (isInquire && JSON.stringify(this.oldParams) == JSON.stringify(this.params)) {
                return;
            }
            if (!isInquire && this.status == 'nomore') return;

            this.status = 'loading'
            this.params.pageNo = isInquire ? 1 : this.params.pageNo + 1;

            agentSettle(this.params).then((res) => {
                if (res.code == "00") {
                    if (res.data.list.length != 0) {
                        this.show = false;
                        this.total = res.data.total;
                        !isInquire
                            ? res.data.list.forEach((i) => {
                                this.pageData.push(i);
                            })
                            : (this.pageData = res.data.list);
                        isInquire && uni.pageScrollTo({
                            scrollTop: 0,
                        });
                    } else {
                        this.show = true;
                    }

                    if (this.pageData.length >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore';
                    } else {
                        this.status = 'loadmore';
                    }
                }
            });
        },
    }
};
</script>

<style lang="less">
@import "../../../static/css/game.less";

#settlementList {
    main {
        .list-data {
            padding: 20rpx 30rpx 0;
            .card {
                background: #fff;
                border-radius: 20rpx;
                padding: 32rpx 32rpx 2rpx;
                margin-bottom: 20rpx;
                position: relative;
                .tag {
                    position: absolute;
                    top: 24rpx;
                    right: 30rpx;
                }
                .right {
                    font-size: 24rpx;
                    > span {
                        color: #999999;
                        margin-bottom: 30rpx;
                        display: inline-block;
                    }
                    > p {
                        color: #666666;
                        margin: 0 0 30rpx 0;
                        > span {
                            color: #222222;
                        }
                        .money {
                            color: #ce0010;
                            font-weight: 600;
                        }
                    }
                }
            }
        }
    }
}
</style>