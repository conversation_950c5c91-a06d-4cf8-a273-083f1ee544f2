<template>
    <view id="gameList">
        <u-sticky>
            <view class="filter-header">
                <u-dropdown ref="uDropdown" :close-on-click-mask="false" :close-on-click-self="false" @open="openFilterHeader">
                    <u-dropdown-item title="筛选">
                        <view class="filter-form">
                            <u-field v-model="params.gameName" label="游戏名称" placeholder="请输入游戏名称" />
                            <div class="btnTools">
                                <u-button size="medium" @click="toReset">重置</u-button>
                                <u-button color="#004ea9" type="primary" size="medium" @click="$refs.uDropdown.close(), getPageData(true)">确定</u-button>
                            </div>
                        </view>
                    </u-dropdown-item>
                </u-dropdown>
            </view>
        </u-sticky>

        <main>
            <no-content v-if="show" />

            <view class="list-data" v-show="!show">
                <view v-for="(m, index) in pageData" :key="index" @click="toAppUrl(m.gameUrl)" class="game-box">
                    <div class="remark" v-if="m.gameLevel != 4">
                        <image :class="m.gameLevel == 1 ? 'rateClass' : ''" :src="m.gameLevel == 1 ? require('../../static/images/boil.png') : m.gameLevel == 2 ? require('../../static/images/hot.png') : m.gameLevel == 3 ? require('../../static/images/recommend.png') : ''" alt="" />
                    </div>
                    <div class="card">
                        <div class="c-top">
                            <image class="game-icon" :src="m.icon" alt="" />
                            <div>
                                <p>
                                    游戏名称: <span>{{ m.gameName }}</span>
                                </p>
                                <p>
                                    创建时间: <span>{{ m.createTime | dateFormat }}</span>
                                </p>
                                <p @click.stop="copyUrl(m.gameUrl)">复制推广链接</p>
                            </div>
                        </div>
                    </div>
                    <view class="shareBtnTools">
                        <div class="ratebili">
                            <p class="first-rate" v-if="agentLevel == 1">
                                一代返佣比例: <span>{{ m.channelRate }}%</span>
                            </p>
                            <p>
                                推广返佣比例: <span>{{ m.lowerChannelRate }}%</span>
                            </p>
                        </div>
                        <u-button type="primary" size="mini" @click="toTuiGuang(m.gameUrl)">推 广</u-button>
                    </view>
                </view>
            </view>

            <u-loadmore v-if="!show" :status="status" @loadmore="getPageData(false)" />
        </main>

        <!-- 游戏推广二维码弹窗 -->
        <section class="qrcard-proup">
            <u-popup v-model="showQRcard" mode="center" :zoom="false" border-radius="14" height="auto">
                <view class="content">
                    <div class="game-qrcard">
                        <create-qrcode v-show="!imageUrl" ref="qrcode" :val="gameUrl" :size="340" :showLoading="false" @result="resultQr" />
                        <image v-show="imageUrl" :src="imageUrl" mode="widthFix" @longpress="openAppToolBox(imageUrl)" show-menu-by-longpress />
                        <div>长按二维码可保存、分享</div>
                    </div>
                    <div class="close">
                        <u-icon name="close-circle" size="54" color="#e5e5e5" @click="showQRcard = false" />
                    </div>
                </view>
            </u-popup>
        </section>
    </view>
</template>

<script>
import { dateFormat } from '../../../static/utils/date'
import { gameList } from '../../../http/api'
import CreateQrcode from 'tki-qrcode'

export default {
    name: 'GameList',
    components: { CreateQrcode },
    filters: {
        dateFormat
    },
    data() {
        return {
            total: null,
            show: false,
            showQRcard: false,
            imageUrl: '',
            gameUrl: '',
            oldParams: null,
            params: {
                gameName: '',
                pageNo: 1,
                pageSize: 10
            },
            pageData: [],
            status: 'loading'
        }
    },
    computed: {
        agentLevel() {
            return this.$store.state.userInfo.agentLevel
        }
    },
    onLoad() {
        this.getPageData(true)
    },
    onReachBottom() {
        this.getPageData(false)
    },
    methods: {
        resultQr(val) {
            this.imageUrl = val
        },
        openAppToolBox(imgPath) {
            // #ifdef APP-PLUS
            uni.showActionSheet({
                itemList: ['保存', '分享'],
                success: function ({ tapIndex }) {
                    switch (tapIndex) {
                        case 0:
                            uni.saveImageToPhotosAlbum({
                                filePath: imgPath,
                                success: function () {
                                    uni.showToast({ title: '图片保存成功' })
                                }
                            })
                            break
                        case 1:
                            plus.share.sendWithSystem(
                                { pictures: [imgPath] },
                                function () {
                                    console.log('分享成功')
                                },
                                function (e) {
                                    console.log(e)
                                }
                            )
                            break
                        default:
                            break
                    }
                }
            })
            // #endif
        },
        openFilterHeader() {
            this.oldParams = JSON.parse(JSON.stringify(this.params))
        },
        toTuiGuang(gameUrl) {
            if (!gameUrl) {
                return uni.showToast({
                    title: '游戏链接未配置',
                    icon: 'none'
                })
            }
            this.imageUrl = ''
            this.gameUrl = gameUrl
            this.showQRcard = true
            this.$nextTick(() => {
                this.$refs.qrcode._makeCode()
            })
        },
        toAppUrl(url) {
            // #ifdef APP-PLUS
            plus.runtime.openURL(url)
            // #endif

            // #ifdef MP
            this.$Router.push({ name: 'WebView', params: { url } })
            // #endif
        },
        copyUrl(url) {
            uni.setClipboardData({
                data: url
            })
        },
        // 重置参数
        toReset() {
            this.params = this.$options.data().params
        },
        getPageData(isInquire) {
            if (!isInquire && this.status == 'nomore') return
            if (isInquire && JSON.stringify(this.oldParams) == JSON.stringify(this.params)) {
                return
            }
            this.status = 'loading'
            this.params.pageNo = isInquire ? 1 : this.params.pageNo + 1
            gameList(this.params).then(res => {
                if (res.code == '00') {
                    if (res.data.list.length != 0) {
                        this.show = false
                        this.total = res.data.total

                        !isInquire
                            ? res.data.list.forEach(i => {
                                  this.pageData.push(i)
                              })
                            : (this.pageData = res.data.list)
                        isInquire &&
                            uni.pageScrollTo({
                                scrollTop: 0
                            })
                    } else {
                        this.show = true
                    }
                    if (this.pageData.length >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore'
                    } else {
                        this.status = 'loadmore'
                    }
                }
            })
        }
    }
}
</script>

<style lang="less">
@import '../../../static/css/game.less';

#gameList {
    main {
        .list-data {
            padding: 20rpx 30rpx 0;
            .game-box {
                background: #fff;
                border-radius: 14rpx;
                margin-bottom: 20rpx;
                position: relative;
                z-index: 1;
                .remark {
                    position: absolute;
                    top: 30rpx;
                    right: 30rpx;
                    > image {
                        width: 48rpx;
                        height: 50rpx;
                    }
                    .rateClass {
                        animation: shakeY 5s ease 0s infinite alternate;
                    }
                }
                .card {
                    padding: 28rpx;
                    .c-top {
                        display: flex;
                        align-items: flex-start;
                        .game-icon {
                            width: 120rpx;
                            height: 120rpx;
                            border-radius: 8rpx;
                        }

                        > div {
                            margin-left: 20rpx;
                            > p {
                                margin: 0;
                                margin-top: 4rpx;
                                color: #666;
                                font-size: 26rpx;
                                > span {
                                    font-size: 28rpx;
                                    color: #333;
                                }
                                &:nth-child(2) {
                                    span {
                                        color: #666;
                                    }
                                }
                                &:last-child {
                                    color: rgb(197, 12, 12);
                                }
                            }
                        }
                    }
                }
                .shareBtnTools {
                    padding: 24rpx 28rpx;
                    border-top: 2rpx solid #e5e5e5;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    .ratebili {
                        > p {
                            margin: 0;
                            font-size: 24rpx;
                            color: #888;
                            > span {
                                color: #666;
                            }
                        }
                        .first-rate {
                            margin-bottom: 4rpx;
                        }
                    }
                }
            }
        }
    }
    .qrcard-proup {
        /deep/ .u-mode-center-box {
            border-radius: 0 !important;
            background-color: transparent;
        }
        .content {
            position: relative;
            .game-qrcard {
                padding: 20rpx;
                background-color: #fff;
                border-radius: 10rpx;
                > image {
                    width: 340rpx;
                    height: 340rpx;
                }
                > div {
                    margin-top: 6rpx;
                    text-align: center;
                    font-size: 24rpx;
                    color: #999;
                }
            }
            .close {
                padding-top: 30rpx;
                text-align: center;
            }
        }
    }
}
@keyframes shakeY {
    0%,
    to {
        transform: translateZ(0);
    }
    10%,
    30%,
    50%,
    70%,
    90% {
        transform: translate3d(0, -2rpx, 0);
    }
    20%,
    40%,
    60%,
    80% {
        transform: translate3d(0, 2rpx, 0);
    }
}
</style>
