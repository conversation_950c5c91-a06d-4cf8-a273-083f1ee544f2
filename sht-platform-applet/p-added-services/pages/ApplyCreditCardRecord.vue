<template>
    <div id="applyCreditCardRecord">
        <u-sticky>
            <u-tabs :list="allStatus" :current="searchStatus" :is-scroll="false" @change="changeSearchStatus"> </u-tabs>
        </u-sticky>

        <main>
            <no-content v-if="show" />
            <view class="list-data" v-show="!show">
                <section v-for="(p, index) in pageData" :key="index">
                    <p><span>用户姓名:</span> {{ p.custNameMask || '-' }}</p>
                    <p><span>用户手机号:</span> {{ p.mobileMask || '-' }}</p>
                    <p><span>银行名称:</span> {{ p.bankName || '-' }}</p>
                    <p><span>所在地区:</span> {{ (p.provinceName === p.cityName ? p.cityName : p.provinceName + p.cityName) || '-' }}</p>
                    <p>
                        <span>核卡状态:</span>
                        <span>{{ checkStatus[p.checkStatus] || '-' }}</span>
                    </p>
                    <template v-if="p.checkStatus === 1">
                        <p><span>核卡日期:</span> {{ p.checkDate }}</p>
                        <p>
                            <span>核卡返现状态:</span>
                            <span>{{ checkCashStatus[p.checkCashStatus] || '-' }}</span>
                        </p>
                    </template>
                    <p>
                        <span>激活状态:</span>
                        <span>{{ checkStatus[p.activeStatus] || '-' }}</span>
                    </p>
                    <template v-if="p.activeStatus === 1">
                        <p><span>激活日期:</span> {{ p.activeDate }}</p>
                        <p>
                            <span>激活返现状态:</span>
                            <span>{{ checkCashStatus[p.activeCashStatus] || '-' }}</span>
                        </p>
                    </template>
                </section>
            </view>

            <u-loadmore v-if="!show" :status="status" @loadmore="loadmore" />
        </main>
    </div>
</template>

<script>
import { getApplyCardList } from '../../http/api'
export default {
    name: 'ApplyCreditCardRecord',
    data() {
        return {
            searchStatus: 1,
            allStatus: [
                { name: '进行中', value: 0 },
                { name: '通过', value: 1 },
                { name: '拒绝', value: 2 }
            ],
            show: false,
            total: 0,
            status: 'loading',
            pageData: [],
            ajaxParams: {
                pageNo: 1, //当前页
                pageSize: 10, //每页数量
                status: 1 // 开卡状态 非必填 0-进行中 1-通过 2-拒绝 默认查状态1
            },
            checkStatus: ['进行中', '通过', '拒绝'],
            checkCashStatus: ['未返现', '已返现']
        }
    },
    onLoad() {
        this.getPageData()
    },
    onReachBottom() {
        this.loadmore()
    },
    methods: {
        changeSearchStatus(index) {
            this.searchStatus = index
            this.ajaxParams.status = this.allStatus[index]['value']
            this.getPageData()
        },
        getPageData() {
            this.status = 'loading'
            this.ajaxParams.pageNo = 1
            getApplyCardList(this.ajaxParams).then(res => {
                if (res.code == '00') {
                    this.total = res.data.total
                    if (res.data.list.length != 0) {
                        this.show = false
                        this.pageData = res.data.list
                        uni.pageScrollTo({ scrollTop: 0 })

                        if (this.pageData.length >= this.total) {
                            // 数据全部加载完成
                            this.status = 'nomore'
                        } else {
                            this.status = 'loadmore'
                        }
                    } else {
                        this.show = true
                    }
                }
            })
        },
        loadmore() {
            if (this.status == 'nomore') return
            this.status = 'loading'
            this.ajaxParams.pageNo = this.ajaxParams.pageNo + 1
            getApplyCardList(this.ajaxParams).then(res => {
                if (res.code == '00') {
                    this.total = res.data.total
                    res.data.list.forEach(i => {
                        this.pageData.push(i)
                    })
                    if (this.pageData.length >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore'
                    } else {
                        this.status = 'loadmore'
                    }
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
#applyCreditCardRecord {
    min-height: 100%;
    background-color: #f3f5f7;
    > main {
        width: 100%;
        > .list-data {
            padding: 0 20rpx;
            > section {
                padding: 20rpx 32rpx;
                margin-bottom: 20rpx;
                background-color: #fff;
                border-bottom: 2rpx solid #e5e5e5;
                border-radius: 12rpx;
                &:first-of-type {
                    margin-top: 20rpx;
                }
                > p {
                    display: flex;
                    margin: 0.5em 0;
                    color: #666;
                    font-weight: 500;
                    &:last-of-type {
                        margin-bottom: 0;
                    }
                    > span {
                        &:first-of-type {
                            flex-shrink: 0;
                            width: calc(7em + 30rpx);
                            color: #444;
                            font-weight: normal;
                        }
                    }
                }
            }
        }
    }
}
</style>
