<script>
import store from './store';
import { loginExpire, appVersion, loginSwitch } from './http/api';
import { checkVersion } from './static/utils/updateVersion';

export default {
  onLaunch: async function () {
    console.log('App Launch');

    //#ifdef APP-PLUS
      this.checkAppUpdate();
     await this.checkIosIsPass();
    //#endif

    //#ifdef MP
    const hideTabbarIndexs = [1, 2];
    hideTabbarIndexs.forEach(index => {
      uni.setTabBarItem({
        index,
        visible: true
      });
    });
    //#endif
  },
  onShow: function () {
    console.log('App Show');
    // #ifdef MP
    checkVersion();
    // #endif

    //#ifdef APP-PLUS
    // 应用从后台回到前台时，检查是否需要重新验证iOS状态
    if (uni.getSystemInfoSync().platform === 'ios') {
      this.checkIosStatusOnShow();
    }
    //#endif

    // store.state.token && this.checkLoginExpire()
  },
  onHide: function () {
    console.log('App Hide');
  },
  methods: {
    async checkLoginExpire() {
      const { data } = await loginExpire();

      if (!data.isExpire) {
        store.dispatch('login_out');
      }
    },
    checkAppUpdate() {
      const platform = uni.getSystemInfoSync().platform;
      // 获取本地应用资源版本号
      plus.runtime.getProperty(plus.runtime.appid, inf => {
        //获取服务器的版本号
        appVersion({ abbr: 'shtop', version: inf.version, fileType: platform === 'android' ? 10 : 20 }).then(({ data }) => {
          if (!data) return;
          const { downloadURL, support, newest, buildUpdateDescription } = data;

          const convertData = {
            describe: buildUpdateDescription, // 版本更新内容 支持<br>自动换行
            edition_url: platform === 'android' ? downloadURL : 'itms-apps://itunes.apple.com/cn/app/1617036314', //apk下载地址 或者 跳转appstore
            edition_force: Number(!support), //是否强制更新 0代表否 1代表是
            package_type: 0, //0是整包升级（apk或者appstore或者安卓应用市场） 1是wgt升级
            edition_issue: 1 //是否发行  0否 1是 为了控制上架应用市场审核时不能弹出热更新框
          };
          //判断后台返回是否最新版
          if (newest === 0) {
            //跳转更新页面 （注意！！！如果pages.json第一页的代码里有一打开就跳转其他页面的操作，下面这行代码最好写在setTimeout里面设置延时3到5秒再执行）
            setTimeout(() => {
              uni.navigateTo({
                url: '/uni_modules/rt-uni-update/components/rt-uni-update/rt-uni-update?obj=' +  encodeURIComponent(JSON.stringify(convertData))
              });
            }, 3000);
          }
        });
      });
    },
    async checkIosIsPass() {
      const os = uni.getSystemInfoSync();
      if (os.platform === 'ios') {
        plus.runtime.getProperty(plus.runtime.appid, async inf => {
          try {
            // 检查网络连接状态
            const networkInfo = await this.checkNetworkStatus();
            if (!networkInfo.isConnected) {
              console.log('网络未连接，使用默认配置');
              this.setDefaultIosConfig();
              return;
            }

            // 尝试请求接口，设置超时时间
            const { data } = await Promise.race([
              loginSwitch({ version: inf.version }),
              new Promise((_, reject) =>
                setTimeout(() => reject(new Error('请求超时')), 5000)
              )
            ]);

            if (data && typeof data.loginSwitch !== 'undefined') {
              if (data.loginSwitch === 1) {
                store.commit('SET_ISIOSCHECKPASS', 0);
                this.setTabBarVisibility([1, 2], false);
              } else {
                store.commit('SET_ISIOSCHECKPASS', 1);
                this.setTabBarVisibility([1, 2], true);
              }
            } else {
              console.log('接口返回数据异常，使用默认配置');
              this.setDefaultIosConfig();
            }
          } catch (error) {
            console.log('iOS检查失败:', error.message);
            // 网络请求失败时的处理策略
            this.handleIosCheckError(error);
          }
        });
      } else {
        // 非iOS平台直接设置为通过
        this.setTabBarVisibility([1, 2], true);
        store.commit('SET_ISIOSCHECKPASS', 1);
      }
    },

    // 检查网络状态
    checkNetworkStatus() {
      return new Promise((resolve) => {
        uni.getNetworkType({
          success: (res) => {
            resolve({
              isConnected: res.networkType !== 'none',
              networkType: res.networkType
            });
          },
          fail: () => {
            resolve({ isConnected: false, networkType: 'none' });
          }
        });
      });
    },

    // 设置TabBar可见性
    setTabBarVisibility(indexes, visible) {
      indexes.forEach(index => {
        uni.setTabBarItem({
          index,
          visible
        });
      });
    },

    // 设置默认iOS配置
    setDefaultIosConfig() {
      // 从本地存储获取上次的配置，如果没有则默认为通过
      const lastConfig = uni.getStorageSync('isIosCheckPass');
      const defaultPass = lastConfig !== null ? lastConfig : 1;

      store.commit('SET_ISIOSCHECKPASS', defaultPass);
      this.setTabBarVisibility([1, 2], defaultPass === 1);

      console.log('使用默认配置:', defaultPass === 1 ? '通过' : '不通过');
    },

    // 处理iOS检查错误
    handleIosCheckError(error) {
      // 如果是网络权限问题，延迟重试
      if (error.message.includes('网络') || error.message.includes('请求超时')) {
        console.log('可能是网络权限问题，延迟重试');

        // 先使用默认配置
        this.setDefaultIosConfig();

        // 延迟重试，给用户时间授权网络权限
        setTimeout(() => {
          this.retryIosCheck();
        }, 3000);
      } else {
        // 其他错误，使用默认配置
        this.setDefaultIosConfig();
      }
    },

    // 重试iOS检查
    async retryIosCheck() {
      try {
        const networkInfo = await this.checkNetworkStatus();
        if (!networkInfo.isConnected) {
          console.log('重试时网络仍未连接');
          return;
        }

        plus.runtime.getProperty(plus.runtime.appid, async inf => {
          try {
            const { data } = await loginSwitch({ version: inf.version });
            if (data && typeof data.loginSwitch !== 'undefined') {
              const newStatus = data.loginSwitch === 1 ? 0 : 1;
              const currentStatus = store.state.isIosCheckPass;

              // 只有状态发生变化时才更新
              if (newStatus !== currentStatus) {
                store.commit('SET_ISIOSCHECKPASS', newStatus);
                this.setTabBarVisibility([1, 2], newStatus === 1);
                console.log('重试成功，更新配置:', newStatus === 1 ? '通过' : '不通过');
              }
            }
          } catch (retryError) {
            console.log('重试仍然失败:', retryError.message);
          }
        });
      } catch (error) {
        console.log('重试检查网络状态失败:', error.message);
      }
    },

    // 应用回到前台时检查iOS状态
    async checkIosStatusOnShow() {
      // 如果当前状态是默认值或者上次检查失败，尝试重新检查
      const currentStatus = store.state.isIosCheckPass;
      const lastCheckTime = uni.getStorageSync('lastIosCheckTime') || 0;
      const now = Date.now();

      // 如果距离上次检查超过5分钟，或者当前状态为null，则重新检查
      if (currentStatus === null || (now - lastCheckTime > 5 * 60 * 1000)) {
        console.log('应用回到前台，重新检查iOS状态');

        try {
          const networkInfo = await this.checkNetworkStatus();
          if (networkInfo.isConnected) {
            // 记录检查时间
            uni.setStorageSync('lastIosCheckTime', now);

            plus.runtime.getProperty(plus.runtime.appid, async inf => {
              try {
                const { data } = await Promise.race([
                  loginSwitch({ version: inf.version }),
                  new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('请求超时')), 3000)
                  )
                ]);

                if (data && typeof data.loginSwitch !== 'undefined') {
                  const newStatus = data.loginSwitch === 1 ? 0 : 1;
                  if (newStatus !== currentStatus) {
                    store.commit('SET_ISIOSCHECKPASS', newStatus);
                    this.setTabBarVisibility([1, 2], newStatus === 1);
                    console.log('前台检查更新配置:', newStatus === 1 ? '通过' : '不通过');
                  }
                }
              } catch (error) {
                console.log('前台检查失败:', error.message);
              }
            });
          }
        } catch (error) {
          console.log('前台网络检查失败:', error.message);
        }
      }
    }
  }
};
</script>

<style lang="scss">
@import 'uview-ui/index.scss';
@import './static/css/common.scss';
</style>
