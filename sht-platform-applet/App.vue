<script>
import store from './store';
import { loginExpire, appVersion, loginSwitch } from './http/api';
import { checkVersion } from './static/utils/updateVersion';

export default {
  onLaunch: async function () {
    console.log('App Launch');

    //#ifdef APP-PLUS
      this.checkAppUpdate();
     await this.checkIosIsPass();
    //#endif

    //#ifdef MP
    const hideTabbarIndexs = [1, 2];
    hideTabbarIndexs.forEach(index => {
      uni.setTabBarItem({
        index,
        visible: true
      });
    });
    //#endif
  },
  onShow: function () {
    console.log('App Show');
    // #ifdef MP
    checkVersion();
    // #endif

    //#ifdef APP-PLUS
    // 应用从后台回到前台时，检查是否需要重新验证iOS状态
    if (uni.getSystemInfoSync().platform === 'ios') {
      this.checkIosStatusOnShow();
    }
    //#endif

    // store.state.token && this.checkLoginExpire()
  },
  onHide: function () {
    console.log('App Hide');
  },
  methods: {
    async checkLoginExpire() {
      const { data } = await loginExpire();

      if (!data.isExpire) {
        store.dispatch('login_out');
      }
    },
    checkAppUpdate() {
      const platform = uni.getSystemInfoSync().platform;
      // 获取本地应用资源版本号
      plus.runtime.getProperty(plus.runtime.appid, inf => {
        //获取服务器的版本号
        appVersion({ abbr: 'shtop', version: inf.version, fileType: platform === 'android' ? 10 : 20 }).then(({ data }) => {
          if (!data) return;
          const { downloadURL, support, newest, buildUpdateDescription } = data;

          const convertData = {
            describe: buildUpdateDescription, // 版本更新内容 支持<br>自动换行
            edition_url: platform === 'android' ? downloadURL : 'itms-apps://itunes.apple.com/cn/app/1617036314', //apk下载地址 或者 跳转appstore
            edition_force: Number(!support), //是否强制更新 0代表否 1代表是
            package_type: 0, //0是整包升级（apk或者appstore或者安卓应用市场） 1是wgt升级
            edition_issue: 1 //是否发行  0否 1是 为了控制上架应用市场审核时不能弹出热更新框
          };
          //判断后台返回是否最新版
          if (newest === 0) {
            //跳转更新页面 （注意！！！如果pages.json第一页的代码里有一打开就跳转其他页面的操作，下面这行代码最好写在setTimeout里面设置延时3到5秒再执行）
            setTimeout(() => {
              uni.navigateTo({
                url: '/uni_modules/rt-uni-update/components/rt-uni-update/rt-uni-update?obj=' +  encodeURIComponent(JSON.stringify(convertData))
              });
            }, 3000);
          }
        });
      });
    },
    async checkIosIsPass() {
      const os = uni.getSystemInfoSync();
      if (os.platform === 'ios') {
        plus.runtime.getProperty(plus.runtime.appid, async inf => {
          // 先设置默认配置，确保应用能正常启动
          this.setDefaultIosConfig();

          // 延迟执行网络请求，避免阻塞应用启动
          setTimeout(async () => {
            try {
              console.log('开始iOS状态检查...');

              // 使用较长的超时时间，给用户足够时间处理网络权限弹窗
              const { data } = await Promise.race([
                loginSwitch({ version: inf.version }),
                new Promise((_, reject) =>
                  setTimeout(() => reject(new Error('网络权限请求超时')), 15000)
                )
              ]);

              if (data && typeof data.loginSwitch !== 'undefined') {
                const newStatus = data.loginSwitch === 1 ? 0 : 1;
                const currentStatus = store.state.isIosCheckPass;

                // 只有状态发生变化时才更新UI
                if (newStatus !== currentStatus) {
                  console.log('iOS状态检查完成，更新配置:', newStatus === 1 ? '通过' : '不通过');
                  store.commit('SET_ISIOSCHECKPASS', newStatus);
                  this.setTabBarVisibility([1, 2], newStatus === 1);
                } else {
                  console.log('iOS状态检查完成，配置无变化');
                }
              } else {
                console.log('接口返回数据异常，保持当前配置');
              }
            } catch (error) {
              console.log('iOS状态检查失败:', error.message);

              // 如果是网络权限相关错误，标记需要后续重试
              if (this.isNetworkPermissionError(error)) {
                console.log('检测到可能的网络权限问题，将在后续重试');
                uni.setStorageSync('needRetryIosCheck', true);
              }
            }
          }, 1000); // 延迟1秒执行，让应用先完成基本初始化
        });
      } else {
        // 非iOS平台直接设置为通过
        this.setTabBarVisibility([1, 2], true);
        store.commit('SET_ISIOSCHECKPASS', 1);
      }
    },

    // 判断是否为网络权限相关错误
    isNetworkPermissionError(error) {
      const errorMsg = error.message || '';
      return errorMsg.includes('网络') ||
             errorMsg.includes('超时') ||
             errorMsg.includes('Network') ||
             errorMsg.includes('timeout') ||
             error.code === 'NETWORK_ERROR';
    },

    // 设置TabBar可见性
    setTabBarVisibility(indexes, visible) {
      indexes.forEach(index => {
        uni.setTabBarItem({
          index,
          visible
        });
      });
    },

    // 设置默认iOS配置
    setDefaultIosConfig() {
      // 从本地存储获取上次的配置，如果没有则默认为通过
      const lastConfig = uni.getStorageSync('isIosCheckPass');
      const defaultPass = lastConfig !== null ? lastConfig : 1;

      store.commit('SET_ISIOSCHECKPASS', defaultPass);
      this.setTabBarVisibility([1, 2], defaultPass === 1);

      console.log('设置默认iOS配置:', defaultPass === 1 ? '通过' : '不通过');
    },



    // 应用回到前台时检查iOS状态
    async checkIosStatusOnShow() {
      const needRetry = uni.getStorageSync('needRetryIosCheck');
      const currentStatus = store.state.isIosCheckPass;
      const lastCheckTime = uni.getStorageSync('lastIosCheckTime') || 0;
      const now = Date.now();

      // 如果需要重试，或者距离上次检查超过10分钟，或者当前状态为null，则重新检查
      if (needRetry || currentStatus === null || (now - lastCheckTime > 10 * 60 * 1000)) {
        console.log('应用回到前台，重新检查iOS状态');

        // 清除重试标记
        if (needRetry) {
          uni.removeStorageSync('needRetryIosCheck');
        }

        plus.runtime.getProperty(plus.runtime.appid, async inf => {
          try {
            // 记录检查时间
            uni.setStorageSync('lastIosCheckTime', now);

            const { data } = await Promise.race([
              loginSwitch({ version: inf.version }),
              new Promise((_, reject) =>
                setTimeout(() => reject(new Error('前台检查超时')), 8000)
              )
            ]);

            if (data && typeof data.loginSwitch !== 'undefined') {
              const newStatus = data.loginSwitch === 1 ? 0 : 1;
              if (newStatus !== currentStatus) {
                store.commit('SET_ISIOSCHECKPASS', newStatus);
                this.setTabBarVisibility([1, 2], newStatus === 1);
                console.log('前台检查更新配置:', newStatus === 1 ? '通过' : '不通过');
              } else {
                console.log('前台检查完成，配置无变化');
              }
            }
          } catch (error) {
            console.log('前台检查失败:', error.message);
            // 如果仍然失败，标记下次继续重试
            if (this.isNetworkPermissionError(error)) {
              uni.setStorageSync('needRetryIosCheck', true);
            }
          }
        });
      }
    }
  }
};
</script>

<style lang="scss">
@import 'uview-ui/index.scss';
@import './static/css/common.scss';
</style>
