import { RouterMount, createRouter } from 'uni-simple-router';
import store from '../store'

const router = createRouter({
    platform: process.env.VUE_APP_PLATFORM,
    detectBeforeLock: (router, to, navType) => {
        router.$lockStatus = false;
    },
    routes: [...ROUTES]
})

// 路由白名单
const whiteList = ['Login', 'Register', 'Agreement','Authentification','WebView' ,'AppUpdate']

//全局路由前置守卫
router.beforeEach((to, from, next) => {
    const isNext = store.state.token !== '' || whiteList.indexOf(to.name) !== -1

    if (isNext) {
        next()
    } else {
        next({ name: 'Login', NAVTYPE: 'replaceAll' });
    }
});

export {
    router,
    RouterMount
}