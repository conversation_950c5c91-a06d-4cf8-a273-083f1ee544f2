import Vue from 'vue'
import App from './App'
import uView from "uview-ui"
import { router } from './router'
import store from './store/index'
import NoContent from "./components/NoContent.vue"
import share from './static/utils/mixin' // 每个页面分享mixin
import * as filters from './static/utils/filter' // global filters
// register global utility filters
Object.keys(filters).forEach(key => {
    Vue.filter(key, filters[key])
})
Vue.mixin(share)
Vue.use(uView);
Vue.use(router)
Vue.component("no-content", NoContent);
Vue.prototype.$store = store


Vue.config.productionTip = false
App.mpType = 'app'

const app = new Vue({
    store,
    ...App
})
app.$mount()
