// 标准时间转化格式
const dayjs = require('dayjs')
export function dateFormat(val, formatter = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(val).format(formatter)
}

// 往前/后推n天的日期
export function getDate(n) {
    let d = new Date()
    d.setDate(d.getDate() + n)
    let month = d.getMonth() + 1
    let day = d.getDate()
    if (month < 10) {
        month = '0' + month
    }
    if (day < 10) {
        day = '0' + day
    }
    let s = dayjs(d.getFullYear() + '-' + month + '-' + day).valueOf()
    return s
}

// 数字保留2位小数
export function toDecimal2(x) {
    var f = parseFloat(x)
    if (isNaN(f)) {
        return false
    }
    var f = Math.round(x * 100) / 100,
        s = f.toString(),
        rs = s.indexOf('.')
    if (rs < 0) {
        rs = s.length
        s += '.'
    }
    while (s.length <= rs + 2) {
        s += '0'
    }
    return s
}

// js除法精准计算
export function accDiv(arg1, arg2) {
    var t1 = 0,
        t2 = 0,
        r1,
        r2
    try {
        t1 = arg1.toString().split('.')[1].length
    } catch (e) {}
    try {
        t2 = arg2.toString().split('.')[1].length
    } catch (e) {}
    r1 = Number(arg1.toString().replace('.', ''))
    r2 = Number(arg2.toString().replace('.', ''))
    return (r1 / r2) * Math.pow(10, t2 - t1)
}

// 页面搜索框查询防抖
export function debounce(fn, delay) {
    var delay = delay || 200
    var timer

    return function () {
        var th = this
        if (timer) {
            clearTimeout(timer)
        }
        timer = setTimeout(function () {
            timer = null
            fn.apply(th)
        }, delay)
    }
}
