<template>
    <div class="title statisticAnalysis">
        <p>
            <image src="../../static/images/home/<USER>" alt="" />
            <span>数据统计</span>
        </p>
        <div>
            <view class="subsection">
                <u-subsection :font-size="24" :height="60" active-color="#004ea9" :list="tabList" :current="active" @change="changeData" />
            </view>
            <view class="chart">
                <line-chart ref="lineChart" :dataType="dataType" :payMarketMode="payMarketMode" />
            </view>
        </div>
        <div>
            <p>
                <image src="../../static/images/home/<USER>" alt="" />
                <span>本月交易(万元)</span>
                <span>{{ sumThisMonth }}</span>
            </p>
            <p>
                <image src="../../static/images/home/<USER>" alt="" />
                <span>本月收益(元)</span>
                <span>{{ profitThisMonth }}</span>
            </p>
        </div>
    </div>
</template>

<script>
import LineChart from '../LineChart.vue'
import { getDirectDataThisMonth, getTeamDataThisMonth } from "../../http/api";
import { toDecimal2, accDiv } from '../../static/utils/date';
export default {
    name: 'StatisticAnalysis',
    components: {
        LineChart
    },
    props: {
        payMarketMode: {
            type: Number
        },
    },
    filters: {
        toDecimal2
    },
    data() {
        return {
            active: 0,
            dataType: 1,
            sumThisMonth: '',
            profitThisMonth: '',
            tabList: [{ name: '本周交易量' }, { name: '本月交易量' }, { name: '近半年交易量' }]
        }
    },
    mounted() {
        this.getThisMonthData();
    },
    methods: {
        async getThisMonthData() {
            try {
                const teamdata = await getTeamDataThisMonth(parseInt(this.$store.state.currentMode))
                const directData = await getDirectDataThisMonth(parseInt(this.$store.state.currentMode))
                if (teamdata.code == '00' && directData.code == '00') {
                    this.$emit('teamTurnover', directData.data.directPosTransAmount);
                    this.sumThisMonth = toDecimal2(accDiv(teamdata.data.teamPosTransAmount + directData.data.directPosTransAmount, 10000));
                    this.profitThisMonth = toDecimal2(teamdata.data.teamPosProfitAmount + directData.data.directPosProfitAmount + teamdata.data.teamPosCashbackAmount + directData.data.directPosCashbackAmount);
                }
            } catch (error) {
                console.log(error)
            }
        },

        changeData(name) {
            this.active = name
            this.dataType = name + 1;
        }
    }
}
</script>

<style lang="scss" scoped>
@import "../../static/css/home.scss";
.statisticAnalysis {
    > div {
        &:nth-of-type(2) {
            width: 690rpx;
            height: 536rpx;
            background: #f3f5f7;
            border-radius: 20rpx;
            overflow: hidden;
            padding: 30rpx 0 20rpx;
            margin-bottom: 20rpx;
            position: relative;
            .subsection {
                margin: 0 40rpx 10rpx;
            }
        }
        &:nth-of-type(3) {
            display: flex;
            justify-content: space-between;
            > p {
                width: 330rpx;
                background: #f3f5f7;
                border-radius: 16rpx;
                margin: 0;
                position: relative;
                > image {
                    width: 12rpx;
                    height: 12rpx;
                    margin: 44rpx 12rpx 44rpx 30rpx;
                }
                > span {
                    &:nth-of-type(1) {
                        font-size: 28rpx;
                        color: #666666;
                        position: absolute;
                        top: 34rpx;
                    }
                    &:nth-of-type(2) {
                        font-size: 36rpx;
                        color: #ce0010;
                        margin-bottom: 30rpx;
                        display: inline-block;
                        position: relative;
                        top: 10rpx;
                    }
                }
            }
        }
    }
}
</style>