<template>
  <div class="top">
    <div></div>
    <section>
      <view v-if="userInfo.payMarketMode == 0" class="set-mode">
        <text>{{ currentMode == 1 ? '渠道' : '直营' }}模式</text>
        <image :src="modeImg" alt="" @click="changeMode" />
      </view>
      <slot name="wallet"></slot>
      <view class="banner-swiper" v-if="banners.length">
        <u-swiper :list="banners" name="url" @click="toBannerAdvertisement" />
      </view>
      <view class="notice-bar" v-show="!!latestNewsTitle">
        <u-notice-bar
          type="primary"
          mode="horizontal"
          :border-radius="4"
          :list="['官方公告: ' + latestNewsTitle]"
          more-icon
          @click="toNewsDetail"
          @getMore="toNews"
        ></u-notice-bar>
      </view>
    </section>

    <!-- 公告弹框 -->
    <u-popup
      v-model="showNewsModel"
      class="news-popup"
      mode="center"
      width="70%"
      border-radius="20"
      :mask-close-able="false"
      closeable
      close-icon="close-circle-fill"
      close-icon-color="#fff"
      close-icon-size="70"
      close-icon-pos="bottom-left"
    >
      <view class="news-model">
        <image
          src="../../static/images/home/<USER>"
          mode="widthFix"
        ></image>
        <view class="news-title">{{ latestNews.noticeTitle }}</view>
        <scroll-view scroll-y="true" style="height: 340rpx">
          <view class="news-content">
            <rich-text :nodes="latestNews.noticeContent"></rich-text>
          </view>
        </scroll-view>
      </view>
    </u-popup>
  </div>
</template>

<script>
  import { getAllNotifications, getSystemParamImgUrl } from '../../http/api';
  import { mapState } from 'vuex';
  import dayjs from 'dayjs';

  export default {
    name: 'Top',
    props: {
      modeImg: {
        type: String
      }
    },
    data() {
      return {
        latestNewsTitle: '',
        latestNews: {},
        showNewsModel: false,
        isInit: true,
        banners: []
      };
    },
    computed: {
      ...mapState(['userInfo', 'currentMode', 'appNotifications'])
    },
    mounted() {
      if (
        !(typeof plus === 'object') ||
        this.$store.state.isIosCheckPass == 1
      ) {
        this.getBanners();
      }
    },
    methods: {
      getBanners() {
        getSystemParamImgUrl({ tip: '占位' }).then((res) => {
          if (res.code == '00') {
            const bannerIds = [];
            Object.keys(res.data || {})
              .filter((key) => key.includes('appBannerImgUrl'))
              .forEach((i) => {
                bannerIds.push(i.replace('appBannerImgUrl', ''));
              });

            bannerIds.forEach((id) => {
              if (res.data[`appBannerImgUrl${id}`]) {
                this.banners.push({
                  url:
                    res.data[`appBannerImgUrl${id}`] +
                    `?number=${Math.random()}`,
                  jumpStatus: res.data[`appBanner${id}JumpStatus`],
                  jumpUrl: res.data[`appBanner${id}JumpUrl`]
                });
              }
            });
          }
        });
      },
      changeMode() {
        if (this.currentMode == 1) {
          this.$store.commit('SET_CURRENTMODE', 2);
        } else {
          this.$store.commit('SET_CURRENTMODE', 1);
        }
      },
      toBannerAdvertisement(index) {
        const { jumpStatus, jumpUrl } = this.banners[index];
        if (!jumpStatus) return;
        switch (jumpStatus) {
          case 1:
            // #ifdef APP-PLUS
            plus.runtime.openURL(jumpUrl);
            // #endif
            // #ifdef MP
            uni.showModal({
              content: '请复制链接前往浏览器查看！',
              confirmText: '复制链接',
              success: (val) => {
                if (val.confirm) {
                  uni.setClipboardData({
                    data: jumpUrl
                  });
                }
              }
            });
            // #endif
            break;
          case 2:
            this.$Router.push({ name: jumpUrl });
            break;
          default:
            this.$u.toast('jumpStatus 状态未知!');
            break;
        }
      },
      toNewsDetail() {
        this.$Router.push({
          name: 'Notification',
          params: { id: this.latestNews.id, announceType: this.latestNews.type }
        });
      },
      toNews() {
        this.$Router.push({ name: 'Notifications' });
      },
      getNotifications() {
        getAllNotifications().then((res) => {
          if (res.code == '00') {
            var { officialNOtice = [], systemNotice = [] } = res.data;
            officialNOtice
              ? officialNOtice.forEach((o) => (o.type = 0))
              : (officialNOtice = []);
            systemNotice
              ? systemNotice.forEach((s) => (s.type = 1))
              : (systemNotice = []);
            this.latestNewsTitle = officialNOtice[0]?.noticeTitle;
            this.latestNews = officialNOtice[0] || {};
            if (this.latestNewsTitle) {
              const intervalDays = dayjs().diff(
                this.latestNews.createTime,
                'day'
              );
              if (intervalDays > 10) {
                this.latestNewsTitle = '';
              }
            }
            if (this.isInit) {
              const shownLatestNewsId = uni.getStorageSync('shownLatestNewsId');
              if (
                this.latestNews.id !== shownLatestNewsId &&
                this.latestNews.createTime
              ) {
                const diffDay = dayjs(this.latestNews.createTime).diff(
                  new Date(),
                  'day'
                );
                if (!(diffDay < -3)) {
                  this.showNewsModel = true;
                  this.isInit = false;
                  uni.setStorageSync('shownLatestNewsId', this.latestNews.id);
                }
              }
            }
            var appNotificationsMap = [...officialNOtice, ...systemNotice];
            const appNotifications = [];
            appNotificationsMap.forEach((i) => {
              appNotifications.push({
                id: i.id,
                type: i.type,
                noticeTitle: i.noticeTitle,
                noticeContent: i.noticeContent,
                createTime: i.createTime,
                readingSatus: 0 // 未读
              });
            });
            const storageNotifications =
              uni.getStorageSync('appNotifications') || [];
            if (storageNotifications.length) {
              appNotifications.forEach((i) => {
                storageNotifications.forEach((j) => {
                  if (i.id == j.id && i.type == j.type) {
                    i.readingSatus = j.readingSatus;
                  }
                });
              });
            }
            this.$store.commit('SET_APPNOTIFICATIONS', appNotifications);
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .top {
    > div {
      width: 100%;
      height: 424rpx;
      background-image: url('../../static/images/home/<USER>');
      background-size: 100%;
      background-position: 0 -10rpx;
      background-repeat: no-repeat;
      position: absolute;
      top: 0;
    }
    > section {
      position: relative;
      z-index: 1;
      .set-mode {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 30rpx;
        margin-bottom: 16rpx;
        font-size: 0;
        text {
          position: relative;
          margin-left: 30rpx;
          font-size: 28rpx;
          color: #fff;
          &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: -22rpx;
            width: 14rpx;
            height: 14rpx;
            transform: translateY(-50%);
            border-radius: 50%;
            background-color: #4caf50;
          }
        }
        image {
          width: 188rpx;
          height: 48rpx;
        }
      }
    }
    .banner-swiper {
      margin-bottom: 20rpx;
      /deep/ swiper {
        width: 690rpx;
        height: 168rpx !important;
        border-radius: 20rpx;
        margin: 0 auto;
        background-color: #fff !important;
      }
    }

    .notice-bar {
      padding: 0 30rpx;
    }

    .news-model {
      > image {
        width: 100%;
        border-radius: 20rpx 20rpx 0 0;
      }
      .news-title {
        padding: 30rpx 20rpx 0;
        text-align: center;
        font-weight: 500;
        font-size: 30rpx;
      }
      .news-content {
        padding: 20rpx 30rpx;
      }
    }
  }
</style>
