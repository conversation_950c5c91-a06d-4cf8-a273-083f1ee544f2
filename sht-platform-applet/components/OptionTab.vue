<template>
    <u-sticky :offset-top="top">
        <header>
            <section>
                <div :class="current == 0 ? 'checked' : ''" @click="clickTab(0)">
                    {{ opts.substring(0, opts.indexOf(",")) }}
                </div>
                <div :class="current == 1 ? 'checked' : ''" @click="clickTab(1)">
                    {{ opts.substring(opts.indexOf(",") + 1) }}
                </div>
            </section>
        </header>
    </u-sticky>

</template>

<script>
export default {
    name: "OptionTab",
    props: {
        opts: {
            type: String,
            default: ""
        },
        current: {
            type: Number,
            default: 0
        },
        top: {
            type: Number,
            default: 0
        }
    },
    methods: {
        clickTab(index) {
            this.$emit("select", index);
        }
    }
};
</script>

<style lang="less" scoped>
header {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 120rpx;
    background: #fff;
    border-bottom: 1rpx solid #f3f5f7;
}
section {
    display: flex;
    align-items: center;
    width: 100%;
    height: 78rpx;
    margin: 0 30rpx;
    background-color: #f3f5f7;
    border-radius: 16rpx;

    div {
        flex: 1;
        margin: 0 10rpx;
        height: 60rpx;
        line-height: 60rpx;
        text-align: center;
        font-size: 26rpx;
        border-radius: 14rpx;
        &.checked {
            color: #004ea9;
            background-color: #fff;
        }
    }
}
</style>
