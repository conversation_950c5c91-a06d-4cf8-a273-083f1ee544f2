<template>
    <div class="dataChart">
        <view class="tabs">
            <u-tabs :list="tabsDeal" :is-scroll="false" :current="typeActive" @change="changeTypeActive" />
        </view>

        <u-field disabled right-icon="arrow-down-fill" label="选择通道:" placeholder="请选择要查询的通道" :value="channelName" @click="showChannelCode" />
        <u-select v-model="showChannelCodePicker" mode="single-column" :list="channleCodes" value-name="code" label-name="name" @confirm="onConfirmChannelCode" />

        <u-dropdown v-if="channelCode">
            <u-dropdown-item v-model="cycleTypeActive" :title="tabsDayOrMonth[cycleTypeActive].label" :options="tabsDayOrMonth" />
            <u-dropdown-item v-model="cycleActive" :title="tabsTime[cycleActive].label" :options="tabsTime" />
        </u-dropdown>

        <main v-if="channelCode">
            <div class="chart">
                <view class="chart-title">
                    <text>{{ lineSubtext }}</text>
                </view>
                <view class="chart-box">
                    <qiun-data-charts inScrollView type="area" :opts="chartOptions" canvasId="datchartbox2" canvas2d :chartData="chartData" />
                </view>
            </div>
        </main>
    </div>
</template>

<script>
import { accDiv, toDecimal2 } from '../../static/utils/date'
import { getChannelChartDataList, getChannelProfitChartData, getChannelActivationChartData, getChannel } from '../../http/api'

export default {
    name: 'DataChart2',
    data() {
        return {
            typeActive: 0,
            cycleActive: 0,
            cycleTypeActive: 0,
            lineSubtext: '交易额:万元',
            dataInfo: {
                totalPosTransAmount: 0, // 总交易量
                totalProfitAmount: 0, // 总收益
                totalActivateTerminalNum: 0, // 终端激活总数
                totalStandardTerminalNum: 0, // 终端达标总数
                totalNewAgentNum: 0, // 代理商总数,
                totalNewMerchantNum: 0
            },
            accDiv,
            chartData: {},
            chartOptions: {
                extra: {
                    area: { type: 'curve', addLine: true, gradient: true, opacity: 0.4 }
                },
                yAxis: {
                    splitNumber: 5
                },
                xAxis: { labelCount: 7 },
                dataLabel: false,
                dataPointShape: false
            },
            tabsDayOrMonth: [
                {
                    label: '按日',
                    value: 0
                },
                {
                    label: '按月',
                    value: 1
                }
            ],
            showChannelCodePicker: false,
            channelName: '',
            channelCode: '',
            channleCodes: []
        }
    },
    computed: {
        tabsDeal() {
            const arr = [{ name: '交易' }, { name: '分润' }, { name: '返现' }, { name: '商户' }]
            if (this.$store.state.currentMode == 1) {
                arr.splice(3, 0, { name: '激活' })
                arr.splice(4, 0, { name: '达标' })
            }
            return arr
        },
        currentMode() {
            return this.$store.state.currentMode
        },
        tabsTime() {
            const tabsNameArr = this.cycleTypeActive == 0 ? ['近七天', '近一个月', '近半年'] : ['半年内', '一年内', '全部']
            const tabsValueArr = [{ value: 0 }, { value: 1 }, { value: 2 }]

            tabsValueArr.forEach((t, index) => (tabsValueArr[index].label = tabsNameArr[index]))
            return tabsValueArr
        },
        selectType() {
            var typeActive
            if (this.$store.state.currentMode == 1) {
                if (this.typeActive < 2) {
                    typeActive = this.typeActive + 1
                } else if (this.typeActive == 2) {
                    typeActive = 8
                } else {
                    if (this.typeActive == 5) {
                        typeActive = 6
                    } else {
                        typeActive = this.typeActive
                    }
                }
            } else {
                if (this.typeActive < 2) {
                    typeActive = this.typeActive + 1
                } else if (this.typeActive == 2) {
                    typeActive = 8
                } else {
                    typeActive = 6
                }
            }
            return typeActive
        }
    },
    filters: {
        toDecimal2
    },
    watch: {
        selectType(val) {
            switch (val) {
                case 1:
                    this.lineSubtext = '交易额:万元'
                    break
                case 2:
                    this.lineSubtext = '分润:元'
                    break
                case 8:
                    this.lineSubtext = '返现:元'
                    break
                case 3:
                    this.lineSubtext = '数量:台数'
                    break
                case 4:
                    this.lineSubtext = '数量:台数'
                    break
                default:
                    this.lineSubtext = '数量:个数'
            }
            this.getChartData()
        },
        cycleActive() {
            this.getChartData()
        },
        cycleTypeActive() {
            this.getChartData()
        }
    },
    methods: {
        showChannelCode() {
            getChannel().then(res => {
                if (res.code == '00') {
                    if (res.data.length != 0) {
                        this.channleCodes = res.data
                        this.showChannelCodePicker = true
                    } else {
                        this.$u.toast('暂无可选通道')
                    }
                }
            })
        },
        onConfirmChannelCode([{ label, value }]) {
            this.channelName = label
            this.channelCode = value
            this.showChannelCodePicker = false
            this.getChartData()
        },
        refreshData() {
            if (!this.channelCode) return this.$u.toast('请选择通道')
            this.getChartData()
        },
        changeTypeActive(index) {
            this.typeActive = index
        },
        changeCycleActive(index) {
            this.cycleActive = index
        },
        changeCycleTypeActive(index) {
            this.cycleTypeActive = index
        },
        updateChartData(xAxis, series) {
            const data = {
                categories: xAxis,
                series: [
                    { name: '总数', legendShape: 'rect', data: series[0] },
                    { name: '直营', legendShape: 'rect', data: series[1] },
                    { name: '团队', legendShape: 'rect', data: series[2] }
                ]
            }
            this.chartData = data
        },
        getChartData() {
            if (!this.channelCode) return this.$u.toast('请选择通道')
            switch (this.typeActive) {
                case 0:
                    //交易统计
                    getChannelChartDataList({
                        dataType: this.cycleActive + 1,
                        payMarketMode: parseInt(this.$store.state.currentMode),
                        censusType: this.cycleTypeActive,
                        payOrgCode: this.channelCode
                    }).then(res => {
                        if (res.code == '00') {
                            var xAxis = [],
                                series = [[], [], []]
                            if (!res.data.length) return this.updateChartData(xAxis, series)
                            res.data.forEach((i, index) => {
                                xAxis.push(i.summaryDate)
                                // 总
                                series[0].push(Number(accDiv(i.totalTransAmount, 10000).toFixed(3).slice(0, -1)))
                                // 直营
                                series[1].push(Number(accDiv(i.directTransAmount, 10000).toFixed(3).slice(0, -1)))
                                // 团队
                                series[2].push(Number(accDiv(i.teamTransAmount, 10000).toFixed(3).slice(0, -1)))
                                if (index == res.data.length - 1) {
                                    this.updateChartData(xAxis, series)
                                }
                            })
                        }
                    })
                    break
                case 1:
                    //分润统计
                    getChannelProfitChartData({
                        dataType: this.cycleActive + 1,
                        selectType: 2,
                        censusType: this.cycleTypeActive,
                        payOrgCode: this.channelCode
                    }).then(res => {
                        if (res.code == '00') {
                            var xAxis = [],
                                series = [[], [], []]
                            if (!res.data.length) return this.updateChartData(xAxis, series)
                            res.data.forEach((i, index) => {
                                xAxis.push(i.summaryDate)
                                // 总
                                series[0].push(Number(toDecimal2(i.totalProfit)))
                                // 直营
                                series[1].push(Number(toDecimal2(i.directProfit)))
                                // 团队
                                series[2].push(Number(toDecimal2(i.teamProfit)))
                                if (index == res.data.length - 1) {
                                    this.updateChartData(xAxis, series)
                                }
                            })
                        }
                    })
                    break
                case 2:
                    //返现统计
                    getChannelProfitChartData({
                        dataType: this.cycleActive + 1,
                        selectType: 8,
                        censusType: this.cycleTypeActive,
                        payOrgCode: this.channelCode
                    }).then(res => {
                        if (res.code == '00') {
                            var xAxis = [],
                                series = [[], [], []]
                            if (!res.data.length) return this.updateChartData(xAxis, series)
                            res.data.forEach((i, index) => {
                                xAxis.push(i.summaryDate)
                                // 总
                                series[0].push(Number(toDecimal2(i.totalProfit)))
                                // 直营
                                series[1].push(Number(toDecimal2(i.directProfit)))
                                // 团队
                                series[2].push(Number(toDecimal2(i.teamProfit)))
                                if (index == res.data.length - 1) {
                                    this.updateChartData(xAxis, series)
                                }
                            })
                        }
                    })
                    break
                default:
                    // 激活、达标、商户数据统计
                    getChannelActivationChartData({
                        dataType: this.cycleActive + 1,
                        selectType: this.selectType,
                        censusType: this.cycleTypeActive,
                        payOrgCode: this.channelCode
                    }).then(res => {
                        if (res.code == '00') {
                            var xAxis = [],
                                series = [[], [], []]
                            if (!res.data.length) return this.updateChartData(xAxis, series)
                            res.data.forEach((i, index) => {
                                xAxis.push(i.summaryDate)
                                series[0].push(i.totalCount)
                                series[1].push(i.directCount)
                                series[2].push(i.teamCount)

                                if (index == res.data.length - 1) {
                                    this.updateChartData(xAxis, series)
                                }
                            })
                        }
                    })
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.dataChart {
    .tabs {
        position: sticky;
        top: 0;
        left: 0;
    }
    main {
        > .chart {
            height: 300px;
            margin: 0 10px;
            border-radius: 7px;
            display: flex;
            flex-direction: column;
            background-color: #f3f5f7;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            .chart-title {
                > text {
                    display: block;
                    padding: 20rpx;
                    font-size: 24rpx;
                    font-style: italic;
                }
            }
            .chart-box {
                width: 100%;
                flex: 1;
            }
        }
    }
}
</style>
