<template>
    <div class="data-ranking">
        <section class="header">
            <view class="tabs">
                <u-tabs :is-scroll="isTabsScroll" :list="tabsDeal" :current="active" @change="change" />
            </view>
            <view class="time-cycle" @click="changeTime">
                <text>{{ time }}</text>
                <u-icon name="arrow-down" />
            </view>
        </section>

        <u-field disabled right-icon="arrow-down-fill" label="选择通道:" placeholder="请选择要查询的通道" :value="channelName" @click="showChannelCode" />
        <u-select v-model="showChannelCodePicker" mode="single-column" :list="channleCodes" value-name="code" label-name="name" @confirm="onConfirmChannelCode" />

        <main>
            <no-content v-show="show" />
            <section v-show="!show" class="list">
                <div v-for="(r, index) in ranking" :key="index">
                    <p>
                        <span>{{ index + 1 }}</span>
                        <span>{{ r.agentName }}</span>
                        <span>{{ active == 0 ? `${accDiv(r.transAmount, 10000).toFixed(2)}万元` : active == 1 || active == 6 || active == 2 ? `${r.transAmount}元` : r.transNumber || 0 }}</span>
                    </p>
                    <u-line-progress :height="16" :active-color="index % 2 == 0 ? '#F4C6D8' : '#8CE1F3'" :show-percent="false" :percent="r.percentage" />
                </div>
            </section>
        </main>

        <u-picker v-model="showTimePicker" :show-time-tag="false" mode="time" :default-time="defaultTime" title="选择年月" :params="timePkckerOpt" @confirm="onConfirmTime" />
    </div>
</template>

<script>
import { getChannelDataRanking, getChannel } from '../../http/api'
import { dateFormat, accDiv } from '../../static/utils/date'

export default {
    name: 'DataRanking2',
    data() {
        return {
            defaultTime: dateFormat(new Date()),
            time: dateFormat(new Date()).substr(0, 7).replace('-', '.'),
            showTimePicker: false,
            active: 0,
            show: false,
            ranking: [],
            accDiv,
            tabsDeal: this.$store.state.currentMode == 1 ? [{ name: '交易' }, { name: '分润' }, { name: '返现' }, { name: '激活' }, { name: '达标' }, { name: '商户' }, { name: '台均' }] : [{ name: '交易' }, { name: '分润' }, { name: '返现' }, { name: '商户' }],
            timePkckerOpt: {
                year: true,
                month: true
            },
            showChannelCodePicker: false,
            channelName: '',
            channelCode: '',
            channleCodes: []
        }
    },
    computed: {
        isTabsScroll() {
            return this.$store.state.currentMode == 1
        }
    },
    methods: {
        showChannelCode() {
            getChannel().then(res => {
                if (res.code == '00') {
                    if (res.data.length != 0) {
                        this.channleCodes = res.data
                        this.showChannelCodePicker = true
                    } else {
                        this.$u.toast('暂无可选通道')
                    }
                }
            })
        },
        onConfirmChannelCode([{ label, value }]) {
            this.channelName = label
            this.channelCode = value
            this.showChannelCodePicker = false
            this.refreshData()
        },
        refreshData() {
            this.change(this.active)
        },
        getRankingList(yearMonth, statisType) {
            if (!this.channelCode) return this.$u.toast('请选择通道')
            this.ranking = []
            getChannelDataRanking({
                yearMonth: yearMonth,
                statisType: statisType,
                payMarketMode: parseInt(this.$store.state.currentMode),
                payOrgCode: this.channelCode
            }).then(res => {
                if (res.code == '00') {
                    this.ranking.length = 0
                    if (res.data.length != 0) {
                        this.show = false
                        let sum = 0
                        if (this.active == 0 || this.active == 1 || this.active == 6 || this.active == 2) {
                            res.data.forEach((i, index) => {
                                if (index == 0) {
                                    sum = i.transAmount
                                }
                                this.ranking.push(Object.assign({}, i, { percentage: accDiv(i.transAmount, sum).toFixed(2) != 1 ? parseInt(accDiv(i.transAmount, sum).toFixed(2).toString().split('.')[1]) : 100 }))
                            })
                        } else {
                            res.data.forEach((i, index) => {
                                if (index == 0) {
                                    sum = i.transNumber || 0
                                }
                                this.ranking.push(
                                    Object.assign({}, i, {
                                        percentage:
                                            accDiv(i.transNumber || 0, sum).toFixed(2) != 1
                                                ? accDiv(i.transNumber || 0, sum)
                                                      .toFixed(2)
                                                      .toString()
                                                      .split('.')[1]
                                                : 100
                                    })
                                )
                            })
                        }
                    } else {
                        this.show = true
                    }
                }
            })
        },
        changeTime() {
            this.showTimePicker = true
        },
        onConfirmTime(val) {
            this.time = val.year + '.' + val.month
            this.defaultTime = dateFormat(val.year + '-' + val.month)
            this.showTimePicker = false
            var typeActive
            if (this.$store.state.currentMode == 1) {
                if (this.active < 2 || this.active > 4) {
                    typeActive = this.active + 1
                } else if (this.active == 2) {
                    typeActive = 8
                } else {
                    typeActive = this.active
                }
            } else {
                if (this.active < 2) {
                    typeActive = this.active + 1
                } else if (this.active == 2) {
                    typeActive = 8
                } else {
                    typeActive = 6
                }
            }
            this.getRankingList(this.time.split('.').join(''), parseInt(typeActive))
        },
        change(index) {
            this.active = index
            var typeActive
            if (this.$store.state.currentMode == 1) {
                if (index < 2 || index > 4) {
                    typeActive = index + 1
                } else if (index == 2) {
                    typeActive = 8
                } else {
                    typeActive = index
                }
            } else {
                if (index < 2) {
                    typeActive = index + 1
                } else if (index == 2) {
                    typeActive = 8
                } else {
                    typeActive = 6
                }
            }
            this.getRankingList(this.time.split('.').join(''), parseInt(typeActive))
        }
    }
}
</script>

<style lang="scss" scoped>
.data-ranking {
    .header {
        position: sticky;
        top: 0;
        left: 0;
        display: flex;
        justify-content: space-between;
        background-color: #fff;
        .tabs {
            overflow-x: auto;
            flex: 1;
        }
        .time-cycle {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            padding: 0 10px;
            line-height: 100%;
            border-left: 1px solid #f3f5f7;
            color: #004ea9;
            font-weight: 500;
            > text {
                margin-right: 2px;
            }
        }
    }
    > main {
        .list {
            padding: 0 15px;
            > div {
                > p {
                    margin: 15px 0 5px 0;
                    > span {
                        vertical-align: middle;
                        display: inline-block;
                        &:nth-of-type(1) {
                            height: 15px;
                            line-height: 15px;
                            text-align: center;
                            background: #ebedf0;
                            border-radius: 5px;
                            padding: 0 3px 0 2px;
                            margin-right: 10px;
                        }
                        &:nth-of-type(2) {
                            margin-right: 20px;
                        }
                    }
                }
            }
        }
    }
}
</style>
