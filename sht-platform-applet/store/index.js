import Vue from 'vue';
import Vuex from 'vuex';

Vue.use(Vuex);

const getDefaultState = () => {
  return {
    token: uni.getStorageSync('token') || '',
    userInfo: uni.getStorageSync('userInfo') || {},
    appInfo: uni.getStorageSync('appInfo') || {},
    currentMode: uni.getStorageSync('currentMode') || null,
    appNotifications: uni.getStorageSync('appNotifications') || [], // 通知信息
    payOrgCodes: uni.getStorageSync('payOrgCodes') || [],
    isIosCheckPass: uni.getStorageSync('isIosCheckPass') || null
    // isIosCheckPass: 1
  };
};

const store = new Vuex.Store({
  state: getDefaultState(),
  mutations: {
    SET_TOKEN(state, val) {
      state.token = val;
      uni.setStorageSync('token', val);
    },
    SET_USERINFO(state, val) {
      state.userInfo = val;
      uni.setStorageSync('userInfo', val);
    },
    SET_PAYORG_CODES(state, list) {
      state.payOrgCodes = list || [];
      uni.setStorageSync('payOrgCodes', list);
    },
    SET_APPINFO(state, val) {
      state.appInfo = val;
      uni.setStorageSync('appInfo', val);
    },
    SET_CURRENTMODE(state, val) {
      state.currentMode = val;
      uni.setStorageSync('currentMode', val);
    },
    SET_APPNOTIFICATIONS(state, val) {
      state.appNotifications = val;
      uni.setStorageSync('appNotifications', val);
    },
    SET_ISIOSCHECKPASS(state, val) {
        state.isIosCheckPass = val;
        uni.setStorageSync('isIosCheckPass', val);
      },
    // 重置部分state属性
    RESET_SOMESTATE(state) {
      state = Object.assign(state, getDefaultState());
    }
  },
  actions: {
    login_out({ commit }) {
      uni.removeStorageSync('token');
      uni.removeStorageSync('userInfo');
      uni.removeStorageSync('appInfo');
      // uni.removeStorageSync('isIosCheckPass');
      // uni.removeStorageSync('currentMode')

      commit('RESET_SOMESTATE');

      uni.reLaunch({ url: 'pages/login/Login' });
    }
  }
});

export default store;
