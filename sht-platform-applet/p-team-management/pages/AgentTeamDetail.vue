<template>
    <div id="agent-team-detail">
        <main>
            <option-tab opts="按月,按日" :current="active" @select="select" />

            <section class="query-time" v-show="showDate">
                <p>查询日期</p>
                <div>
                    <u-field disabled :border-bottom="false" :value="startTime" placeholder="开始时间" @click="showStartPicker = true" />
                    <u-picker v-model="showStartPicker" title="开始时间" :show-time-tag="false" mode="time" @confirm="onConfirmStart" />
                    <span>—</span>
                    <u-field disabled :border-bottom="false" :value="endTime" placeholder="结束时间" @click="showEndPicker = true" />
                    <u-picker v-model="showEndPicker" title="结束时间" :show-time-tag="false" mode="time" @confirm="onConfirmEnd" />
                </div>
            </section>

            <div>
                <no-content v-if="show" />

                <div class="list-box" v-show="!show">
                    <div class="list" v-for="(l, index) in list" :key="index">
                        <p>统计{{ active == 1 ? "日期" : "月份"}}：{{l.summary}}</p>
                        <p>
                            <span>交易量</span>
                            <span>{{ l.agentPosTransAmount | toDecimal2 }}</span>
                        </p>
                        <p>
                            <span>分润收益</span>
                            <span>{{ l.agentPosProfitAmount }}</span>
                        </p>
                        <p>
                            <span>注册代理</span>
                            <span>{{ l.agentNewAgentNum }}</span>
                        </p>
                        <p>
                            <span>新增商户</span>
                            <span>{{ l.agentNewMerchantNum }}</span>
                        </p>
                    </div>
                </div>

            </div>
        </main>
        <u-back-top :scrollTop="scrollTop" />
    </div>
</template>

<script>
import OptionTab from "../../components/OptionTab.vue";
import { getAgentTeamDetail } from '../../http/api-direct';
import { dateFormat, toDecimal2, getDate } from "../../static/utils/date.js";

export default {
    name: 'AgentTeamDetail',
    components: {
        OptionTab
    },
    filters: {
        dateFormat,
        toDecimal2
    },
    data() {
        return {
            active: 0,
            showDate: false,
            showStartPicker: false,
            minDateStart: new Date(2019, 0, 1),
            maxDateStart: new Date(),
            currentDateStart: new Date(getDate(-30)),
            showEndPicker: false,
            minDateEnd: new Date(2019, 0, 1),
            maxDateEnd: new Date(),
            currentDateEnd: new Date(),
            startTime: "",
            endTime: "",
            show: false,
            list: [],
            scrollTop: 0
        }
    },
    onLoad() {
        this.getList("", "");
    },
    onReady() {
        uni.setNavigationBarTitle({
            title: `${this.$Route.query.agentName}团队详情`
        });
    },
    onPageScroll(e) {
        this.scrollTop = e.scrollTop;
    },
    methods: {
        getList(startTime, endTime) {
            getAgentTeamDetail({
                agentCode: this.$Route.query.agentCode,
                dataType: this.active == 0 ? 2 : 1,
                startTime: startTime,
                endTime: endTime,
            }).then((res) => {
                if (res.code == "00") {
                    uni.pageScrollTo({
                        scrollTop: 0
                    });
                    if (res.data.length != 0) {
                        this.show = false;
                        this.list = res.data;
                    } else {
                        this.show = true;
                    }
                }
            });
        },
        select(data) {
            if (this.active == data) return;
            this.active = data;
            switch (this.active) {
                case 1:
                    this.showDate = true;
                    this.startTime = dateFormat(getDate(-30)).substring(0, 10);
                    this.endTime = dateFormat(new Date()).substring(0, 10);
                    this.getList(`${this.startTime} 00:00:00`, `${this.endTime} 23:59:59`);
                    break;
                default:
                    this.showDate = false;
                    this.startTime = this.endTime = "";
                    this.getList("", "");
            }
            this.showDate = this.active == 1;
        },
        onConfirmStart(val) {
            this.startTime = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.minDateEnd = new Date(val);
            this.showStartPicker = false;
            this.getList(this.startTime + " 00:00:00", this.endTime + " 23:59:59");
        },
        onConfirmEnd(val) {
            this.endTime = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.maxDateStart = new Date(val);
            this.showEndPicker = false;
            this.getList(this.startTime + " 00:00:00", this.endTime + " 23:59:59");
        }
    }
}
</script>

<style lang="less" scoped>
#agent-team-detail {
    > main {
        > header {
            border: 0;
        }
        .query-time {
            margin-top: 20rpx;
            color: #999999;
            text-align: center;
            > p {
                height: 40rpx;
                font-size: 28rpx;
                margin: 0;
            }
            > div {
                height: 90rpx;
                border-radius: 10rpx;
                box-shadow: #a8a8a8 0 0 10rpx;
                margin: 0 30rpx;
                display: flex;
                /deep/ .u-label {
                    display: none !important;
                }
                /deep/ .u-field__input-wrap {
                    text-align: center;
                }
                > span {
                    line-height: 90rpx;
                }
            }
        }
        .list-box {
            padding: 20rpx 30rpx;
            .list {
                background: #fff;
                border-radius: 20rpx;
                margin-bottom: 20rpx;
                > p {
                    height: 80rpx;
                    line-height: 80rpx;
                    font-size: 28rpx;
                    padding: 0 30rpx;
                    margin: 0;
                    display: flex;
                    justify-content: space-between;
                    &:nth-of-type(1) {
                        font-size: 24rpx;
                        color: #999999;
                    }
                    &:nth-of-type(even) {
                        background: #fafbfc;
                    }
                    > span {
                        color: #666666;
                        &:nth-of-type(2) {
                            color: #222222;
                            font-weight: 600;
                        }
                    }
                }
            }
        }
    }
}
</style>