<template>
  <div id="merchantDetail">
    <header>
      <u-tag text="月交易量" shape="circleLeft" @click="toTransDetail" />
    </header>
    <main>
      <div>
        <p><span></span>商户信息</p>
        <p>
          <span>商户姓名</span>
          <span>{{ merchantName }}</span>
        </p>
        <p>
          <span>商户编号</span>
          <span
            ><text user-select selectable> {{ merchantId }} </text></span
          >
        </p>
      </div>
      <div>
        <p><span></span>商户交易</p>
        <p>
          <span>本月交易量</span>
          <span>{{ monthAmount | toDecimal2 }}</span>
        </p>
        <!-- <p>
                    <span>累计交易量</span>
                    <span>{{ totalAmount | toDecimal2 }}</span>
                </p> -->
      </div>
      <div v-if="terminalInfoVoList.length != 0">
        <p><span></span>机具概况</p>
        <section v-for="(t, index) in terminalInfoVoList" :key="index">
          <p>
            <span>终端类型</span>
            <span>{{ t.terminalModel }}</span>
          </p>
          <p>
            <span>机具终端号</span>
            <text user-select selectable>{{ t.terminalId }}</text>
          </p>
          <p v-if="t.firstTime != null">
            <span>首笔交易时间</span>
            <span>{{ t.firstTime }}</span>
          </p>
          <p v-if="t.activationTime != null">
            <span>激活时间</span>
            <span>{{ t.activationTime }}</span>
          </p>
          <p v-if="t.upToStandardTime != null">
            <span>首次达标时间</span>
            <span>{{ t.upToStandardTime }}</span>
          </p>
        </section>
      </div>
    </main>
  </div>
</template>

<script>
  import { merchantDetail } from '../../http/api';
  import { toDecimal2 } from '../../static/utils/date';

  export default {
    name: 'MerchantDetail',
    filters: {
      toDecimal2
    },
    data() {
      return {
        merchantName: '',
        merchantId: '',
        monthAmount: '',
        totalAmount: '',
        terminalInfoVoList: []
      };
    },
    onLoad() {
      merchantDetail(this.$Route.query.id).then((res) => {
        if (res.code == '00') {
          this.merchantName = res.data.merchantName;
          this.merchantId = res.data.merchantId;
          this.monthAmount = res.data.monthAmount;
          this.terminalInfoVoList = res.data.terminalInfoVoList;
        }
      });
    },
    methods: {
      toTransDetail() {
        this.$Router.push({
          name: 'MerchantTransDetail',
          params: { merchantId: this.$Route.query.id }
        });
      }
    }
  };
</script>

<style lang="less" scoped>
  #merchantDetail {
    min-height: 100%;
    padding-top: 20rpx;
    > header {
      display: flex;
      justify-content: flex-end;
      padding-bottom: 20rpx;
    }
    > main {
      > div {
        background-color: white;
        padding: 20rpx 30rpx;
        margin-bottom: 20rpx;
        border-radius: 10rpx;
        > p {
          position: relative;
          > span {
            color: #888888;
            &:nth-of-type(2) {
              position: absolute;
              right: 0;
            }
          }
          &:nth-of-type(1) {
            font-size: 30rpx;
            font-weight: 500;
            border-bottom: 1px solid #e5e5e5;
            padding-bottom: 20rpx;
            margin-bottom: 10rpx;
            > span {
              display: inline-block;
              width: 6rpx;
              height: 1.1rem;
              background-color: #004ea9;
              margin-right: 10rpx;
              position: relative;
              top: 0.2rem;
            }
          }
        }
        &:nth-of-type(3) {
          margin-bottom: 0;
          > section {
            > p {
              display: flex;
              padding: 10rpx 0;
              > span {
                width: 100px;
                flex-shrink: 0;
                color: #888888;
                &:nth-of-type(2) {
                  flex: 1;
                  text-align: 'right';
                  word-break: break-all;
                }
              }
            }
            &:not(:last-of-type) {
              border-bottom: 1rpx solid #a6a6a6;
            }
          }
        }
      }
    }
  }
</style>
