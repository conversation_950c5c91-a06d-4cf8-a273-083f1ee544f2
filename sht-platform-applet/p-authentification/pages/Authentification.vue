<template>
    <div id="authentification">
        <main>
            <u-form :model="user" ref="uForm" label-width="160">
                <p>基本信息</p>
                <u-form-item label="公司名称">
                    <u-input placeholder="请输入公司名称(选填)" v-model="user.companyName" />
                </u-form-item>
                <u-form-item label="代理商名称" prop="agentName">
                    <u-input placeholder="请输入代理商名称" v-model="user.agentName" />
                </u-form-item>

                <p>结算信息</p>
                <u-form-item label="姓名" prop="name">
                    <u-input placeholder="请输入真实姓名" v-model="user.name" />
                </u-form-item>
                <u-form-item label="身份证号" prop="idCardNo">
                    <u-input placeholder="请输入身份证号" v-model="user.idCardNo" />
                </u-form-item>
                <!-- <u-form-item label="开户行名称" prop="bankName">
                    <u-input placeholder="请输入开户行名称" v-model="user.bankName" />
                </u-form-item> -->
                <u-form-item label="银行卡号" prop="bankCardNo">
                    <u-input type="digit" placeholder="请输入银行卡号" v-model="user.bankCardNo" />
                </u-form-item>
                <u-form-item label="预留手机号" prop="mobile">
                    <u-input placeholder="请输入银行预留手机号" v-model="user.mobile" type="tel" />
                </u-form-item>
            </u-form>
            <view class="submit-btn">
                <u-button type="primary" @click="submit">提 交</u-button>
            </view>
        </main>
        <view class="agreement">
            <p><span @click="toWebview(0)">《服务协议》</span>及<span @click="toWebview(1)">《隐私规则》</span></p>
        </view>
    </div>
</template>

<script>
import { auth, getChannel } from '../../http/api'

export default {
    name: 'Authentification',
    data() {
        return {
            user: {
                companyName: '',
                agentName: '',
                name: '',
                idCardNo: '',
                // bankName: "",
                bankCardNo: '',
                mobile: ''
            },
            rules: {
                agentName: [{ required: true, message: '必填' }],
                mobile: [{ required: true, pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确' }],
                bankName: [{ required: true, message: '必填' }],
                bankCardNo: [{ required: true, message: '必填' }],
                name: [{ required: true, pattern: /^[^ ]+$/, message: '必填且不包含空格' }],
                idCardNo: [{ required: true, pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '身份证号不符合规则' }]
            }
        }
    },
    onLoad() {},
    onReady() {
        this.$refs.uForm.setRules(this.rules)
    },
    methods: {
        submit() {
            this.$refs.uForm.validate(valid => {
                if (valid) {
                    auth(this.user, this.$Route.query.token).then(res => {
                        if (res.code == '00') {
                            uni.showToast({
                                title: res.message,
                                icon: 'none'
                            })
                            this.$store.commit('SET_USERINFO', {
                                agentCode: this.$store.state.userInfo.agentCode,
                                realName: this.user.name,
                                accountName: this.user.name,
                                agentLevel: this.$store.state.userInfo.agentLevel,
                                mobile: this.$store.state.userInfo.mobile,
                                payMarketMode: this.$store.state.userInfo.payMarketMode,
                                companyName: this.user.name,
                                appAuthStatus: 1,
                                payTaxType: this.$store.state.userInfo.payTaxType,
                                taxationRegisterStatus: this.$store.state.userInfo.taxationRegisterStatus,
                                idCardNo: this.user.idCardNo,
                                showCreditDiscountOpenConf: this.$store.state.userInfo.showCreditDiscountOpenConf,
                                showSecTransOpenConf: this.$store.state.userInfo.showSecTransOpenConf,
                                memberLevel: this.$store.state.userInfo.memberLevel,
                                isMobileVerify: this.$store.state.userInfo.isMobileVerify,
                                loginName: this.$store.state.userInfo.loginName,
                                bankCardNo: this.user.bankCardNo,
                                bankName: this.user.bankName || '',
                                payChannelCode: this.$store.state.userInfo.payChannelCode,
                                cloudOpenStatus: this.$store.state.userInfo.cloudOpenStatus,
                                wsyDisplaySwitch: this.$store.state.userInfo.wsyDisplaySwitch,
                                dlgSignStatue: this.$store.state.userInfo.dlgSignStatue,
                                hkpaySignStatue: this.$store.state.userInfo.hkpaySignStatue,
                                receiveType: this.$store.state.userInfo.receiveType,
                                email: this.$store.state.userInfo.email,
                            })
                            setTimeout(() => {
                                this.$store.commit('SET_TOKEN', this.$Route.query.token)
                                this.$Router.pushTab({ name: 'Home' })
                                getChannel()
                                    .then(res => {
                                        res.code == '00' && this.$store.commit('SET_PAYORG_CODES', res.data)
                                    })
                                    .catch(err => console.log(err))
                            }, 1000)
                        }
                    })
                }
            })
        },
        toWebview(type) {
            const url = type ? 'https://openappyc.shtcloud.com/privacyRulesSht' : 'https://openappyc.shtcloud.com/userServRulesSht'
            this.$Router.push({ name: 'WebView', params: { title: type ? '隐私规则' : '服务协议', url } })
        }
    }
}
</script>

<style lang="less" scoped>
#authentification {
    min-height: 100%;
    background-color: #fff;
    main {
        /deep/ .u-form-item {
            padding: 20rpx 30rpx;
        }
        p {
            padding: 20rpx;
            background-color: #f3f5f7;
            font-weight: bold;
        }
    }
    .submit-btn {
        padding: 100rpx 30rpx 30rpx;
    }
    .agreement {
        display: flex;
        justify-content: center;
        > p {
            margin-top: 10rpx;
            > span {
                color: #004ea9;
            }
        }
    }
}
</style>
