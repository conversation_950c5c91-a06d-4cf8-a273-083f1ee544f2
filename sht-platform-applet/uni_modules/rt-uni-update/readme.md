## 整包更新和热更新组件 支持vue3 支持打开安卓、苹果应用市场，支持wgt静默更新

  - 一键式检查更新，同时支持整包升级与wgt资源包更新 支持打开安卓自带的应用市场和苹果appstore
  - 好看、实用、可自定义的客户端提示框
  - 支持强制更新，无法退出
  - 支持静默更新 无感知，下次启动后更新的内容自动生效
  - 支持覆盖原生tabar，原生导航栏

## 安装指引

1. 在插件市场打开本插件页面，在右侧点击`使用 HBuilderX 导入插件`，选择要导入的项目点击确定（建议使用uni_modules版本 非uni_modules版本不在维护，有需要自行修改）

2. 在`pages.json`中添加页面路径。注意：一定不要设置为pages.json中第一项（在1.1.9版本新增弹出一个合并页面路由的pages.json修改界面。点击确认按钮即可完成插件页面向项目pages.json的注册。HBuilderX 3.5.0+支持，无需手动添加）

```
"pages": [
		// ……其他页面配置
		{
			"path": "uni_modules/rt-uni-update/components/rt-uni-update/rt-uni-update",
			"style": {
				"disableScroll": true,
				"app-plus": {
					"backgroundColorTop": "transparent",
					"background": "transparent",
					"titleNView": false,
					"scrollIndicator": false,
					"popGesture": "none",
					"animationType": "fade-in",
					"animationDuration": 200

				}
			}
		}
]
```

3. 查看显示效果 (注意：这里只是查看显示效果，具体代码需要按照项目使用说明编写)

```

// App.vue的onShow中查看效果 如果无法跳转 请在`pages.json`中添加页面路径，参照第二步

uni.navigateTo({
	url: '/uni_modules/rt-uni-update/components/rt-uni-update/rt-uni-update'
});
					
```

## 项目使用说明 最重要！！！

- 注意！！！后端返回数据要求 字段如下 必须和以下一致才可以使用！！！

``` 
data:{
	// 版本更新内容 支持<br>自动换行
	describe: '1. 修复已知问题<br>
				2. 优化用户体验', 
	edition_url: '', //apk、wgt包下载地址或者应用市场地址  安卓应用市场 market://details?id=xxxx 苹果store itms-apps://itunes.apple.com/cn/app/xxxxxx
	edition_force: 0, //是否强制更新 0代表否 1代表是
	package_type: 1 //0是整包升级（apk或者appstore或者安卓应用市场） 1是wgt升级
	edition_issue:1 //是否发行  0否 1是 为了控制上架应用市场审核时不能弹出热更新框
	edition_number:100 //版本号 最重要的manifest里的版本号 （检查更新主要以服务器返回的edition_number版本号是否大于当前app的版本号来实现是否更新）
	edition_name:'1.0.0'// 版本名称 manifest里的版本名称
	edition_silence:0 // 是否静默更新 0代表否 1代表是
}
```


## 后端注意！！！
edition_number传这个参数是为了解决部分用户app长期不使用，第一次打开服务器查到的版本是最新的是wgt包，但是之前app有过整包更新，如果直接更新最新wgt的话，会出现以前的整包添加的原生模块或者安卓权限无法使用，所以后端查询版本必须返回大于当前edition_number版本的最新的整包apk地址或者是应用市场地址，如果没有大于edition_number的整包，就返回最新的wgt包地址就行。

- 前端示例代码 或者根据实际业务修改 如果需要自动检测新版本，建议写在App.vue的onShow中

```
import silenceUpdate from '@/uni_modules/rt-uni-update/js_sdk/silence-update.js' //引入静默更新

//#ifdef APP-PLUS 

// 获取本地应用资源版本号
		plus.runtime.getProperty(plus.runtime.appid, (inf) => {
			//获取服务器的版本号
			uni.request({
				url: 'http://127.0.0.1:8088/edition_manage/get_edition', //示例接口
				data: {
					edition_type: plus.runtime.appid,
					version_type: uni.getSystemInfoSync().platform, //android或者ios
					edition_number: inf.versionCode // 打包时manifest设置的版本号 
				},
				success: (res) => {
					//res.data.xxx根据后台返回的数据决定（我这里后端返回的是data），所以是res.data.data 
					//判断后台返回版本号是否大于当前应用版本号 && 是否发行 （上架应用市场时一定不能弹出更新提示）
					if (Number(res.data.data.edition_number) > Number(inf.versionCode) && res
						.data.data.edition_issue == 1) {

						//如果是wgt升级，并且是静默更新 （注意！！！ 如果是手动检查新版本，就不用判断静默更新，请直接跳转更新页，不然点击检查新版本后会没反应）
						if (res.data.data.package_type == 1 && res.data.data.edition_silence == 1) {

							//调用静默更新方法 传入下载地址
							silenceUpdate(res.data.data.edition_url)

						} else {
							//跳转更新页面 （注意！！！如果pages.json第一页的代码里有一打开就跳转其他页面的操作，下面这行代码最好写在setTimeout里面设置延时3到5秒再执行）
							uni.navigateTo({
								url: '/uni_modules/rt-uni-update/components/rt-uni-update/rt-uni-update?obj=' +
									JSON.stringify(res.data.data)
							});
						}
					} else {

						// 如果是手动检查新版本 需开启以下注释
						/* uni.showModal({
							title: '提示',
							content: '已是最新版本',
							showCancel: false
						}) */
					}
				}


			})

		});
		
		//#endif	
			
			
		
```
## 如有问题，请加qq 965969604
