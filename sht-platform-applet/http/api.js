import request from './request';

// 获取app信息
export function getAppInfo(abbreviation) {
  return request({
    url: '/inf/queryByAbbreviation',
    method: 'get',
    data: { abbreviation: abbreviation }
  });
}

// 登录
export function login(data) {
  return request({
    url: '/user/iosLogin',
    method: 'post',
    data
  });
}
// 根据APP版本查是否支持
export function appVersion(data) {
  return request({
    url: '/inf/updateCheck',
    method: 'post',
    data
  });
}
// 登录有效期
export function loginExpire() {
  return request({
    url: '/user/loginExpire',
    method: 'get'
  });
}

// 注册
export function register(data) {
  return request({
    url: '/user/register',
    method: 'post',
    data
  });
}

// 获取验证码
export function getSmsCode(data) {
  return request({
    url: '/user/sendMsgCode',
    method: 'post',
    data
  });
}

// 绑定手机号码
export function bindPhone(data) {
  return request({
    url: '/user/mobileBind',
    method: 'post',
    data
  });
}
// 校验手机号是否存在
export function preValidMobile(data) {
  return request({
    url: '/user/preValidMobile',
    method: 'post',
    data
  });
}
// 查询直属/非直属代理商
export function queryAgentList(data) {
  return request({
    url: '/agentManage/queryAgentList',
    method: 'post',
    data
  });
}

// 查询机具
export function gatTerminals(agentCode) {
  return request({
    url: '/terminal/queryStock',
    method: 'get',
    data: { agentCode: agentCode }
  });
}

// 终端列表
export function getAgentTerminalList(data) {
  return request({
    url: '/terminal/queryTerminalNoList',
    method: 'post',
    data
  });
}

// 终端详情
export function getTerminalDetail(data) {
  return request({
    url: '/terminal/queryTerminalRule',
    method: 'post',
    data
  });
}

// 机具回撤

// 选择下拨查询终端号
export function getTeminalNos(data) {
  return request({
    url: '/terminal/selDownTerList',
    method: 'post',
    data
  });
}

// 选择下拨查询父级返现规则序号数量
export function getRangeStirCount(data) {
  return request({
    url: '/terminal/selectRuleCountBySel',
    method: 'post',
    data
  });
}

// 选择下拨
export function rangeStir(data) {
  return request({
    url: '/terminal/dialDownBySel',
    method: 'post',
    data
  });
}

// 查询已实名直属代理商
export function getDirRealAgent(agentName) {
  return request({
    url: '/terminal/directlyUnderAgent',
    method: 'get',
    data: { agentName: agentName }
  });
}

// 查询下级代理商结算价模板
export function getAgentRateType() {
  return request({
    url: '/agentRateType/getRateTypeList',
    method: 'get'
  });
}

// 根据结算价模板查询代理商结算底价
export function getSettlementPrice(agentCode, rateType) {
  return request({
    url: '/agentManage/rateInfoDetails',
    method: 'get',
    data: { agentCode: agentCode, rateType: rateType }
  });
}

// 结算底价修改
export function changeSettmentPrice(data) {
  return request({
    url: '/agentManage/updatePrice',
    method: 'post',
    data
  });
}

// 一代修改结算价模板
export function changeSettmentPrices(data) {
  return request({
    url: '/agentRateType/editAgentRateModelType',
    method: 'post',
    data
  });
}

// 一代新增结算价模板
export function addSettmentPrices(data) {
  return request({
    url: '/agentRateType/insertAgentRateModelType',
    method: 'post',
    data
  });
}

// 一代新增结算政策
export function addSettmentPolicy(data) {
  return request({
    url: '/settleRate/addPolicyByFirst',
    method: 'post',
    data
  });
}
// 结算政策编辑(普通)
export function editSettmentPolicy(data) {
  return request({
    url: '/settleRate/editPolicy',
    method: 'post',
    data
  });
}
// 结算政策编辑(聚合通道)
export function editSettmentMultPolicy(data) {
  return request({
    url: '/settleRate/editMultPolicy',
    method: 'post',
    data
  });
}
// 结算政策详情
export function detailSettmentPolicy(data) {
  return request({
    url: '/settleRate/detailPolicy',
    method: 'post',
    data
  });
}
// 结算政策名称编辑
export function editSettmentPolicyName(data) {
  return request({
    url: '/settleRate/editPolicyName',
    method: 'post',
    data
  });
}
// 开通下级政策（聚合）
export function openMultLowerSettmentPolicy(data) {
  return request({
    url: '/settleRate/openMultLower',
    method: 'post',
    data
  });
}
// 开通下级政策(普通）
export function openLowerSettmentPolicy(data) {
  return request({
    url: '/settleRate/openLower',
    method: 'post',
    data
  });
}
// 一代关闭政策
export function closeLowerSettmentPolicy(data) {
  return request({
    url: '/settleRate/closeLower',
    method: 'post',
    data
  });
}
// 聚合通道列表
export function findJhzfChannel(data) {
  return request({
    url: '/settleRate/jhzfChannel',
    method: 'post',
    data
  });
}
// 子通道列表(聚合)
export function findSubChannelList(data) {
  return request({
    url: '/settleRate/findSubChannelList',
    method: 'post',
    data
  });
}
// 分页查询结算政策
export function getSettmentPolicyList(data) {
  return request({
    url: '/settleRate/queryPolicyPageList',
    method: 'post',
    data
  });
}
// 查询下级结算政策列表(含开通状态)
export function getOpenLowerList(data) {
  return request({
    url: '/settleRate/unOpenLowerList',
    method: 'post',
    data
  });
}

// 区间下拨查询父级返现规则序号数量
export function selectRuleTypeCount(data) {
  return request({
    url: '/terminal/selectRuleTypeCount',
    method: 'post',
    data
  });
}

// 区间下拨
export function stir(data) {
  return request({
    url: '/terminal/dialDown',
    method: 'post',
    data
  });
}

// 查询可下拨机具
export function getRetraceList(agentCode, payOrgCode) {
  return request({
    url: '/terminalRetrace/queryRetraceList',
    method: 'get',
    data: { agentCode: agentCode, payOrgCode }
  });
}

// 回撤
export function retrace(data) {
  return request({
    url: '/terminalRetrace/launch',
    method: 'post',
    data
  });
}

// 查询机具列表（机具管理-机具管理->）
export function getTerminalTypeList() {
  return request({
    url: '/appTerminalRecord/getTerminalTypeList',
    method: 'get'
  });
}

// 终端类型列表
export function getTerminalPageList(data) {
  return request({
    url: '/appTerminalRecord/getTerminalPageList',
    method: 'post',
    data
  });
}
// 终端概况/我的终端/终端激活详情
export function getTerActivateDetail(id) {
  return request({
    url: '/myTerminal/getTerActivateDetail',
    method: 'get',
    data: { id }
  });
}
// 终端概况/我的终端/终端流量费详情
export function getTerSimDetail(id) {
  return request({
    url: '/myTerminal/getTerSimDetail',
    method: 'get',
    data: { id }
  });
}
// 终端概况/我的终端/终端流量费详情/入账信息
export function getTerSimFlowDetail(id) {
  return request({
    url: '/myTerminal/getTerSimFlowDetail',
    method: 'get',
    data: { id }
  });
}

// 终端管理/终端流量卡
export function getTerSimNoDetail(data) {
  return request({
    url: '/myTerminal/getTerSimNoDetail',
    method: 'get',
    data
  });
}

// 终端管理/查询SN达标情况
export function querySnReachDetail(payOrgCode, sn) {
  return request({
    url: `/terminal/querySnReachDetail`,
    method: 'get',
    data: { payOrgCode, sn }
  });
}

// 机具详情
export function getMechineDetail(data) {
  return request({
    url: '/appTerminalRecord/getTerminalDetail',
    method: 'post',
    data
  });
}

// 根据终端号查询代理商
export function getCashBackAgent(terminalNo, payOrgCode) {
  return request({
    url: '/cashback/getDirForModify',
    method: 'get',
    data: { terminalNo: terminalNo, payOrgCode }
  });
}

// 查询返现规则
export function getCashBackRules(data) {
  return request({
    url: '/cashback/query',
    method: 'post',
    data
  });
}

// 修改返现规则
export function changeCashBackRules(data) {
  return request({
    url: '/cashback/modify',
    method: 'post',
    data
  });
}

// 查询渠道列表
export function getChannel() {
  return request({
    url: '/fastMerchantAccess/allChannel',
    method: 'post'
  });
}
// 商户链接拓展/查询渠道列表
export function getMerchantUrlChannel() {
  return request({
    url: '/channel/getAgentChannelList',
    method: 'get'
  });
}

// 获取渠道商户APP下载url
export function getMerchantUrl(data) {
  return request({
    url: '/fastMerchantAccess/appUrl',
    method: 'post',
    data
  });
}

// 获取直属商户（不分页）
export function getMerchantList(merchantName, payOrgCode) {
  return request({
    url: '/businessApply/queryDirectMerchList',
    method: 'get',
    data: { merchantName: merchantName, payOrgCode }
  });
}

// 查询商户费率
export function getMerchantRate(data) {
  return request({
    url: '/businessApply/getMerchRateInfo',
    method: 'post',
    data
  });
}

// 修改商户费率
export function changeMerchantRate(data) {
  return request({
    url: '/businessApply/editMerchRateInfo',
    method: 'post',
    data
  });
}

// 查询kq渠道编号判断是否显示商户链接拓展+渠道提现菜单
export function getChannelCode() {
  return request({
    url: '/channel/getPayChannelInf',
    method: 'get'
  });
}

// 查询拓展商户链接列表
export function getMerchantsUrlList(payChannelNo) {
  return request({
    url: '/channel/getUrlList',
    method: 'get',
    data: { payChannelNo }
  });
}

// 添加拓展商户链接
export function addMerchantUrl(data) {
  return request({
    url: '/channel/addMerchantUrl',
    method: 'post',
    data
  });
}

// 查询商户押金返还明细
export function getDepRetDetails(data) {
  return request({
    url: '/channel/queryDepositReturnInf',
    method: 'post',
    data
  });
}

// 查询渠道账户余额
export function getWalletBalance(payChannelNo) {
  return request({
    url: '/channel/queryWalletBalance',
    method: 'get',
    data: { payChannelNo }
  });
}

// 查询单笔提现限额
export function queryWithdrawLimit(payChannelNo) {
  return request({
    url: '/channel/queryWithdrawLimit',
    method: 'get',
    data: { payChannelNo }
  });
}

// 渠道提现
export function channelWithdraw(data) {
  return request({
    url: '/channel/withdraw',
    method: 'post',
    data
  });
}

// 渠道提现记录
export function getChannelWithdrawRecord(data) {
  return request({
    url: '/channel/withdraw/queryPage',
    method: 'post',
    data
  });
}

// 查询渠道结算卡
export function getBankCard(data) {
  return request({
    url: '/agentBankCard/queryBankCard',
    method: 'post',
    data
  });
}

// 设置渠道默认结算卡
export function setDefaultBankCard(data) {
  return request({
    url: '/agentBankCard/setDefaultBankCard',
    method: 'post',
    data
  });
}

// 查询开户行支行所在省份
export function getBranchBankProvince(data) {
  return request({
    url: '/agentBankCard/queryProvince',
    method: 'post',
    data
  });
}

// 查询开户行支行所在市县
export function getBranchBankCity(data) {
  return request({
    url: '/agentBankCard/queryProvinceChild',
    method: 'post',
    data
  });
}

// 查询开户行支行银行类别
export function getBranchBankBankType(data) {
  return request({
    url: '/agentBankCard/queryBankType',
    method: 'post',
    data
  });
}

// 查询开户行支行列表
export function getBranchBankList(data) {
  return request({
    url: '/agentBankCard/queryBankBranchList',
    method: 'post',
    data
  });
}

// 添加渠道结算卡
export function addChannelBankCard(data) {
  return request({
    url: '/agentBankCard/addOrUpdate',
    method: 'post',
    data
  });
}

// 查询直属/非直属商户（分页）
export function queryMerchantList(data) {
  return request({
    url: '/merchantInfo/queryMerchantInfo',
    method: 'post',
    data
  });
}

// 商户详情
export function merchantDetail(id) {
  return request({
    url: '/merchantInfo/detailInfo',
    method: 'get',
    data: { id: id }
  });
}

// 分润明细列表
export function getProfitList(data) {
  return request({
    url: '/profitSharingRecord/query',
    method: 'post',
    data
  });
}

// 钱包余额查询
export function walletMoney() {
  return request({
    url: '/account/queryAccount',
    method: 'get'
  });
}

// 查看绑定结算卡
export function getSettleCard() {
  return request({
    url: '/agentManage/findCard',
    method: 'get'
  });
}

// 查询是否设置支付密码
export function getPayPsw() {
  return request({
    url: '/account/payPwdStatus',
    method: 'get'
  });
}

// 设置/找回支付密码
export function setPayPwd(data) {
  return request({
    url: '/account/setPayPwd',
    method: 'post',
    data
  });
}

// 修改支付密码
export function changePayPwd(data) {
  return request({
    url: '/account/editPayPwd',
    method: 'post',
    data
  });
}

// 提现接口
export function withdraw(data) {
  return request({
    url: '/account/withdraw',
    method: 'post',
    data
  });
}
// 提现接口
export function getWithdrawRecord(data) {
  return request({
    url: '/orderQuery/queryWithdrawRecordPageList',
    method: 'post',
    data
  });
}
// 首页金额
export function getHomeProfit() {
  return request({
    url: '/homePage/todayMySelfAmount',
    method: 'get'
  });
}

// 账单流水
export function bill(data) {
  return request({
    url: '/account/queryFlow',
    method: 'post',
    data
  });
}

// 数据-累计数据
export function getTotal(payMarketMode) {
  return request({
    url: '/summary/totalAmount',
    method: 'get',
    data: { payMarketMode: payMarketMode }
  });
}

// 本月直营数据
export function getDirectDataThisMonth(payMarketMode) {
  return request({
    url: '/summary/thisMonthDirectData',
    method: 'get',
    data: { payMarketMode: payMarketMode }
  });
}

// 本月团队数据
export function getTeamDataThisMonth(payMarketMode) {
  return request({
    url: '/summary/thisMonthTeamData',
    method: 'get',
    data: { payMarketMode: payMarketMode }
  });
}

// 直营统计数据
export function getDirectDataList(data) {
  return request({
    url: '/summary/getAppDirectDataList',
    method: 'post',
    data
  });
}

// 团队统计数据
export function getTeamDataList(data) {
  return request({
    url: '/summary/getAppTeamDataList',
    method: 'post',
    data
  });
}

// 个人信息认证
export function auth(data, token) {
  return request({
    url: '/user/auth',
    method: 'post',
    data,
    token
  });
}

// 我的银行卡
// export function getBankCard() {
// 	return request({
// 		url: "/agentManage/findCard",
// 		method: "get"
// 	})
// }

// 更新结算卡
export function updateCard(data) {
  return request({
    url: '/agentManage/updateCard',
    method: 'post',
    data
  });
}

// 税筹注册发送验证码
export function taxRegisterSms(data) {
  return request({
    url: '/taxation/userRegisterSms',
    method: 'post',
    data
  });
}

// 税筹注册
export function taxRegister(data) {
  return request({
    url: '/taxation/userRegister',
    method: 'post',
    data
  });
}

// 修改密码
export function updatePwd(data) {
  return request({
    url: '/user/updatePwd',
    method: 'post',
    data
  });
}

// 退出登录
export function exit() {
  return request({
    url: '/user/exit',
    method: 'get'
  });
}

// 查询代理商详情
// export function getAgentDetail(agentCode) {
// 	return request({
// 		url: "/user/detail/" + agentCode,
// 		method: "get"
// 	})
// }

/*
 * 手游推广
 */

// 游戏列表
export function gameList(data) {
  return request({
    url: '/gameInfo/gameInfoList',
    method: 'post',
    data
  });
}

// 游戏绑定
export function gamePlayerBind(playerCode) {
  return request({
    url: `/gamePlayerBind/infoBound`,
    method: 'get',
    data: { playerCode }
  });
}

// 游戏绑定记录
export function gameBindList(data) {
  return request({
    url: '/gamePlayerBind/lowBoundList',
    method: 'post',
    data
  });
}

//当前代理商分润比例查询
export function nowAgentRate(data) {
  return request({
    url: '/gameAgentRate/queryGameRate',
    method: 'post',
    data
  });
}

// 批量开通下级游戏分成比例
export function batchOpeningRate(data) {
  return request({
    url: '/gameAgentRate/batchOpeningRate',
    method: 'post',
    data
  });
}

// 单个修改下级游戏分成比例
export function editChildAgentRate(data) {
  return request({
    url: '/gameAgentRate/editChildAgentRate',
    method: 'post',
    data
  });
}

// 当前代理商的下级分成记录
export function nowAgentLowList(data) {
  return request({
    url: '/gameAgentRate/queryAll',
    method: 'post',
    data
  });
}

// 查询是否首次批量开通
export function queryExistOpening(data) {
  return request({
    url: '/gameAgentRate/queryExistOpening',
    method: 'post',
    data
  });
}

// 后续代理商开通
export function followUpOpening(data) {
  return request({
    url: '/gameAgentRate/followUpOpening',
    method: 'post',
    data
  });
}

// 修改游戏分成比例
export function editGameRate(data) {
  return request({
    url: '/gameAgentRate/editGameRate',
    method: 'post',
    data
  });
}

// 代理商结算明细
export function agentSettle(data) {
  return request({
    url: '/gameAgentSettle/queryAll',
    method: 'post',
    data
  });
}

// 游戏玩家充值明细
export function playerRecharge(data) {
  return request({
    url: '/gamePlayerRecharge/queryAll',
    method: 'post',
    data
  });
}

/*
 * 电商小程序推广
 */

// 商品列表
export function getGoodsList(data) {
  return request({
    url: '/appletManage/queryProductList',
    method: 'post',
    data
  });
}

// 获取对应商品的小程序码
export function getMiniProgramQrCode(id) {
  return request({
    url: `/appletManage/getQrCodeByPid`,
    method: 'get',
    data: { pid: id }
  });
}

// 订单消费记录列表
export function getOrderPushList(data) {
  return request({
    url: '/appletManage/queryNewOrderList',
    method: 'post',
    data
  });
}

/*
 * 图表数据
 */

// 渠道、直营首页交易量图表数据
export function getChartDataList(data) {
  return request({
    url: '/dataAnalysis/getChartDataList',
    method: 'post',
    data
  });
}

// 总、直营、团队收益统计图表
export function getProfitChartData(data) {
  return request({
    url: '/dataAnalysis/getChartProfitList',
    method: 'post',
    data
  });
}

// 激活、达标、渠道统计图表
export function getActivationChartData(data) {
  return request({
    url: '/dataAnalysis/getChartCountList',
    method: 'post',
    data
  });
}
// 通道交易量图表数据
export function getChannelChartDataList(data) {
  return request({
    url: '/channelAnalysis/getChartDataList',
    method: 'post',
    data
  });
}

// 通道收益统计图表
export function getChannelProfitChartData(data) {
  return request({
    url: '/channelAnalysis/getChartProfitList',
    method: 'post',
    data
  });
}

// 激活、达标、渠道统计图表-通道
export function getChannelActivationChartData(data) {
  return request({
    url: '/channelAnalysis/getChartCountList',
    method: 'post',
    data
  });
}

/**
 * 货款相关
 */

// 查询货款
export function queryLoans(data) {
  return request({
    url: '/account/queryLoans',
    method: 'post',
    data
  });
}

// 查询上级或渠道货款金额
export function getLoansAccountAmount(type) {
  return request({
    url: `/appLoansAccount/getLoansAccountAmount`,
    method: 'get',
    data: { type }
  });
}

// 查询货款订单分页
export function getLoansOrderPageList(data) {
  return request({
    url: '/appLoansOrder/getLoansOrderPageList',
    method: 'post',
    data
  });
}
// 货款账户历史
export function getLoansAccountHisPageList(data) {
  return request({
    url: '/appLoansAccount/getLoansAccountHisPageList',
    method: 'post',
    data
  });
}
// 货款申请
export function applyLoansOrder(data) {
  return request({
    url: '/appLoansOrder/applyLoansOrder',
    method: 'post',
    data
  });
}
// 查询货款单详情
export function getLoansOrderDetail(id) {
  return request({
    url: `/appLoansOrder/getLoansOrderDetail`,
    method: 'get',
    data: { id }
  });
}
// 货款审批
export function applyUpdateOrder(data) {
  return request({
    url: '/appLoansOrder/applyUpdateOrder',
    method: 'post',
    data
  });
}
// 分页查询渠道货款账户
export function getAppLoansAccounPageList(data) {
  return request({
    url: '/appLoansAccount/getAppLoansAccounPageList',
    method: 'post',
    data
  });
}
// 修改代理商货款比例
export function updateLoansAccountRate(data) {
  return request({
    url: '/appLoansAccount/updateLoansAccountRate',
    method: 'post',
    data
  });
}

// 获取数据排名
export function getDataRanking(data) {
  return request({
    url: '/dataAnalysis/rank',
    method: 'post',
    data
  });
}
// 获取数据排名-通道
export function getChannelDataRanking(data) {
  return request({
    url: '/channelAnalysis/rank',
    method: 'post',
    data
  });
}

// 查询全部通知
export function getAllNotifications() {
  return request({
    url: '/notice/queryAll',
    method: 'get'
  });
}

// 获取最新5条通知
export function getNotifications() {
  return request({
    url: '/notice/getNoticeNew',
    method: 'post'
  });
}

// 根据id查询详细通知内容
export function getNoticeById(id) {
  return request({
    url: '/notice/getNoticeById',
    method: 'get',
    data: { id }
  });
}

// 终端查询本级信息（我的终端）
export function queryMyTerDetails() {
  return request({
    url: '/myTerminal/queryMyTerDetails',
    method: 'get'
  });
}

// 终端查询下级直属代理商信息（我的终端）
export function queryMyAgentTerSummary(data) {
  return request({
    url: '/myTerminal/queryMyAgentTerSummary',
    method: 'post',
    data
  });
}

// 查询本级/下级终端列表(我的/xxx的终端)
export function queryMyTerList(data) {
  return request({
    url: '/myTerminal/queryMyTerList',
    method: 'post',
    data
  });
}

// 终端数量
export function getAgentTerminalCount() {
  return request({
    url: '/appAgentTerminalSummary/getAgentTerminalCount',
    method: 'get'
  });
}

// 未激活伪激活/本级代理商终端数据
export function getCurentAgentActiviDetail(payOrgCode) {
  return request({
    url: '/appAgentTerminalSummary/getCurentAgentActiviDetail',
    method: 'get',
    data: { payOrgCode }
  });
}

// 未激活伪激活/查询直属下级代理商终端数据
export function getAgentActiviList(data) {
  return request({
    url: '/appAgentTerminalSummary/getAgentActiviList',
    method: 'post',
    data
  });
}

// 未激活伪激活/查询直属下级代理商终端数据/代理商未激活伪激活终端列表
export function getTerminalActiviList(data) {
  return request({
    url: '/appAgentTerminalSummary/getTerminalActiviList',
    method: 'post',
    data
  });
}

// 获取分享素材类型
export function getTagType() {
  return request({
    url: '/share/getImgTagType',
    method: 'post'
  });
}

// 获取素材图片
export function getTagImgs(data) {
  return request({
    url: '/share/getImgUrlShareOther',
    method: 'post',
    data
  });
}

// 渠道管理/获取菜单展示情况
export function getMenuViewStatus() {
  return request({
    url: '/agentManage/selChlMenuViewStatus',
    method: 'get'
  });
}

// 商户优惠VIP费率

// 分页查询规则信息
export function getRulesByPage(data) {
  return request({
    url: '/agentDiscountRate/queryRulesByPage',
    method: 'post',
    data
  });
}
//  新增代理返现规则模板
export function addRule(data) {
  return request({
    url: '/agentDiscountRate/addRule',
    method: 'post',
    data
  });
}
//  获取直属下级代理商信息
export function getDirAgentList(data) {
  return request({
    url: '/agentDiscountRate/getDirAgentList',
    method: 'post',
    data
  });
}
//  获取当前登录人的查询所有规则信息(返现规则、费率规则)
export function getAllRules() {
  return request({
    url: '/agentDiscountRate/queryAllRules',
    method: 'get'
  });
}
//  批量下级设置返现规则
export function batchSetRule(data) {
  return request({
    url: '/agentDiscountRate/batchSetRule',
    method: 'post',
    data
  });
}
//  一代修改返现规则
export function editRule(data) {
  return request({
    url: '/agentDiscountRate/editRule',
    method: 'post',
    data
  });
}
//  修改下级返现金额
export function batchSetAmount(data) {
  return request({
    url: '/agentDiscountRate/editCashbackAmount',
    method: 'post',
    data
  });
}
//  获取操作状态
export function getOptPmsState() {
  return request({
    url: '/agentDiscountRate/getOptPmsState',
    method: 'get'
  });
}
//  删除返现规则
export function delRuleInfoById(id) {
  return request({
    url: '/agentDiscountRate/delRuleInfoById',
    method: 'get',
    data: { id }
  });
}
//  分页查询商户提现手续费订单
export function queryMerRateRecordByPage(data) {
  return request({
    url: '/merchWithdraw/queryMerRateRecordByPage',
    method: 'post',
    data
  });
}
//  获取商户提现手续费明细
export function getMerRateRecordDetail(id) {
  return request({
    url: '/merchWithdraw/getMerRateRecordDetail',
    method: 'get',
    data: { id }
  });
}
//  商户检测统计功能
export function getMerchSummaryInfData() {
  return request({
    url: '/merchSummary/getMerchSummaryInfData',
    method: 'get'
  });
}
//  查询指定类型的商户列表
export function getMerchListBySelType(selType) {
  return request({
    url: '/merchSummary/getMerchListBySelType',
    method: 'get',
    data: { selType }
  });
}
// 获取该商户下近半年交易明细列表
export function getMerchDetailListByMerchNo(merchantId) {
  return request({
    url: '/merchSummary/getMerchDetailListByMerchNo',
    method: 'get',
    data: { merchantId }
  });
}
//  我的/云户通开户
export function openBankAccount(data) {
  return request({
    url: '/appCloud/openBankAccount',
    method: 'post',
    data
  });
}
//  我的钱包/查询开户状态
export function getRegisterStatus() {
  return request({
    url: '/appRegister/getRegisterStatus',
    method: 'post'
  });
}
// 我的/设备兑换券
export function couponRecord(data) {
  return request({
    url: '/myInfo/couponRecord',
    method: 'post',
    data
  });
}
// 获取展示代理编号列表
export function getListAgentCode() {
  return request({
    url: '/apply/listAgentCode',
    method: 'get'
  });
}
// 获取网卡信息列表
export function getListCardType() {
  return request({
    url: '/apply/listCardType',
    method: 'get'
  });
}
// 申请信用卡链接
export function generateApplyLink(data) {
  return request({
    url: '/apply/link',
    method: 'post',
    data
  });
}
// 查询申请开卡记录
export function getApplyCardList(data) {
  return request({
    url: '/apply/applyCardList',
    method: 'post',
    data
  });
}
// 图片模块素材/获取参数配置图片资源
export function getSystemParamImgUrl(data) {
  return request({
    url: '/share/getSystemParamImgUrl',
    method: 'post',
    data
  });
}
// 个人图像上传
export function headPortraitSetting(data) {
  return request({
    url: '/user/headPortraitSetting',
    method: 'post',
    data
  });
}
// 个人图像获取
export function getHeadPortrait() {
  return request({
    url: '/user/getHeadPortrait',
    method: 'get'
  });
}
// 钉灵工签约
export function dlgSign(data) {
  return request({
    url: '/dlgUser/sign',
    method: 'post',
    data
  });
}
// 获取万商云授权URL
export function getChnToken(data) {
  return request({
    url: '/wsysaas/getChnToken',
    method: 'post',
    data
  });
}
// 登录状态
export function loginSwitch(data) {
  return request({
    url: '/user/loginSwitch',
    method: 'post',
    data
  });
}
// SN序列号查询
export function queryTerminalNoList(data) {
  return request({
    url: '/businessApply/queryTerminalNoList',
    method: 'get',
    data
  });
}
export function getMerchantQrCode(data) {
  return request({
      url: '/wsysaas/merchantQrCode',
      method: 'get',
      data
  })
}

// 海科签约
export function hkpaySign(data) {
    return request({
        url: '/hkpay/sign',
        method: 'post',
        data
    })
}

// 省查询（海科）
export function getHkpayProvince(data) {
    return request({
        url: '/hkpay/province',
        method: 'post',
        data
    })
}

// 市查询（海科）
export function getHkpayCity(data) {
    return request({
        url: '/hkpay/city',
        method: 'post',
        data
    })
}

// 区县查询（海科）
export function getHkpayCounty(data) {
    return request({
        url: '/hkpay/county',
        method: 'post',
        data
    })
}


// 银行类别查询（海科）
export function getHkpayBankType(data) {
    return request({
        url: '/hkpay/bankType',
        method: 'post',
        data
    })
}

// 银行支行网点查询（海科）
export function getHkpayBankSub(data) {
    return request({
        url: '/hkpay/bankSub',
        method: 'post',
        data
    })
}

// 修改银行卡信息（同步海科）
export function hkpayEditBankCard(data) {
    return request({
        url: '/agentManage/hkpayEditBankCard',
        method: 'post',
        data
    })
}