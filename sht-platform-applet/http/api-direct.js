import request from './request'

// 获取团队管理代理商列表
export function getDirectAgentList(data) {
  return request({
    url: '/teamManage/getZyAgentList',
    method: 'post',
    data
  })
}

// 获取代理商团队数据详情
export function getAgentTeamDetail(data) {
  return request({
    url: '/agentSummary/getAppAgentDataList',
    method: 'post',
    data
  })
}

/**
 * 物料商城
 */

// 物料礼包列表查询
export function getGiftBagList(data) {
  return request({
    url: '/materiel/getGiftBagList',
    method: 'post',
    data
  })
}

// 收件物流列表查询
export function getExpressInfoList(data) {
  return request({
    url: '/express/getExpressInfoList',
    method: 'post',
    data
  })
}

// 添加物流信息
export function addExpress(data) {
  return request({
    url: '/express/add',
    method: 'post',
    data
  })
}

// 编辑物流信息
export function editExpress(data) {
  return request({
    url: '/express/edit',
    method: 'post',
    data
  })
}

// 删除物流信息
export function deleteExpress(id) {
  return request({
    url: `/express/deleteById?id=${id}`,
    method: 'post',
    data: { id: String(id) }
  })
}

// 上传凭证，订单提交
export function orderSubmit(data) {
  return request({
    url: '/materiel/orderSubmit',
    method: 'post',
    data
  })
}

// 查询订单列表
export function listSimpleOrder(data) {
  return request({
    url: '/materiel/listSimpleOrder',
    method: 'post',
    data
  })
}

// 查询订单详情
export function queryDetailOrder(id) {
  return request({
    url: `/materiel/queryDetailOrder?id=${id}`,
    method: 'post',
    data: { id: String(id) }
  })
}

// 一代确认订单
export function reviewOrder(id) {
  return request({
    url: `/materiel/reviewOrder?id=${id}`,
    method: 'post',
    data: { id: String(id) }
  })
}

// 获取省
export function getProvince(data) {
  return request({
    url: '/area/provinceListAll',
    method: 'post',
    data
  })
}

// 获取市
export function getCity(code) {
  return request({
    url: `/area/cityListAll?parentCode=${code}`,
    method: 'post',
    data: { parentCode: String(code) }
  })
}

// 获取县、区
export function getCounty(code) {
  return request({
    url: `/area/countyListAll?parentCode=${code}`,
    method: 'post',
    data: { parentCode: String(code) }
  })
}

// 获取下拨终端列表
export function getTerList(data) {
  return request({
    url: '/direct/terminal/selDownTerList',
    method: 'post',
    data
  })
}

// 获取下拨代理商列表
export function getSubAgents(agentName) {
  return request({
    url: `/direct/terminal/getSubAgents`,
    method: 'get',
    data: { agentName }
  })
}

// 选择下拨
export function selectStir(data) {
  return request({
    url: '/direct/terminal/dialDownBySel',
    method: 'post',
    data
  })
}

// 区间下拨
export function rangeStir(data) {
  return request({
    url: '/direct/terminal/dialDown',
    method: 'post',
    data
  })
}
