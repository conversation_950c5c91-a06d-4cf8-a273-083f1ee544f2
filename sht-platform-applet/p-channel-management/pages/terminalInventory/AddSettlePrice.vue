<template>
    <div id="addSettlePrice">
        <main>
            <u-form :model="cost" ref="uForm" class="u-form">
                <p class="interval" />
                <u-form-item label="结算价模板名称:" prop="modelName" label-width="auto">
                    <u-input v-model="cost.modelName" :clearable="false" placeholder="请输入结算价模板名称" />
                </u-form-item>
                <p class="interval" />
                <section>
                    <u-form-item label="借记卡(%)" prop="debitCost">
                        <u-input type="digit" v-model="cost.debitCost" :clearable="false" placeholder-style="font-size:24rpx" />
                    </u-form-item>
                    <u-form-item label="借记卡封顶值(元)" prop="debitCapValue">
                        <u-input type="digit" v-model="cost.debitCapValue" :clearable="false" placeholder-style="font-size:24rpx" />
                    </u-form-item>
                    <p class="interval" />
                    <u-form-item label="信用卡(%)" prop="creditCost">
                        <u-input type="digit" v-model="cost.creditCost" :clearable="false" placeholder-style="font-size:24rpx" />
                    </u-form-item>
                    <u-form-item v-if="costSwitch.creditDiscountOpenConf" label="信用卡特惠(%)" prop="creditDiscountCost">
                        <u-input type="digit" v-model="cost.creditDiscountCost" :clearable="false" placeholder-style="font-size:24rpx" />
                    </u-form-item>
                    <u-form-item v-if="costSwitch.secTransOpenConf" label="信用卡秒到(%)" prop="creditSecTransCost">
                        <u-input type="digit" v-model="cost.creditSecTransCost" :clearable="false" placeholder-style="font-size:24rpx" />
                    </u-form-item>
                    <p class="interval" />
                    <u-form-item label="扫码(%)" prop="scanCost">
                        <u-input type="digit" v-model="cost.scanCost" :clearable="false" placeholder-style="font-size:24rpx" />
                    </u-form-item>
                    <u-form-item label="闪付(%)" prop="passCost">
                        <u-input type="digit" v-model="cost.passCost" :clearable="false" placeholder-style="font-size:24rpx" />
                    </u-form-item>
                </section>

            </u-form>
        </main>

        <footer class="submit-button">
            <u-button type="primary" @click="submit">提 交</u-button>
        </footer>
    </div>
</template>

<script>
import { addSettmentPrices } from "../../../http/api";

const pattern = /^0\.\d{0,3}$/;
const costs = ['creditCost', 'debitCost', 'scanCost', 'passCost', 'creditDiscountCost', 'creditSecTransCost']
const rules = {
    debitCapValue: [{ required: true, message: '必填' }],
    modelName: [{ required: true, message: '必填' }]
}
costs.forEach(c => rules[c] = [{ pattern, message: '大于0、小于1且不得超过3位小数' }])

export default {
    name: 'AddSettlePrice',
    data() {
        return {
            cost: {
                modelName: '',
                creditCost: "0.000",
                debitCost: "0.000",
                debitCapValue: "0",
                scanCost: "0.000",
                passCost: "0.000",
                creditDiscountCost: '0.000',
                creditSecTransCost: '0.000',
            },
            costSwitch: {
                secTransOpenConf: false,//秒到交易费率成本展示开关 true 打开 | false 关闭
                creditDiscountOpenConf: false, //信用卡特惠费率成本展示开关 true 打开 | false 关闭
            }
        }
    },
    onLoad() {
        this.costSwitch = Object.assign(this.costSwitch, JSON.parse(this.$Route.query.switchs))
    },
    onReady() {
        this.$refs.uForm.setRules(rules);
    },
    methods: {
        submit() {
            this.$refs.uForm.validate(valid => {
                if (valid) {
                    var cost = {
                        modelName: this.cost.modelName,
                        creditCost: this.cost.creditCost,
                        debitCost: this.cost.debitCost,
                        debitCapValue: this.cost.debitCapValue,
                        scanCost: this.cost.scanCost,
                        passCost: this.cost.passCost,
                        creditDiscountCost: this.cost.creditDiscountCost,
                        creditDiscountCost: this.cost.creditDiscountCost,
                    };
                    addSettmentPrices(cost).then((res) => {
                        if (res.code == "00") {
                            uni.showToast({
                                title: '模板新增成功',
                                icon: 'none'
                            });
                            setTimeout(() => {
                                this.$Router.back(1);
                            }, 1500)
                        }
                    });
                }
            })

        }
    }
}
</script>

<style lang="less" scoped>
#addSettlePrice {
    main {
        background-color: #fff;
        /deep/ .u-form {
            .u-form-item {
                padding: 20rpx 30rpx;
            }
            section {
                .u-form-item--left {
                    flex: 1 !important;
                }
                .u-form-item--right {
                    flex: none;
                    width: 140rpx;
                    border-radius: 6rpx;
                    background: #eaeef1;
                    input {
                        text-align: center;
                        color: #4e77d9;
                    }
                }
                .u-form-item__message {
                    text-align: right;
                }
            }
        }
    }
    .submit-button {
        padding: 50rpx 60rpx 0;
    }
}
</style>