<template>
    <div id="retrace">
        <option-tab opts="区间回撤,选择回撤" :current="currentTab" @select="select" />

        <main>
            <u-field label='支付通道' input-align="right" placeholder="请先选择支付通道" :value="payOrgCode" @click="showPayOrgCodes" :right-icon="`arrow-${payOrgCodesPicker ? 'up' : 'down'}`" disabled />
            <u-select v-model="payOrgCodesPicker" :list="payOrgCodes" label-name="name" value-name="code" @confirm="payOrgCodeConfirm" :default-value="payOrgCode ? [payOrgCodes.findIndex(i=>i.name === payOrgCode)] : []" />
            <no-content v-if="show" />

            <div v-show="!show">
                <!-- 区间回撤 -->
                <view class="terminal-form" v-show="payOrgCode && terminal.retraceType == 0">
                    <u-form :model="terminal" ref="uForm" :label-width="200">
                        <u-form-item label="开始终端号" prop="startTerminalNo">
                            <u-input v-model="terminal.startTerminalNo" placeholder="请输入开始终端号" />
                        </u-form-item>
                        <u-form-item label="结束终端号" prop="endTerminalNo">
                            <u-input v-model="terminal.endTerminalNo" placeholder="请输入结束终端号" />
                        </u-form-item>
                        <u-form-item label="终端数量" prop="terminalCount">
                            <u-input type="number" v-model="terminal.terminalCount" placeholder="请输入终端数量" />
                        </u-form-item>
                        <footer class="custom-button">
                            <button @click="submitRange">确 认</button>
                        </footer>
                    </u-form>
                </view>

                <!-- 选择回撤 -->
                <section class="select-group" v-show="payOrgCode && terminal.retraceType == 2">
                    <checkbox-group @change="checkboxGroupChange">
                        <u-cell-group :border="false">
                            <label v-for="(t, index) in teminalNos" :key="index">
                                <u-cell-item :border-bottom="false" :title="t" :arrow="false">
                                    <checkbox style="transform:scale(0.8)" slot="right-icon" :value="t" :checked="teminalNosSelect.length && teminalNosSelect.indexOf(t) !== -1" />
                                </u-cell-item>
                            </label>
                        </u-cell-group>
                    </checkbox-group>
                    <footer class="custom-button">
                        <button @click="submitSelect">确 认</button>
                    </footer>
                </section>
            </div>
        </main>
    </div>
</template>

<script>
import OptionTab from "../../../components/OptionTab.vue";
import { getRetraceList, retrace, getChannel } from "../../../http/api";

const rules = {
    startTerminalNo: [{ required: true, message: '必填', trigger: 'blur', }],
    endTerminalNo: [{ required: true, message: '必填', trigger: 'blur', }],
    terminalCount: [{ required: true, message: '必填', trigger: 'blur', }]
}
export default {
    name: "Retrace",
    components: { OptionTab },
    data() {
        return {
            payOrgCodesPicker: false,
            payOrgCodes: [],
            payOrgCode: "",
            show: false,
            teminalNos: [],
            teminalNosSelect: [],
            terminal: {
                payOrgCode: '',
                agentCodeOrigin: '',
                retraceType: 0,
                terDoc: "",
                startTerminalNo: "",
                endTerminalNo: "",
                terminalCount: "",
            },
            currentTab: 0,
        };
    },
    onLoad() {
        this.terminal.agentCodeOrigin = this.$Route.query.agentCode
    },
    onReady() {
        this.$refs.uForm.setRules(rules);
    },
    methods: {
        showPayOrgCodes() {
            this.payOrgCodes = [];
            getChannel().then((res) => {
                if (res.code == "00") {
                    if (res.data.length) {
                        this.payOrgCodes = res.data
                        this.payOrgCodesPicker = true;
                    } else {
                        this.$u.toast("暂无可选支付通道！");
                    }
                }
            });
        },
        payOrgCodeConfirm([{ label, value }]) {
            this.payOrgCode = label
            this.terminal.payOrgCode = value
            this.payOrgCodesPicker = false

            getRetraceList(this.$Route.query.agentCode, value).then((res) => {
                if (res.code == "00") {
                    if (res.data.length != 0) {
                        this.show = false
                        this.teminalNos = res.data;
                    } else {
                        this.show = true;
                    }
                }
            });
        },
        checkboxGroupChange(e) {
            this.teminalNosSelect = e.detail.value
        },
        select(data) {
            if (this.currentTab == data) return;
            this.currentTab = data;
            this.terminal.retraceType = this.currentTab == 0 ? 0 : 2;
        },
        submitRange() {
            this.$refs.uForm.validate(valid => {
                if (valid) {
                    retrace(this.terminal).then((res) => {
                        if (res.code == "00") {
                            uni.showToast({ title: res.message, icon: 'none' })

                            setTimeout(() => {
                                this.$Router.back(1);
                            }, 1000);
                        }
                    });
                }
            })

        },
        submitSelect() {
            if (this.teminalNosSelect.length != 0) {
                this.terminal.terDoc = "";
                this.teminalNosSelect.forEach((i) => {
                    if (this.terminal.terDoc == "") {
                        this.terminal.terDoc = i;
                    } else {
                        this.terminal.terDoc = this.terminal.terDoc + "," + i;
                    }
                });
                retrace(this.terminal).then((res) => {
                    if (res.code == "00") {
                        uni.showToast({ title: res.message, icon: 'none' })

                        setTimeout(() => {
                            this.$Router.back(1);
                        }, 1000);
                    }
                });
            } else {
                uni.showToast({ title: '请选择需要回撤的终端！', icon: 'none' })
            }
        },
    },
};
</script>

<style lang="less" scoped>
#retrace {
    height: 100%;
    background-color: #fff;
    > main {
        > div {
            padding: 20rpx 30rpx;
            .terminal-form {
                padding: 0 30rpx;
                background: #f3f5f7;
                border-radius: 10rpx;
            }
            /deep/ .select-group {
                overflow-y: scroll;
                height: calc(100vh - 260rpx - 88rpx);
                background: #f3f5f7;
                border-radius: 20rpx;
                .u-cell-item-box {
                    background-color: transparent;
                }
                checkbox .wx-checkbox-input.wx-checkbox-input-checked {
                    color: #004ea9;
                }
            }
            .custom-button {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
            }
        }
    }
}
</style>