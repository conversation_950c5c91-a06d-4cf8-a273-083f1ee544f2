<template>
    <div id="set">
        <main>
            <u-cell-group>
                <u-cell-item title="修改登录密码" @click="$Router.push({name:'ChangeLoginPsw'})" />
                <u-cell-item title="设置/修改支付密码" @click="getPayPsw" />
                <u-cell-item title="找回支付密码" @click="$Router.push({name:'SetResetPayPsw',params:{type:1}})" />
                <u-cell-item :title="`${userInfo.isMobileVerify ? '更换' : '' }绑定手机号`" @click="$Router.push({name:'BindPhone'})" />
            </u-cell-group>
            <view class="login-out">
                <u-button @click="loginOut" type="primary">退出登录</u-button>
            </view>
        </main>
    </div>
</template>

<script>
import { getPayPsw, exit } from "../../http/api"
import { mapState } from 'vuex';

export default {
    name: "Set",
    computed: {
        ...mapState(['userInfo'])
    },
    methods: {
        getPayPsw() {
            getPayPsw().then(res => {
                if (res.code == "00") {
                    if (res.data) {
                        this.$Router.push({ name: 'ChangePayPsw' });
                    } else {
                        this.$Router.push({ name: 'SetResetPayPsw', params: { type: 0 } });
                    }
                }
            })
        },
        loginOut() {
            uni.showModal({
                content: '确定要退出吗?',
                success: (type) => {
                    if (type.confirm) {
                        exit().then((res) => {
                            if (res.code == "00") {
                                this.$store.dispatch('login_out')
                            }
                        })
                    }
                }
            });

        }
    }
}
</script>

<style lang="less" scoped>
#set {
    padding-top: 20rpx;
    > main {
        > .login-out {
            margin: 450rpx 90rpx 0;
        }
    }
}
</style>