<template>
  <view class="index-container">
    <view class="main">
      <u-form
        :model="form"
        ref="uForm"
        :rules="rules"
        :error-type="['message']"
        label-width="180"
        class="u-form"
      >
        <view class="block-title">基本信息</view>

        <u-form-item label="姓名" prop="legalName" border-bottom>
          <u-input
            v-model="form.legalName"
            placeholder=""
            :disabled="true"
            :border="false"
          />
        </u-form-item>

        <u-form-item label="身份证号" prop="legalCertNo" border-bottom>
          <u-input
            v-model="form.legalCertNo"
            placeholder=""
            :disabled="true"
            :border="false"
          />
        </u-form-item>

        <view class="block-title">结算信息</view>

        <u-form-item label="银行账号" prop="bankAccountNo" border-bottom>
          <u-input
            v-model="form.bankAccountNo"
            placeholder="请填写银行账号"
            :border="false"
          />
        </u-form-item>

        <u-form-item
          label="开户行所在地"
          prop="bankAreaFieldValue"
          border-bottom
        >
          <u-input
            :value="bankAreaFieldValue"
            placeholder="请选择省/市"
            :border="false"
            type="select"
            :select-open="showBankAreaPicker"
            @click="showBankAreaPicker = true"
          />
        </u-form-item>

        <u-form-item label="开户行总行" prop="typeName" border-bottom>
          <u-input
            :value="form.typeName"
            placeholder="请选择开户行总行"
            :border="false"
            type="select"
            :select-open="showBankPicker"
            @click="showBankPicker = true"
          />
        </u-form-item>

        <u-form-item label="开户支行" prop="bankSubName" border-bottom>
          <u-input
            :value="form.bankSubName"
            placeholder="请选择开户支行"
            :border="false"
            type="select"
            :select-open="showBankSubPicker"
            @click="openBankSubPicker"
          />
        </u-form-item>

        <u-form-item label="预留手机号" prop="mobile" border-bottom>
          <u-input
            v-model="form.mobile"
            placeholder="请填写银行预留手机号"
            :border="false"
          />
        </u-form-item>

        <!-- 提交按钮 -->
        <view class="submit-btn-container">
          <u-button
            type="primary"
            :custom-style="{ background: '#4896FA', border: 'none' }"
            @click="onSubmitForm"
          >
            提 交
          </u-button>
        </view>
      </u-form>

      <!-- 开户行地区选择器 -->
      <u-picker
        v-model="showBankAreaPicker"
        title="请选择省/市"
        mode="multiSelector"
        :default-selector="bankAreaDefaultSelector"
        :range="bankAreaRange"
        range-key="text"
        @columnchange="onBankAreaColumnChange"
        @confirm="onBankAreaConfirm"
      />

      <!-- 银行总行选择器 -->
      <u-picker
        v-model="showBankPicker"
        mode="selector"
        :range="bankList"
        range-key="text"
        @confirm="onConfirmBank"
        @cancel="showBankPicker = false"
      >
        <template #top>
          <view style="padding: 20rpx">
            <u-search
              placeholder="输入银行名称查询"
              v-model="whereBank.typeName"
              @search="findBank"
            ></u-search>
          </view>
        </template>
      </u-picker>

      <!-- 银行支行选择器 -->
      <u-picker
        v-model="showBankSubPicker"
        mode="selector"
        :range="bankSubList"
        range-key="text"
        @confirm="onConfirmBankSub"
        @cancel="showBankSubPicker = false"
      >
        <template #top>
          <view style="padding: 20rpx">
            <u-search
              placeholder="输入银行支行名称查询"
              v-model="whereBankSub.bankName"
              @search="getBankSubList(false)"
            ></u-search>
          </view>
        </template>
      </u-picker>
    </view>
  </view>
</template>

<script>
  import { mapState } from 'vuex';
  import {
    hkpayEditBankCard,
    getHkpayProvince,
    getHkpayCity,
    getHkpayCounty,
    getHkpayBankType,
    getHkpayBankSub
  } from '../../../http/api';

  export default {
    name: 'ChangeBankCardSync',
    data() {
      return {
        // 表单信息
        form: {
          bankAccountName: '', // 银行账户名
          bankAccountNo: '', // 银行账号
          mobile: '', // 预留手机号
          // 以下信息支行接口获取
          bankChannelNo: '', // 联行号
          typeCode: '', // 银行编码
          typeName: '', // 银行名称
          accountType: 'S', // 卡类型 默认对私-S 不可选
          bankSubName: '', // 银行支行名称
          bankProvince: '', // 银行所属省编码
          bankCity: '' // 银行所属市编码
        },

        // 开户行地址级联相关
        bankAreaFieldValue: '',
        showBankAreaPicker: false,
        bankAreaDefaultSelector: [0, 0],
        bankAreaRange: [[], []],

        // 结算相关
        showBankPicker: false,
        showBankSubPicker: false,
        whereBank: {
          typeName: '', // 银行名称 模糊查询
          pageNo: 1, // 第几页
          pageSize: 100 // 每页大小 最大100
        },
        whereBankSub: {
          bankName: '', // 银行支行名称 模糊查询
          typeCode: '', // 银行类别编码 必填
          provinceCode: '', // 省编码 必填
          cityCode: '', // 市编码 必填
          pageNo: 1, // 第几页
          pageSize: 100 // 每页大小 最大100
        },
        bankList: [], // 银行总行列表
        bankSubList: [] // 银行支行列表
      };
    },
    computed: {
      // 表单校验规则
      rules() {
        return {
          bankAccountNo: [
            { required: true, message: '请输入银行账号', trigger: 'blur' }
          ],
          bankAreaFieldValue: [
            {
              validator: (rule, value, callback) => {
                if (!this.form.bankCity) {
                  callback(new Error('请选择开户行所在地'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ],
          typeName: [
            { required: true, message: '请选择开户行总行', trigger: 'blur' }
          ],
          bankSubName: [
            { required: true, message: '请选择开户支行', trigger: 'blur' }
          ],
          mobile: [
            {
              required: true,
              message: '请输入银行预留手机号',
              trigger: 'blur'
            },
            {
              pattern: /^1[3456789]\d{9}$/,
              message: '手机号格式错误',
              trigger: 'blur'
            }
          ]
        };
      },
      ...mapState(['userInfo'])
    },
    onLoad() {
      this.loadProvince();
      this.getBankList();

      this.form.legalName = this.userInfo.accountName;
      this.form.legalCertNo = this.userInfo.idCardNo;
      this.form.bankAccountName = this.userInfo.accountName;
      this.form.bankAccountNo = this.userInfo.bankCardNo || '';
      this.form.mobile = this.userInfo.mobile || '';
    },
    onReady() {
      this.$refs.uForm.setRules(this.rules);
    },
    methods: {
      // 提交表单
      onSubmitForm() {
        this.$refs.uForm
          .validate((valid) => {
            if (valid) {
              hkpayEditBankCard(this.form).then((res) => {
                if (res.code == '00') {
                  uni.showToast({
                    title: '结算信息修改成功！',
                    icon: 'none'
                  });

                  this.$store.state.userInfo.mobile = this.form.mobile;
                  this.$store.state.userInfo.bankCardNo =
                    this.form.bankAccountNo;

                  setTimeout(() => {
                    this.$Router.back(1);
                  }, 1500);
                }
              });
            }
          })
          .catch((errors) => {
            console.log('表单验证失败', errors);
          });
      },

      openBankSubPicker() {
        if (!this.form.typeCode) {
          this.$u.toast('请先选择银行总行');
          return;
        }

        if (!this.form.bankCity) {
          this.$u.toast('请先选择开户行所在地');
          return;
        }

        this.getBankSubList(true);
        this.showBankSubPicker = true;
      },

      onConfirmBank(value) {
        const index = value[0];
        const row = this.bankList[index];
        this.showBankPicker = false;
        if (row) {
          this.form.typeName = row.text;
          this.form.typeCode = row.value;
          this.form.bankSubName = '';
          this.form.bankChannelNo = '';
        }
      },

      findBank() {
        this.whereBank.pageNo = 1;
        this.getBankList();
      },

      getBankList() {
        getHkpayBankType(this.whereBank).then((res) => {
          if (res.code == '00') {
            this.bankList = [];
            const { rows } = res.data || {};
            if (rows) {
              this.bankList = rows.map((item) => {
                return {
                  text: item.typeName,
                  value: item.typeCode
                };
              });
            }
          }
        });
      },

      onConfirmBankSub(value) {
        const index = value[0];
        const row = this.bankSubList[index];
        this.showBankSubPicker = false;
        if (row) {
          this.form.bankSubName = row.text;
          this.form.bankChannelNo = row.value;
        }
      },

      getBankSubList(isInit = false) {
        if (isInit) {
          this.whereBankSub.bankName = '';
        }
        this.whereBankSub.typeCode = this.form.typeCode;
        this.whereBankSub.provinceCode = this.form.bankProvince;
        this.whereBankSub.cityCode = this.form.bankCity;
        this.whereBankSub.pageNo = 1;
        getHkpayBankSub(this.whereBankSub).then((res) => {
          if (res.code == '00') {
            this.bankSubList = [];
            const { rows } = res.data || {};
            if (rows) {
              this.bankSubList = rows.map((item) => {
                return {
                  text: item.bankName,
                  value: item.bankChannelNo
                };
              });
            }
          }
        });
      },

      // 银行地区选择器相关方法
      onBankAreaColumnChange(e) {
        const { column, index } = e;
        if (column === 0) {
          // 省份变化，加载对应的城市
          this.bankAreaRange.splice(1, 1, []);
          this.loadCity(this.bankAreaRange[0][index], true);
          this.bankAreaDefaultSelector = [index, 0];
        }
      },

      onBankAreaConfirm(arrIndex) {
        if (arrIndex && arrIndex.length >= 2) {
          const value = [
            this.bankAreaRange[0][arrIndex[0]],
            this.bankAreaRange[1][arrIndex[1]]
          ];
          const [province, city] = value;
          const newValue = `${province.text}/${city.text}`;
          if (newValue !== this.bankAreaFieldValue) {
            this.form.bankSubName = '';
            this.form.bankChannelNo = '';
          }
          this.bankAreaFieldValue = newValue;
          Object.assign(this.form, {
            bankProvince: province.value,
            bankCity: city.value
          });
        }
        this.showBankAreaPicker = false;
      },

      // 加载省
      async loadProvince() {
        let { data } = await getHkpayProvince({});
        const list = data?.list || [];

        // 构建省级数据
        const provinceData = list.map((province) => ({
          text: province.areaName,
          value: province.areaCode
        }));

        this.bankAreaRange[0] = provinceData;

        // 加载市
        if (provinceData.length > 0) {
          await this.loadCity(provinceData[0], true);
        }
      },

      // 加载市
      async loadCity(province, isBank = false) {
        let { data } = await getHkpayCity({ parentCode: province.value });
        const list = data?.list || [];

        const cityData = list.map((city) => ({
          text: city.areaName,
          value: city.areaCode
        }));

        cityData.forEach((city) => {
          if (isBank) {
            this.bankAreaRange[1].push(city);
          } else {
            this.areaRange[1].push(city);
          }
        });
      },

      // 加载区县
      async loadCounty(city) {
        const { data } = await getHkpayCounty({ parentCode: city.value });
        const list = data?.list || [];

        const districtData = list.map((district) => ({
          text: district.areaName,
          value: district.areaCode
        }));

        districtData.forEach((district) => {
          this.areaRange[2].push(district);
        });
      }
    }
  };
</script>

<style lang="less" scoped>
  .index-container {
    background-color: #f5f5f5;

    .u-form {
      /deep/ .u-form-item {
        padding: 20rpx 28rpx;
        line-height: 0;
        background-color: #fff;

        input {
          height: 48rpx;
          min-height: 48rpx !important;
        }
      }
    }

    .main {
      background-color: #fff;

      .block-title {
        padding: 24rpx 30rpx;
        font-weight: 500;
        background-color: #f3f5f7;
        font-size: 28rpx;
        color: #333;
      }

      .picker-validity {
        width: 100%;
        display: flex;
        align-items: center;

        .validity-line {
          width: 20rpx;
          height: 2rpx;
          background-color: #969799;
          margin: 0 20rpx;
        }
      }

      .submit-btn-container {
        margin: 60rpx 40rpx 80rpx;
      }

      .picker-header {
        padding: 20rpx;
        border-bottom: 1px solid #eee;
        display: flex;
        align-items: center;
        gap: 20rpx;
      }
    }
  }
</style>
