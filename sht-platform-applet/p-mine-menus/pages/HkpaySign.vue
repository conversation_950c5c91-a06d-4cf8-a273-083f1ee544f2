<template>
  <view class="index-container">
    <view class="main">
      <u-form
        :model="form"
        ref="uForm"
        :rules="rules"
        :error-type="['message']"
        label-width="180"
        class="u-form"
      >
        <view class="block-title">基本信息</view>

        <u-form-item label="姓名" prop="legalName" border-bottom>
          <u-input
            v-model="form.legalName"
            placeholder="请填写姓名"
            :disabled="true"
            :border="false"
          />
        </u-form-item>

        <u-form-item label="身份证号" prop="legalCertNo" border-bottom>
          <u-input
            v-model="form.legalCertNo"
            placeholder="请填写身份证号"
            :disabled="true"
            :border="false"
          />
        </u-form-item>

        <u-form-item label="证件有效期" prop="validDate" border-bottom>
          <view class="picker-validity">
            <u-input
              :value="form.legalCertStartDate"
              placeholder="开始日期"
              :border="false"
              disabled
              @click="showStartDatePicker = true"
            />
            <view class="validity-line"></view>
            <u-input
              :value="form.legalCertEndDate"
              placeholder="结束日期"
              :border="false"
              disabled
              @click="showEndDatePicker = true"
            />
          </view>
        </u-form-item>

        <u-form-item label="居住地址" prop="legalCertAddr" border-bottom>
          <u-input
            v-model="form.legalCertAddr"
            type="textarea"
            placeholder="请填写居住地址"
            :border="false"
            height="70rpx"
          />
        </u-form-item>

        <u-form-item label="经营地区" prop="areaFieldValue" border-bottom>
          <u-input
            :value="areaFieldValue"
            placeholder="请选择省/市/区"
            :border="false"
            type="select" :select-open="showAreaPicker"
            @click="showAreaPicker = true"
          />
        </u-form-item>

        <u-form-item label="详细地址" prop="officeAddr" border-bottom>
          <u-input
            v-model="form.officeAddr"
            type="textarea"
            placeholder="请输入经营详细地址"
            :border="false"
            height="70rpx"
          />
        </u-form-item>

        <view class="block-title">结算信息</view>

        <u-form-item label="银行账户名" prop="bankAccountName" border-bottom>
          <u-input
            v-model="form.bankAccountName"
            placeholder="请填写银行账户名"
            :disabled="true"
            :border="false"
          />
        </u-form-item>

        <u-form-item label="银行账号" prop="bankAccountNo" border-bottom>
          <u-input
            v-model="form.bankAccountNo"
            placeholder="请填写银行账号"
            :border="false"
          />
        </u-form-item>

        <u-form-item
          label="开户行所在地"
          prop="bankAreaFieldValue"
          border-bottom
        >
          <u-input
            :value="bankAreaFieldValue"
            placeholder="请选择省/市"
            :border="false"
           type="select" :select-open="showBankAreaPicker"
            @click="showBankAreaPicker = true"
          />
        </u-form-item>

        <u-form-item label="开户行总行" prop="typeName" border-bottom>
          <u-input
            :value="form.typeName"
            placeholder="请选择开户行总行"
            :border="false"
           type="select" :select-open="showBankPicker"
            @click="showBankPicker = true"
          />
        </u-form-item>

        <u-form-item label="开户支行" prop="bankSubName" border-bottom>
          <u-input
            :value="form.bankSubName"
            placeholder="请选择开户支行"
            :border="false"
          type="select" :select-open="showBankSubPicker"
            @click="openBankSubPicker"
          />
        </u-form-item>

        <u-form-item label="预留手机号" prop="mobile" border-bottom>
          <u-input
            v-model="form.mobile"
            placeholder="请填写银行预留手机号"
            :border="false"
          />
        </u-form-item>

        <!-- 提交按钮 -->
        <view class="submit-btn-container">
          <u-button
            type="primary"
            :custom-style="{ background: '#4896FA', border: 'none' }"
            @click="onSubmitForm"
          >
            提 交
          </u-button>
        </view>
      </u-form>

      <!-- 开始日期选择器 -->
      <u-picker
        v-model="showStartDatePicker"
        title="开始日期"
        mode="time"
        :start-year="2005"
        :params="startDateParams"
        @confirm="onStartDateConfirm"
      />

      <!-- 结束日期选择器 -->
      <u-picker
        v-model="showEndDatePicker"
        title="结束日期"
        :show-time-tag="false"
        mode="selector"
        :range="endDatePickerColumns"
        range-key="text"
        @confirm="onEndDateConfirm"
      />

      <!-- 地区选择器 -->
      <u-picker
        v-model="showAreaPicker"
        title="请选择省/市/区"
        mode="multiSelector"
        :default-selector="areaDefaultSelector"
        :range="areaRange"
        range-key="text"
        @columnchange="onAreaColumnChange"
        @confirm="onAreaConfirm"
      />

      <!-- 开户行地区选择器 -->
      <u-picker
        v-model="showBankAreaPicker"
        title="请选择省/市"
        mode="multiSelector"
        :default-selector="bankAreaDefaultSelector"
        :range="bankAreaRange"
        range-key="text"
        @columnchange="onBankAreaColumnChange"
        @confirm="onBankAreaConfirm"
      />

      <!-- 银行总行选择器 -->
      <u-picker
        v-model="showBankPicker"
        mode="selector"
        :range="bankList"
        range-key="text"
        @confirm="onConfirmBank"
        @cancel="showBankPicker = false"
      >
        <template #top>
          <view style="padding: 20rpx">
            <u-search
              placeholder="输入银行名称查询"
              v-model="whereBank.typeName"
              @search="findBank"
            ></u-search>
          </view>
        </template>
      </u-picker>

      <!-- 银行支行选择器 -->
      <u-picker
        v-model="showBankSubPicker"
        mode="selector"
        :range="bankSubList"
        range-key="text"
        @confirm="onConfirmBankSub"
        @cancel="showBankSubPicker = false"
      >
        <template #top>
          <view style="padding: 20rpx">
            <u-search
              placeholder="输入银行支行名称查询"
              v-model="whereBankSub.bankName"
              @search="getBankSubList(false)"
            ></u-search>
          </view>
        </template>
      </u-picker>
    </view>
  </view>
</template>

<script>
  import dayjs from 'dayjs';
  import { mapState } from 'vuex';
  import {
    hkpaySign,
    getHkpayProvince,
    getHkpayCity,
    getHkpayCounty,
    getHkpayBankType,
    getHkpayBankSub
  } from '../../http/api';

  export default {
    name: 'HkpaySign',
    data() {
      return {
        // 表单信息
        form: {
          // 基础信息
          legalName: '', // 法人姓名
          legalCertNo: '', // 法人证件号码
          legalCertAddr: '', // 法人居住地址
          legalCertStartDate: '', // 法人证件有效期
          legalCertEndDate: '', // 法人证件有效期
          provinceCode: '', // 省
          provinceName: '', // 省
          cityCode: '', // 市
          cityName: '', // 市
          districtCode: '', // 区
          districtName: '', // 区
          officeAddr: '', // 详细地址

          // 结算信息
          bankAccountName: '', // 银行账户名
          bankAccountNo: '', // 银行账号
          mobile: '', // 预留手机号
          // 以下信息支行接口获取
          bankChannelNo: '', // 联行号
          typeCode: '', // 银行编码
          typeName: '', // 银行名称
          accountType: 'S', // 卡类型 默认对私-S 不可选
          bankSubName: '', // 银行支行名称
          bankProvince: '', // 银行所属省编码
          bankCity: '' // 银行所属市编码
        },

        // 地址级联相关
        areaFieldValue: '',
        showAreaPicker: false,
        areaDefaultSelector: [0, 0, 0],
        areaRange: [[], [], []],

        // 开户行地址级联相关
        bankAreaFieldValue: '',
        showBankAreaPicker: false,
        bankAreaDefaultSelector: [0, 0],
        bankAreaRange: [[], []],

        // 证件有效期处理
        showStartDatePicker: false,
        showEndDatePicker: false,
        startDateParams: {
          year: true,
          month: true,
          day: true,
          hour: false,
          minute: false,
          second: false,
          timestamp: true
        },
        endDatePickerColumns: [
          { text: '十年', value: 10 },
          { text: '二十年', value: 20 },
          { text: '长期', value: 99 }
        ],

        // 结算相关
        showBankPicker: false,
        showBankSubPicker: false,
        whereBank: {
          typeName: '', // 银行名称 模糊查询
          pageNo: 1, // 第几页
          pageSize: 100 // 每页大小 最大100
        },
        whereBankSub: {
          bankName: '', // 银行支行名称 模糊查询
          typeCode: '', // 银行类别编码 必填
          provinceCode: '', // 省编码 必填
          cityCode: '', // 市编码 必填
          pageNo: 1, // 第几页
          pageSize: 100 // 每页大小 最大100
        },
        bankList: [], // 银行总行列表
        bankSubList: [] // 银行支行列表
      };
    },
    computed: {
      // 表单校验规则
      rules() {
        return {
          legalName: [
            { required: true, message: '请输入姓名', trigger: 'blur' }
          ],
          legalCertNo: [
            { required: true, message: '请输入身份证号', trigger: 'blur' },
            {
              pattern: /(^\d{15}$)|(^\d{17}(x|X|\d)$)/,
              message: '证件号格式错误',
              trigger: 'blur'
            }
          ],
          legalCertAddr: [
            { required: true, message: '请输入居住地址', trigger: 'blur' }
          ],
          validDate: [
            {
              validator: (rule, value, callback) => {
                if (
                  !this.form.legalCertStartDate ||
                  !this.form.legalCertEndDate
                ) {
                  callback(new Error('请选择证件有效期'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ],
          areaFieldValue: [
            {
              validator: (rule, value, callback) => {
                if (!this.form.cityCode) {
                  callback(new Error('请选择经营地区'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ],
          officeAddr: [
            { required: true, message: '请输入经营详细地址', trigger: 'blur' }
          ],
          bankAccountName: [
            { required: true, message: '请输入银行账户名', trigger: 'blur' }
          ],
          bankAccountNo: [
            { required: true, message: '请输入银行账号', trigger: 'blur' }
          ],
          bankAreaFieldValue: [
            {
              validator: (rule, value, callback) => {
                if (!this.form.bankCity) {
                  callback(new Error('请选择开户行所在地'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ],
          typeName: [
            { required: true, message: '请选择开户行总行', trigger: 'blur' }
          ],
          bankSubName: [
            { required: true, message: '请选择开户支行', trigger: 'blur' }
          ],
          mobile: [
            {
              required: true,
              message: '请输入银行预留手机号',
              trigger: 'blur'
            },
            {
              pattern: /^1[3456789]\d{9}$/,
              message: '手机号格式错误',
              trigger: 'blur'
            }
          ]
        };
      },
      ...mapState(['userInfo'])
    },
    onLoad() {
      this.loadProvince();
      this.getBankList();

      this.form.legalName = this.userInfo.accountName;
      this.form.legalCertNo = this.userInfo.idCardNo;
      this.form.bankAccountName = this.userInfo.accountName;
      this.form.bankAccountNo = this.userInfo.bankCardNo || '';
      this.form.mobile = this.userInfo.mobile || '';
    },
    onReady() {
      this.$refs.uForm.setRules(this.rules);
    },
    methods: {
      // 提交表单
      onSubmitForm() {
        this.$refs.uForm
          .validate((valid) => {
            if (valid) {
              hkpaySign(this.form).then((res) => {
                if (res.code == '00') {
                  this.$u.toast(res.message);
                  this.$store.commit('SET_USERINFO', {
                    agentCode: this.$store.state.userInfo.agentCode,
                    realName: this.$store.state.userInfo.realName,
                    accountName: this.$store.state.userInfo.accountName,
                    agentLevel: this.$store.state.userInfo.agentLevel,
                    mobile: this.$store.state.userInfo.mobile,
                    companyName: this.$store.state.userInfo.companyName,
                    appAuthStatus: this.$store.state.userInfo.appAuthStatus,
                    payTaxType: this.$store.state.userInfo.payTaxType,
                    taxationRegisterStatus:
                      this.$store.state.userInfo.taxationRegisterStatus,
                    idCardNo: this.$store.state.userInfo.idCardNo,
                    showCreditDiscountOpenConf:
                      this.$store.state.userInfo.showCreditDiscountOpenConf,
                    showSecTransOpenConf:
                      this.$store.state.userInfo.showSecTransOpenConf,
                    memberLevel: this.$store.state.userInfo.memberLevel,
                    isMobileVerify: this.$store.state.userInfo.isMobileVerify,
                    loginName: this.$store.state.userInfo.loginName,
                    bankCardNo: this.$store.state.userInfo.bankCardNo,
                    bankName: this.$store.state.userInfo.bankName,
                    payChannelCode: this.$store.state.userInfo.payChannelCode,
                    cloudOpenStatus: this.$store.state.userInfo.cloudOpenStatus,
                    wsyDisplaySwitch:
                      this.$store.state.userInfo.wsyDisplaySwitch,
                    hkpaySignStatue: 2,
                    dlgSignStatue: this.$store.state.userInfo.dlgSignStatue,
                    receiveType: this.$store.state.userInfo.receiveType,
                    email: this.$store.state.userInfo.email
                  });
                  setTimeout(() => {
                    this.$Router.back(1);
                  }, 1500);
                }
              });
            }
          })
          .catch((errors) => {
            console.log('表单验证失败', errors);
          });
      },

      openBankSubPicker() {
        if (!this.form.typeCode) {
          this.$u.toast('请先选择银行总行');
          return;
        }

        if (!this.form.bankCity) {
          this.$u.toast('请先选择开户行所在地');
          return;
        }

        this.getBankSubList(true);
        this.showBankSubPicker = true;
      },

      onConfirmBank(value) {
        const index = value[0];
        const row = this.bankList[index];
        this.showBankPicker = false;
        if (row) {
          this.form.typeName = row.text;
          this.form.typeCode = row.value;
          this.form.bankSubName = '';
          this.form.bankChannelNo = '';
        }
      },

      findBank() {
        this.whereBank.pageNo = 1;
        this.getBankList();
      },

      getBankList() {
        getHkpayBankType(this.whereBank).then((res) => {
          if (res.code == '00') {
            this.bankList = [];
            const { rows } = res.data || {};
            if (rows) {
              this.bankList = rows.map((item) => {
                return {
                  text: item.typeName,
                  value: item.typeCode
                };
              });
            }
          }
        });
      },

      onConfirmBankSub(value) {
        const index = value[0];
        const row = this.bankSubList[index];
        this.showBankSubPicker = false;
        if (row) {
          this.form.bankSubName = row.text;
          this.form.bankChannelNo = row.value;
        }
      },

      getBankSubList(isInit = false) {
        if (isInit) {
          this.whereBankSub.bankName = '';
        }
        this.whereBankSub.typeCode = this.form.typeCode;
        this.whereBankSub.provinceCode = this.form.bankProvince;
        this.whereBankSub.cityCode = this.form.bankCity;
        this.whereBankSub.pageNo = 1;
        getHkpayBankSub(this.whereBankSub).then((res) => {
          if (res.code == '00') {
            this.bankSubList = [];
            const { rows } = res.data || {};
            if (rows) {
              this.bankSubList = rows.map((item) => {
                return {
                  text: item.bankName,
                  value: item.bankChannelNo
                };
              });
            }
          }
        });
      },

      // 地区选择器相关方法
      async onAreaColumnChange(e) {
        const { column, index } = e;
        if (column === 0) {
          // 省份变化，加载对应的城市
          this.areaRange.splice(1, 1, []);
          this.areaRange.splice(2, 1, []);
          this.areaDefaultSelector = [index, 0, 0];

          await this.loadCity(this.areaRange[0][index], false);
          await this.loadCounty(this.areaRange[1][0]);
        } else if (column === 1) {
          this.areaRange.splice(2, 1, []);
          // 城市变化，加载对应的区县
          this.loadCounty(this.areaRange[1][index]);
          this.areaDefaultSelector = [this.areaDefaultSelector[0], index, 0];
        }
      },

      onAreaConfirm(arrIndex) {
        if (arrIndex && arrIndex.length >= 3) {
          const value = [
            this.areaRange[0][arrIndex[0]],
            this.areaRange[1][arrIndex[1]],
            this.areaRange[2][arrIndex[2]]
          ];
          const [province, city, district] = value;
          this.areaFieldValue = `${province.text}/${city.text}/${district.text}`;
          Object.assign(this.form, {
            provinceCode: province.value,
            cityCode: city.value,
            districtCode: district.value,
            provinceName: province.text,
            cityName: city.text,
            districtName: district.text
          });
          this.form.officeAddr = '';
        }
        this.showAreaPicker = false;
      },

      // 银行地区选择器相关方法
      onBankAreaColumnChange(e) {
        const { column, index } = e;
        if (column === 0) {
          // 省份变化，加载对应的城市
          this.bankAreaRange.splice(1, 1, []);
          this.loadCity(this.bankAreaRange[0][index], true);
          this.bankAreaDefaultSelector = [index, 0];
        }
      },

      onBankAreaConfirm(arrIndex) {
        if (arrIndex && arrIndex.length >= 2) {
          const value = [
            this.bankAreaRange[0][arrIndex[0]],
            this.bankAreaRange[1][arrIndex[1]]
          ];
          const [province, city] = value;
          const newValue = `${province.text}/${city.text}`;
          if (newValue !== this.bankAreaFieldValue) {
            this.form.bankSubName = '';
            this.form.bankChannelNo = '';
          }
          this.bankAreaFieldValue = newValue;
          Object.assign(this.form, {
            bankProvince: province.value,
            bankCity: city.value
          });
        }
        this.showBankAreaPicker = false;
      },

      // 加载省
      async loadProvince() {
        let { data } = await getHkpayProvince({});
        const list = data?.list || [];

        // 构建省级数据
        const provinceData = list.map((province) => ({
          text: province.areaName,
          value: province.areaCode
        }));

        this.areaRange[0] = provinceData;
        this.bankAreaRange[0] = provinceData;

        // 加载市
        if (provinceData.length > 0) {
          await this.loadCity(provinceData[0], false);
          await this.loadCity(provinceData[0], true);
        }
        // 加载区县
        if (this.areaRange[1].length > 0) {
          await this.loadCounty(this.areaRange[1][0]);
        }
      },

      // 加载市
      async loadCity(province, isBank = false) {
        let { data } = await getHkpayCity({ parentCode: province.value });
        const list = data?.list || [];

        const cityData = list.map((city) => ({
          text: city.areaName,
          value: city.areaCode
        }));

        cityData.forEach((city) => {
          if (isBank) {
            this.bankAreaRange[1].push(city);
          } else {
            this.areaRange[1].push(city);
          }
        });
      },

      // 加载区县
      async loadCounty(city) {
        const { data } = await getHkpayCounty({ parentCode: city.value });
        const list = data?.list || [];

        const districtData = list.map((district) => ({
          text: district.areaName,
          value: district.areaCode
        }));

        districtData.forEach((district) => {
          this.areaRange[2].push(district);
        });
      },

      // 证件有效期处理
      onStartDateConfirm(value) {
        this.form.legalCertStartDate = dayjs(value.timestamp * 1000).format(
          'YYYY-MM-DD'
        );
        this.form.legalCertEndDate = '';
        this.showStartDatePicker = false;
      },

      onEndDateConfirm(index) {
        const row = this.endDatePickerColumns[index];
        if (row) {
          const selectedValue = row.value;
          switch (selectedValue) {
            case 10:
            case 20:
              const dateValue = dayjs(
                this.form.legalCertStartDate || new Date()
              )
                .add(selectedValue, 'years')
                .format('YYYY-MM-DD');
              this.form.legalCertEndDate = dateValue;
              break;
            case 99:
              this.form.legalCertEndDate = '2999-12-31';
              break;
          }
        }
        this.showEndDatePicker = false;
      }
    }
  };

  /**
   * 深拷贝
   */
  function deepCopy(obj, cache = new WeakMap()) {
    if (!obj || typeof obj !== 'object') return obj;
    if (cache.has(obj)) return cache.get(obj);
    if (obj instanceof Date) return new Date(obj);
    if (obj instanceof RegExp) return new RegExp(obj.source, obj.flags);

    const copy = Array.isArray(obj)
      ? []
      : Object.create(Object.getPrototypeOf(obj));
    cache.set(obj, copy);
    for (const [key, value] of Object.entries(obj)) {
      copy[key] = deepCopy(value, cache);
    }
    return copy;
  }
</script>

<style lang="less" scoped>
  .index-container {
    background-color: #f5f5f5;

    .u-form {
      /deep/ .u-form-item {
        padding: 20rpx 28rpx;
        line-height: 0;
        background-color: #fff;

        input {
          height: 48rpx;
          min-height: 48rpx !important;
        }
      }
    }

    .main {
      background-color: #fff;

      .block-title {
        padding: 24rpx 30rpx;
        font-weight: 500;
        background-color: #f3f5f7;
        font-size: 28rpx;
        color: #333;
      }

      .picker-validity {
        width: 100%;
        display: flex;
        align-items: center;

        .validity-line {
          width: 20rpx;
          height: 2rpx;
          background-color: #969799;
          margin: 0 20rpx;
        }
      }

      .submit-btn-container {
        margin: 60rpx 40rpx 80rpx;
      }

      .picker-header {
        padding: 20rpx;
        border-bottom: 1px solid #eee;
        display: flex;
        align-items: center;
        gap: 20rpx;
      }
    }
  }
</style>
