<template>
    <div id="selfBaseInfo">
        <main>
            <u-cell-group title="基本信息" :border="false">
                <u-cell-item title="代理商名称" :value="userInfo.realName" :arrow='false' />
                <u-cell-item title="代理商编号" :value="userInfo.agentCode" :arrow='false' />
                <u-cell-item title="企业名称" :value="userInfo.companyName" :arrow='false' />
                <u-cell-item title="身份证号" :value="idCardNo" :label="idCardNo ? '点击查看完整证件号' : ''" :arrow='false' @click="showAllIdCardNo = true" />
                <u-cell-item title="登录手机号" :value="userInfo.isMobileVerify ? userInfo.loginName : '点击绑定'" arrow @click="$Router.push({name:'BindPhone'})" />
            </u-cell-group>
            <u-cell-group title="结算卡信息" :border="false">
                <u-cell-item title="姓名" :value="userInfo.accountName" :arrow='false' />
                <u-cell-item title="银行卡号" :label="bankCardNo ? '点击查看完整卡号' : ''" :value="bankCardNo" :arrow='false' @click="showAllBankCardNo = true" />
                <u-cell-item title="银行预留手机号" :value="userInfo.mobile" :arrow='false' />
                <u-cell-item title="开户行名称" :value="userInfo.bankName" :arrow='false' />
            </u-cell-group>
        </main>
    </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
    data() {
        return {
            showAllIdCardNo: false,
            showAllBankCardNo: false,
        };
    },
    computed: {
        ...mapState(['userInfo']),
        idCardNo() {
            if (this.userInfo.idCardNo) {
                return this.showAllIdCardNo ? this.userInfo.idCardNo : this.userInfo.idCardNo.replace(/(\d{3})\d*(\d{4})/, '$1 **** **** **** $2')
            }
            return ''
        },
        bankCardNo() {
            if (this.userInfo.bankCardNo) {
                return this.showAllBankCardNo ? this.userInfo.bankCardNo : this.userInfo.bankCardNo.replace(/(\d{4})\d*(\d{4})/, '$1 **** **** **** $2')
            }
            return ''
        }
    }
};
</script>
