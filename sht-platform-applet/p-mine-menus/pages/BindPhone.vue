<template>
    <div id="bind-phone">
        <u-cell-group v-if="userInfo.isMobileVerify" :border="false">
            <u-cell-item title="当前绑定登录手机号:" :value="userInfo.loginName" :arrow="false" />
        </u-cell-group>
        <main>
            <u-form :model="user" ref="uForm" label-width="140">
                <u-form-item label="手机号" prop="mobile">
                    <u-input placeholder="请输入手机号码" v-model="user.mobile" type="tel" />
                </u-form-item>
                <u-form-item label="验证码" prop="validCode">
                    <u-input type="digit" placeholder="请输入短信验证码" v-model="user.validCode" />
                    <u-button slot="right" size="mini" type="success" :disabled="time !== ''" @click="getSms">{{ time }}{{ smsMsg }}</u-button>
                </u-form-item>
            </u-form>
            <view style="margin-top: 150rpx">
                <u-button type="primary" @click="submit">提 交</u-button>
            </view>
        </main>
    </div>
</template>

<script>
import { getSmsCode, bindPhone, preValidMobile } from '../../http/api'
import { mapState } from 'vuex'

export default {
    data() {
        return {
            pattern: /^1[3456789]\d{9}$/,
            user: {
                mobile: '', //手机号 (登录账号) ,必填
                validCode: '' //验证码 (绑定手机号验证码),必填
            },
            time: '',
            smsMsg: '获取验证码',
            rules: {
                mobile: [
                    { required: true, message: '请输入手机号码', trigger: ['change', 'blur'] },
                    {
                        validator: (rule, value, callback) => {
                            return this.$u.test.mobile(value)
                        },
                        message: '手机号码格式不正确',
                        trigger: 'blur'
                    }
                ],
                validCode: [{ required: true, message: '请输入验证码', trigger: ['change', 'blur'] }]
            }
        }
    },
    computed: {
        ...mapState(['userInfo'])
    },
    onLoad() {},
    onReady() {
        this.$refs.uForm.setRules(this.rules)
        uni.setNavigationBarTitle({ title: this.$store.state.userInfo.isMobileVerify ? '更换登录手机号' : '绑定登录手机号' })
    },
    methods: {
        submit() {
            this.$refs.uForm.validate(async valid => {
                if (valid) {
                    const { code } = await bindPhone(this.user)
                    if (code == '00') {
                        this.$u.toast('绑定成功！')
                        this.$store.commit('SET_USERINFO', {
                            agentCode: this.$store.state.userInfo.agentCode,
                            realName: this.$store.state.userInfo.realName,
                            accountName: this.$store.state.userInfo.accountName,
                            agentLevel: this.$store.state.userInfo.agentLevel,
                            mobile: this.$store.state.userInfo.mobile,
                            payMarketMode: this.$store.state.userInfo.payMarketMode,
                            companyName: this.$store.state.userInfo.companyName,
                            appAuthStatus: this.$store.state.userInfo.appAuthStatus,
                            payTaxType: this.$store.state.userInfo.payTaxType,
                            taxationRegisterStatus: this.$store.state.userInfo.taxationRegisterStatus,
                            idCardNo: this.$store.state.userInfo.idCardNo,
                            superAgentCode: this.$store.state.userInfo.superAgentCode,
                            showCreditDiscountOpenConf: this.$store.state.userInfo.showCreditDiscountOpenConf,
                            showSecTransOpenConf: this.$store.state.userInfo.showSecTransOpenConf,
                            memberLevel: this.$store.state.userInfo.memberLevel,
                            isMobileVerify: 1,
                            loginName: this.user.mobile,
                            bankCardNo: this.$store.state.userInfo.bankCardNo,
                            bankName: this.$store.state.userInfo.bankName,
                            payChannelCode: this.$store.state.userInfo.payChannelCode,
                            cloudOpenStatus: this.$store.state.userInfo.cloudOpenStatus,
                            wsyDisplaySwitch: this.$store.state.userInfo.wsyDisplaySwitch,
                            dlgSignStatue: this.$store.state.userInfo.dlgSignStatue,
                            hkpaySignStatue: this.$store.state.userInfo.hkpaySignStatue,
                            receiveType: this.$store.state.userInfo.receiveType,
                            email: this.$store.state.userInfo.email
                        })
                        setTimeout(() => {
                            this.$Router.back(1)
                        }, 1000)
                    }
                }
            })
        },
        async getSms() {
            if (this.smsMsg == '获取验证码') {
                if (!/^1[3456789]\d{9}$/.test(this.user.mobile)) {
                    return this.$u.toast('请正确填写手机号码！')
                }

                const flag = await preValidMobile({ mobile: this.user.mobile })
                if (!flag.data.validFlag) {
                    return this.$u.toast('该手机号已存在！')
                }

                const { code } = await getSmsCode({
                    mobile: this.user.mobile,
                    validateType: 6
                })

                if (code == '00') {
                    this.time = 60
                    this.smsMsg = 's后获取'
                    var timer = setInterval(() => {
                        this.time--
                        if (this.time <= 0) {
                            this.time = ''
                            this.smsMsg = '获取验证码'
                            clearInterval(timer)
                        }
                    }, 1000)
                }
            }
        }
    }
}
</script>

<style lang="less" scoped>
#bind-phone {
    height: 100%;
    padding-top: 20rpx;
    > main {
        height: 100%;
        padding: 0 30rpx;
        background: white;
    }
}
</style>
