<template>
  <div id="cloudopen">
    <main>
      <u-form :model="user" ref="uForm" label-width="160">
        <u-form-item label="姓名" prop="accountName">
          <u-input placeholder="请输入姓名" v-model="user.accountName" :disabled="true" />
        </u-form-item>
        <u-form-item label="身份证号" prop="idCard">
          <u-input placeholder="请输入身份证号" v-model="user.idCard" :disabled="true" />
        </u-form-item>
        <u-form-item label="银行卡号" prop="bankNo">
          <u-input type="digit" placeholder="请输入银行卡号" v-model="user.bankNo" />
        </u-form-item>
        <u-form-item label="预留手机号" prop="phone">
          <u-input placeholder="请输入银行预留手机号" v-model="user.phone" type="tel" />
        </u-form-item>
      </u-form>
      <view class="submit-btn">
        <u-button type="primary" @click="submit">提 交</u-button>
      </view>
    </main>
  </div>
</template>

<script>
import { openBankAccount } from '../../http/api';

export default {
  name: 'CloudOpen',
  data() {
    return {
      user: {
        accountName: '', //账户名称
        idCard: '', //身份证号
        bankNo: '', //银行卡号
        phone: '' //银行卡预留手机号
      },
      rules: {
        accountName: [{ required: true, pattern: /^[^ ]+$/, message: '必填且不包含空格' }],
        idCard: [{ required: true, pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '身份证号不符合规则' }],
        bankNo: [{ required: true, message: '必填' }],
        phone: [{ required: true, pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确' }]
      }
    };
  },
  onLoad() {
    const { accountName, idCardNo: idCard, bankCardNo: bankNo, mobile: phone } = this.$store.state.userInfo;
    this.user = Object.assign(this.user, { accountName, idCard, bankNo, phone });
  },
  onReady() {
    this.$refs.uForm.setRules(this.rules);
  },
  methods: {
    submit() {
      this.$refs.uForm.validate(valid => {
        if (valid) {
          openBankAccount(this.user).then(res => {
            if (res.code == '00') {
              this.$store.commit('SET_USERINFO', {
                agentCode: this.$store.state.userInfo.agentCode,
                realName: this.$store.state.userInfo.realName,
                accountName: this.$store.state.userInfo.accountName,
                agentLevel: this.$store.state.userInfo.agentLevel,
                mobile: this.$store.state.userInfo.mobile,
                companyName: this.$store.state.userInfo.companyName,
                appAuthStatus: this.$store.state.userInfo.appAuthStatus,
                payTaxType: this.$store.state.userInfo.payTaxType,
                taxationRegisterStatus: this.$store.state.userInfo.taxationRegisterStatus,
                idCardNo: this.$store.state.userInfo.idCardNo,
                showCreditDiscountOpenConf: this.$store.state.userInfo.showCreditDiscountOpenConf,
                showSecTransOpenConf: this.$store.state.userInfo.showSecTransOpenConf,
                memberLevel: this.$store.state.userInfo.memberLevel,
                isMobileVerify: this.$store.state.userInfo.isMobileVerify,
                loginName: this.$store.state.userInfo.loginName,
                bankCardNo: this.$store.state.userInfo.bankCardNo,
                bankName: this.$store.state.userInfo.bankName,
                payChannelCode: this.$store.state.userInfo.payChannelCode,
                wsyDisplaySwitch: this.$store.state.userInfo.wsyDisplaySwitch,
                dlgSignStatue: this.$store.state.userInfo.dlgSignStatue,
                hkpaySignStatue: this.$store.state.userInfo.hkpaySignStatue,
                receiveType: this.$store.state.userInfo.receiveType,
                cloudOpenStatus: 1,
                email: this.$store.state.userInfo.email
              });
              uni.showToast({
                title: res.message,
                icon: 'none'
              });
              setTimeout(() => {
                this.$Router.back(1);
              }, 1000);
            }
          });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
#cloudopen {
  min-height: 100%;
  background-color: #fff;
  main {
    /deep/ .u-form-item {
      padding: 20rpx 30rpx;
    }
    p {
      padding: 20rpx;
      background-color: #f3f5f7;
      font-weight: bold;
    }
  }
  .submit-btn {
    padding: 100rpx 30rpx 30rpx;
  }
}
</style>
