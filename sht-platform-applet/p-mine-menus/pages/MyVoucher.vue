<template>
    <div id="myVoucher">
        <u-sticky>
            <u-tabs :list="typeTabs" :is-scroll="false" :current="currentType" @change="changeType" />
        </u-sticky>
        <main>
            <no-content v-if="show" />

            <view class="list-data" v-show="!show">
                <section v-for="(v, key) in vouchers" :key="key" class="voucher-content">
                    <div>
                        <p>{{ v.couponName }}</p>
                        <span>使用时间: {{ v.validStartDate }}-{{ v.validEndDate }}</span>
                    </div>
                </section>
            </view>

            <u-loadmore v-if="!show" :status="status" @loadmore="loadmore" />
        </main>
    </div>
</template>

<script>
import { couponRecord } from '../../http/api'
export default {
    name: 'MyVoucher',
    data() {
        return {
            show: false,
            total: 0,
            status: 'loading',
            vouchers: [],
            currentType: 0,
            ajaxParams: {
                pageNo: 1, //当前页
                pageSize: 10, //每页数量
                useStatus: 0 //使用状态
            }
        }
    },
    computed: {
        typeTabs() {
            return [
                { name: `未使用${this.typeTotalCount(0)}`, value: 0 },
                { name: `已使用${this.typeTotalCount(1)}`, value: 1 },
                { name: `已过期${this.typeTotalCount(2)}`, value: 2 }
            ]
        }
    },
    onLoad() {
        this.getVouchersList()
    },
    onReachBottom() {
        this.loadmore()
    },
    methods: {
        changeType(index) {
            this.ajaxParams.useStatus = this.currentType = index
            this.getVouchersList()
        },
        getVouchersList() {
            this.status = 'loading'
            this.ajaxParams.pageNo = 1
            this.total = null
            couponRecord(this.ajaxParams).then(res => {
                if (res.code == '00') {
                    this.total = res.data.total
                    if (res.data.list.length != 0) {
                        this.show = false
                        this.vouchers = res.data.list
                        uni.pageScrollTo({ scrollTop: 0 })

                        if (this.vouchers.length >= this.total) {
                            // 数据全部加载完成
                            this.status = 'nomore'
                        } else {
                            this.status = 'loadmore'
                        }
                    } else {
                        this.show = true
                    }
                }
            })
        },
        loadmore() {
            if (this.status == 'nomore') return
            this.status = 'loading'
            this.ajaxParams.pageNo = this.ajaxParams.pageNo + 1
            couponRecord(this.ajaxParams).then(res => {
                if (res.code == '00') {
                    this.total = res.data.total
                    res.data.list.forEach(i => {
                        this.vouchers.push(i)
                    })
                    if (this.vouchers.length >= this.total) {
                        // 数据全部加载完成
                        this.status = 'nomore'
                    } else {
                        this.status = 'loadmore'
                    }
                }
            })
        },
        typeTotalCount(type) {
            if (this.ajaxParams.useStatus === type && !!this.total) return `(${this.total})`
            return ''
        }
    }
}
</script>
<style lang="scss" scoped>
#myVoucher {
    min-height: 100%;
    background-color: #f3f5f7;
    > main {
        .list-data {
            width: 100%;
            padding: 20rpx;
            .voucher-content {
                display: flex;
                justify-content: center;
                width: 100%;
                margin-bottom: 20rpx;
                > div {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    width: 600rpx;
                    height: 200rpx;
                    position: relative;
                    background: radial-gradient(circle at right top, transparent 20rpx, #fff 0) top left / 120rpx 51% no-repeat, radial-gradient(circle at right bottom, transparent 20rpx, #fff 0) bottom left / 120rpx 51% no-repeat, radial-gradient(circle at left top, transparent 20rpx, #fff 0) top right / 480rpx 51% no-repeat, radial-gradient(circle at left bottom, transparent 20rpx, #fff 0) bottom right / 480rpx 51% no-repeat;
                    padding: 20rpx 20rpx 20rpx 140rpx;
                    border-radius: 12rpx;
                    > p {
                        margin: 0;
                        font-weight: 500;
                    }
                    > span {
                        margin-top: 20rpx;
                        font-size: 24rpx;
                        color: #888;
                    }
                }
                > div::after {
                    content: '';
                    height: 160rpx;
                    border: 2rpx dashed #eee;
                    position: absolute;
                    left: 120rpx;
                    top: 0;
                    bottom: 0;
                    margin: auto;
                }
            }
        }
    }
}
</style>
