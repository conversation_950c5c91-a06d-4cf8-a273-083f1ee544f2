<template>
  <div id="dlgSignStatue">
    <main>
      <section class="s1">
        <p>
          <span>姓名</span>
          <span>{{ userInfo.accountName }}</span>
        </p>
         <p>
          <span>签约手机号</span>
          <span>{{userInfo.mobile }}</span>
        </p>
        <p v-if="userInfo.payChannelCode !== '1006'">
          <span>身份证号码</span>
          <span>{{ userInfo.idCardNo.replace(/(\d{3})\d*(\d{4})/, '$1 **** **** **** $2') }}</span>
        </p>
      </section>
      <p>上传照片</p>
      <section class="file-info">
        <div>
          <p @click="upload(1)">
            <image class="upload" v-show="showFront" src="../../static/images/common/photo.png" alt="" />
            <span v-show="showFront">身份证人像面照片</span>
            <image class="img" v-show="!showFront" :src="'data:image/jpg;base64,' + idFrontImagePath" alt="" />
          </p>
          <p @click="upload(2)">
            <image class="upload" v-show="showReverse" src="../../static/images/common/photo.png" alt="" />
            <span v-show="showReverse">身份证国徽面照片</span>
            <image class="img" v-show="!showReverse" :src="'data:image/jpg;base64,' + idBackImagePath" alt="" />
          </p>
        </div>
      </section>

      <view class="custom-button">
        <button type="primary" @click="submit">确定</button>
      </view>
    </main>
  </div>
</template>

<script>
import { dlgSign } from '../../http/api';

export default {
  name: 'DlgSignStatue',
  data() {
    return {
      showFront: true,
      idFrontImagePath: '',
      showReverse: true,
      idBackImagePath: ''
    };
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo;
    }
  },
  methods: {
    upload(num) {
      uni.chooseImage({
        count: 1,
        success: async res => {
          const base64Data = await this.pathToBase64(res.tempFilePaths[0]);
          if (base64Data) {
            const base64DataSplice = base64Data.split(',')[1];
            if (num == 1) {
              this.idFrontImagePath = base64DataSplice;
              this.showFront = false;
            } else {
              this.idBackImagePath = base64DataSplice;
              this.showReverse = false;
            }
          }
        }
      });
    },
    pathToBase64(path) {
      return new Promise((resolve, reject) => {
        if (typeof plus === 'object') {
          plus.io.resolveLocalFileSystemURL(
            this.getLocalFilePath(path),
            function (entry) {
              entry.file(
                function (file) {
                  var fileReader = new plus.io.FileReader();
                  fileReader.onload = function (data) {
                    resolve(data.target.result);
                  };
                  fileReader.onerror = function (error) {
                    reject(error);
                  };
                  fileReader.readAsDataURL(file);
                },
                function (error) {
                  reject(error);
                }
              );
            },
            function (error) {
              reject(error);
            }
          );
          return;
        }
        if (typeof wx === 'object' && wx.canIUse('getFileSystemManager')) {
          wx.getFileSystemManager().readFile({
            filePath: path,
            encoding: 'base64',
            success: function (res) {
              resolve('data:image/png;base64,' + res.data);
            },
            fail: function (error) {
              reject(error);
            }
          });
          return;
        }
        reject(new Error('not support'));
      });
    },
    getLocalFilePath(path) {
      if (
        path.indexOf('_www') === 0 ||
        path.indexOf('_doc') === 0 ||
        path.indexOf('_documents') === 0 ||
        path.indexOf('_downloads') === 0
      ) {
        return path;
      }
      if (path.indexOf('file://') === 0) {
        return path;
      }
      if (path.indexOf('/storage/emulated/0/') === 0) {
        return path;
      }
      if (path.indexOf('/') === 0) {
        var localFilePath = plus.io.convertAbsoluteFileSystem(path);
        if (localFilePath !== path) {
          return localFilePath;
        } else {
          path = path.substr(1);
        }
      }
      return '_www/' + path;
    },
    submit() {
      if (!(this.idFrontImagePath && this.idBackImagePath)) {
        uni.showToast({
          title: '请完整上传身份证',
          icon: 'none'
        });
        return;
      }
      dlgSign({
        frontImage: this.idFrontImagePath,
        backImage: this.idBackImagePath
      }).then(res => {
        if (res.code == '00') {
          uni.showToast({
            title: res.message,
            icon: 'none'
          });
          this.$store.commit('SET_USERINFO', {
            agentCode: this.$store.state.userInfo.agentCode,
            realName: this.$store.state.userInfo.realName,
            accountName: this.$store.state.userInfo.accountName,
            agentLevel: this.$store.state.userInfo.agentLevel,
            mobile: this.$store.state.userInfo.mobile,
            companyName: this.$store.state.userInfo.companyName,
            appAuthStatus: this.$store.state.userInfo.appAuthStatus,
            payTaxType: this.$store.state.userInfo.payTaxType,
            taxationRegisterStatus: this.$store.state.userInfo.taxationRegisterStatus,
            idCardNo: this.$store.state.userInfo.idCardNo,
            showCreditDiscountOpenConf: this.$store.state.userInfo.showCreditDiscountOpenConf,
            showSecTransOpenConf: this.$store.state.userInfo.showSecTransOpenConf,
            memberLevel: this.$store.state.userInfo.memberLevel,
            isMobileVerify: this.$store.state.userInfo.isMobileVerify,
            loginName: this.$store.state.userInfo.loginName,
            bankCardNo: this.$store.state.userInfo.bankCardNo,
            bankName: this.$store.state.userInfo.bankName,
            payChannelCode: this.$store.state.userInfo.payChannelCode,
            cloudOpenStatus: this.$store.state.userInfo.cloudOpenStatus,
            wsyDisplaySwitch: this.$store.state.userInfo.wsyDisplaySwitch,
            hkpaySignStatue: this.$store.state.userInfo.hkpaySignStatue,
            dlgSignStatue: 1,
            receiveType: this.$store.state.userInfo.receiveType,
            email: this.$store.state.userInfo.email
          });
          setTimeout(() => {
            this.$Router.back(1);
          }, 1500);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
#dlgSignStatue {
  height: 100%;
  padding-bottom: 100rpx;
  main {
    background-color: #fff;
    > p {
      background: #f8f8f8;
      margin: 0;
      line-height: 78rpx;
      color: #999999;
      padding-left: 30rpx;
    }
    .s1 {
      > p {
        padding: 30rpx;
        margin: 0;

        > span {
          display: inline-block;

          &:nth-of-type(1) {
            width: 180rpx;
            color: #666666;
          }

          &:nth-of-type(2) {
            color: #222222;
          }
        }

        &:nth-of-type(2) {
          border-top: 2rpx solid #e6e6e6;
        }
        &:nth-of-type(3) {
          border-top: 2rpx solid #e6e6e6;
        }
      }
    }
    .base-info {
      > p {
        line-height: 88rpx;
        margin: 0 0 0 30rpx;
        position: relative;
        &:nth-of-type(1) {
          border-bottom: 2rpx solid #e6e6e6;
        }
        > span {
          &:nth-of-type(1) {
            width: 142rpx;
            color: #666666;
            margin-right: 40rpx;
            display: inline-block;
          }
          &:nth-of-type(2) {
            color: #222222;
          }
        }
      }
    }
    .file-info {
      padding: 30rpx;
      > div {
        width: 690rpx;
        background: #f3f5f7;
        border-radius: 24rpx;
        display: flex;
        > p {
          width: 316rpx;
          height: 200rpx;
          text-align: center;
          border: 4rpx dashed #666666;
          border-radius: 12rpx;
          margin: 30rpx 0;
          position: relative;
          &:nth-of-type(1) {
            margin: 30rpx 18rpx 30rpx 20rpx;
          }
          .upload {
            width: 88rpx;
            height: 88rpx;
            margin: 46rpx 0 6rpx;
          }
          > span {
            font-size: 18rpx;
            color: #222222;
            display: block;
          }
          .img {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
          }
        }
      }
    }
    /deep/ .u-form {
      .u-form-item {
        padding: 20rpx 30rpx;
      }
    }
    .custom-button {
      position: fixed;
      bottom: 0;
    }
  }
}
</style>
