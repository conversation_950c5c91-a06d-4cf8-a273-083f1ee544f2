<template>
    <view id="increment">
        <u-navbar :is-back="false" :background="navbarBg" :border-bottom="false">
            <view class="navbar-content">
                <text class="title">增值业务</text>
                <view class="right" @click="showPlayPresent = true">
                    <text>玩法介绍</text>
                    <u-icon size="28" name="question-circle" />
                </view>
            </view>
        </u-navbar>

        <view class="main">
            <view class="nav-list">
                <view v-for="(b, index) in business" :key="index" class="nav-li" :style="{ 'background-color': b.bgColor }" @click="toLink(b.navName)">
                    <view class="nav-name">{{ b.name }}</view>
                    <!-- <view v-if="index == 2" class="call-us" @click.stop="toLink('CallUs')"> 联系我们 </view> -->
                </view>
            </view>
        </view>

        <!-- 玩法介绍弹窗 -->
        <u-modal v-model="showPlayPresent" :showConfirmButton="false" :show-title="false">
            <div class="present">
                <span class="title">玩法介绍</span>
                <div class="content">
                    <p>1.手游返佣: 进入手游列表，分享推广二维码到微信，玩家识别二维码后注册手游账号，在游戏中充值消费；</p>
                    <p>2.电商返佣：进入在线商城，分享商品二维码到微信，客户识别商品二维码后进入小程序下单成功；</p>
                    <p>以上推广场景，均可让推广员赚取返佣分润，佣金比例详情见对应产品列表。</p>
                </div>
                <view class="close" @click="showPlayPresent = false">
                    <u-icon name="close-circle" size="50" />
                </view>
            </div>
        </u-modal>
    </view>
</template>

<script>
import { getListAgentCode } from '../../http/api'
export default {
    name: 'Increment',
    data() {
        return {
            showPlayPresent: false,
            business: [
                {
                    name: '手游推广',
                    navName: 'GameManagement',
                    bgColor: '#17b1aa'
                },
                {
                    name: '在线商城',
                    navName: 'MiniProgramManagement',
                    bgColor: '#e54d42'
                }
                // {
                //     name: '积分兑换',
                //     navName: 'PointExchange',
                //     bgColor: '#f37b1d'
                // },
                // {
                //     name: '酒店贩卖机招合伙人',
                //     navName: 'OpenFile',
                //     bgColor: '#79a789'
                // },
            ],
            navbarBg: {
                backgroundColor: 'rgb(216 185 82)'
            }
        }
    },
    onLoad() {
        getListAgentCode().then(res => {
            if (res.code == '00') {
                const agents = res.data || []
                if (agents.includes(this.$store.state.userInfo.superAgentCode)) {
                    this.business.push({
                        name: '申请信用卡',
                        navName: 'ApplyCreditCard',
                        bgColor: '#79a789'
                    })
                }
            }
        })
    },
    methods: {
        toLink(name) {
            if (name == 'OpenFile') {
                uni.showLoading({
                    title: '正在加载',
                    mask: true
                })
                uni.downloadFile({
                    url: 'https://opentcapi.shtcloud.com/img/hotelppt/hotel.pptx',
                    filePath: wx.env.USER_DATA_PATH + '/酒店贩卖机招合伙人.pptx',
                    success: function (res) {
                        var filePath = res.filePath
                        uni.openDocument({
                            filePath: filePath,
                            success: function (res) {
                                console.log('打开文档成功')
                            }
                        })
                    },
                    complete: function () {
                        uni.hideLoading()
                    }
                })
            } else {
                this.$Router.push({ name })
            }
        }
    }
}
</script>

<style lang="scss" scoped>
#increment {
    .navbar-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20rpx 0 30rpx;
        .title {
            font-size: 34rpx;
            color: #fff;
        }
        .right {
            display: flex;
            align-items: center;
            height: 100%;
            font-size: 24rpx;
            color: #f5f5f5;
            > text {
                margin-right: 2px;
            }
        }
    }
    .nav-list {
        display: flex;
        flex-wrap: wrap;
        padding: 40upx;
        justify-content: space-between;
    }

    .nav-li {
        padding: 30upx 15upx;
        border-radius: 12upx;
        width: 45%;
        margin: 0 2.5% 40upx;
        background-image: url('../../static/images/common/navbar-bg.png');
        background-size: cover;
        background-position: center;
        position: relative;
        z-index: 1;
        color: #fff;
    }

    .call-us {
        position: absolute;
        right: 0;
        top: 0;
        background: #9a9292;
        padding: 6rpx 20rpx;
        font-size: 24rpx;
        border-radius: 0 12rpx 0 34rpx;
    }

    .nav-li::after {
        content: '';
        position: absolute;
        z-index: -1;
        background-color: inherit;
        width: 100%;
        height: 100%;
        left: 0;
        bottom: -10%;
        border-radius: 10upx;
        opacity: 0.2;
        transform: scale(0.9, 0.9);
    }
    .nav-name {
        font-size: 28upx;
        text-transform: Capitalize;
        margin-top: 20upx;
        position: relative;
    }

    .nav-name::before {
        content: '';
        position: absolute;
        display: block;
        width: 40upx;
        height: 6upx;
        background: #fff;
        bottom: 0;
        right: 0;
        opacity: 0.5;
    }

    .nav-name::after {
        content: '';
        position: absolute;
        display: block;
        width: 100upx;
        height: 1px;
        background: #fff;
        bottom: 0;
        right: 40upx;
        opacity: 0.3;
    }

    .nav-name::first-letter {
        font-weight: bold;
        font-size: 36upx;
        margin-right: 1px;
    }

    .present {
        overflow: hidden;
        position: relative;
        width: 100%;
        height: 640rpx;
        background: url('../../static/images/home/<USER>') no-repeat;
        background-size: 100% 100%;
        .title {
            position: absolute;
            top: 68rpx;
            left: 50%;
            transform: translateX(-50%);
            color: #fff;
            font-size: 30rpx;
        }
        .content {
            padding: 220rpx 36rpx 0;
            font-size: 28rpx;
            color: #333;
            line-height: 1.55em;
            > p {
                text-align: left;
                margin: 0;
                margin-bottom: 12rpx;
            }
        }
        .close {
            position: absolute;
            right: 26rpx;
            top: 26rpx;
        }
    }
}
</style>
