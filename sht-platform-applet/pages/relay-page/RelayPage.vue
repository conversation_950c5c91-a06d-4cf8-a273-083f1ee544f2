<template>
    <view id="relayPage">
        <image src="../../static/images/common/aboutUs.png" mode="widthFix" />
    </view>
</template>

<script>
export default {
    name: "RelayPage",
    onLoad() {
        setTimeout(() => {
            this.$Router.pushTab({ name: 'Home' })
        }, 500);
    },
};
</script>
<style lang="scss" scoped>
#relayPage {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    > image {
        width: 180rpx;
        height: 180rpx;
    }
}
</style>
