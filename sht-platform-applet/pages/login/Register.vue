<template>
    <div id="register">
        <main>
            <div class="register-type">
                <p @click="registerType = 0" :class="{'type--checked':!registerType}">账号注册</p>
                <span>- or -</span>
                <p @click="registerType = 1" :class="{'type--checked':registerType}">手机注册</p>
            </div>

            <u-form :model="user" ref="uForm" label-width="50">
                <u-form-item left-icon="account" prop="referrerAgentCode">
                    <u-input v-model="user.referrerAgentCode" placeholder="请输入推荐人代理商编号" />
                </u-form-item>

                <template v-if="!registerType">
                    <u-form-item left-icon="lock" prop="password">
                        <u-input v-model="user.password" placeholder="请输入密码" />
                    </u-form-item>
                    <u-form-item left-icon="lock" prop="confirmPwd">
                        <u-input v-model="user.confirmPwd" placeholder="请确认密码" />
                    </u-form-item>
                    <u-form-item left-icon="photo" prop="verifyCode">
                        <u-input v-model="user.verifyCode" placeholder="请输入验证码" :clearable="false" />
                        <view slot="right" @click="changeImgCode">
                            <image-captcha class="imgCode" :identifyCode="identifyCode" />
                        </view>
                    </u-form-item>
                </template>

                <template v-else>
                    <u-form-item left-icon="phone" prop="mobile">
                        <u-input v-model="user.mobile" placeholder="请输入手机号码" />
                    </u-form-item>
                    <u-form-item left-icon="coupon" prop="validCode">
                        <u-input type="digit" placeholder="请输入短信验证码" v-model="user.validCode" />
                        <u-button slot="right" size="mini" type="success" :disabled="time !== ''" @click="getSms">{{time}}{{smsMsg}}</u-button>
                    </u-form-item>
                    <u-form-item left-icon="lock" prop="password">
                        <u-input v-model="user.password" placeholder="请输入密码" />
                    </u-form-item>
                    <u-form-item left-icon="lock" prop="confirmPwd">
                        <u-input v-model="user.confirmPwd" placeholder="请确认密码" />
                    </u-form-item>
                </template>
            </u-form>

            <view class="u-m-t-60 u-m-b-30">
                <u-button type="primary" :disabled="!agree" @click="submit">注 册</u-button>
            </view>
            <p class="u-flex u-row-center">
                <u-checkbox v-model="agree" />
                <span>我已阅读并同意</span><span @click="agreement">《注册协议》</span>
            </p>
        </main>
    </div>
</template>

<script>
import ImageCaptcha from "../../components/ImageCaptcha";
import { register, getSmsCode, preValidMobile } from "../../http/api";

export default {
    name: "Register",
    components: {
        ImageCaptcha
    },
    data() {
        return {
            identifyCodes: "1234567890",
            identifyCode: "",
            agree: false,
            registerType: 0,
            user: {
                sourceType: 4,
                referrerAgentCode: null,
                payMarketMode: null,
                mobile: null,
                password: null,
                verifyCode: '',
                validCode: '',
                regType: 0,
                confirmPwd: ''
            },
            time: "",
            smsMsg: "获取验证码",
            rules: {
                referrerAgentCode: [{ required: true, message: '请输入推荐人代理商编号', trigger: 'blur', }],
                validCode: [{ required: true, message: '请输入短信验证码', trigger: 'blur', }],
                password: [{ required: true, min: 6, message: '密码必须6位及以上', trigger: ['change', 'blur'] }],
                confirmPwd: [{ required: true, message: '两次密码不一致!', validator: (rule, value, callback) => { return this.user.password == value }, trigger: ['change', 'blur'] }],
                mobile: [{ required: true, message: '请输入手机号码', trigger: ['change', 'blur'], }, { validator: (rule, value, callback) => { return this.$u.test.mobile(value); }, message: '手机号码格式不正确', trigger: 'blur', }],
                verifyCode: [{ required: true, message: '请输入验证码', trigger: ['change', 'blur'], }, { validator: (rule, value, callback) => { return this.identifyCode == value; }, message: '验证码不正确', trigger: 'blur', }]
            },
        }
    },
    watch: {
        registerType(val) {
            this.user.regType = val
        }
    },
    onLoad() {
        this.changeImgCode();
    },
    onReady() {
        this.$refs.uForm.setRules(this.rules);
    },
    methods: {
        async getSms() {
            if (this.smsMsg == "获取验证码") {
                if (!/^1[3456789]\d{9}$/.test(this.user.mobile)) {
                    return this.$u.toast('请正确填写手机号码！');
                }

                const flag = await preValidMobile({ mobile: this.user.mobile })
                if (!flag.data.validFlag) {
                    return this.$u.toast("该手机号已存在！");
                }

                const { code } = await getSmsCode({
                    mobile: this.user.mobile,
                    validateType: 5
                })

                if (code == '00') {
                    this.time = 60;
                    this.smsMsg = "s后获取";
                    var timer = setInterval(() => {
                        this.time--;
                        if (this.time <= 0) {
                            this.time = "";
                            this.smsMsg = "获取验证码";
                            clearInterval(timer);
                        }
                    }, 1000);
                }
            }
        },
        randomNum(min, max) {
            return Math.floor(Math.random() * (max - min) + min);
        },
        makeCode(o, l) {
            for (let i = 0; i < l; i++) {
                this.identifyCode += this.identifyCodes[
                    this.randomNum(0, this.identifyCodes.length)
                ];
            }
        },
        changeImgCode() {
            this.identifyCode = "";
            this.makeCode(this.identifyCodes, 4);
        },
        agreement() {
            this.$Router.push({ name: 'Agreement' });
        },
        submit() {
            this.$refs.uForm.validate(valid => {
                if (valid) {
                    register(this.user).then((res) => {
                        if (res.code == "00") {
                            const content = this.registerType ? `您的登录手机号为: ${this.user.mobile}` : `您的代理商编号为: ${res.data}`
                            uni.showModal({
                                title: '注册成功',
                                content: content + ',请前往登录！',
                                confirmText: '复制',
                                cancelText: '关闭',
                                success: (val) => {
                                    if (val.confirm) {
                                        uni.setClipboardData({
                                            data: this.registerType ? this.user.mobile : res.data
                                        });
                                    }
                                    // 重置输入框
                                    Object.assign(this.$data, this.$options.data.call(this))
                                }
                            });
                        }
                        this.changeImgCode()
                    })
                }
            })

        }
    }
}
</script>

<style lang="less" scoped>
#register {
    min-height: 100%;
    background-color: #fff;
    > main {
        padding: 0 30rpx;
        > p {
            color: #666666;
            > span {
                font-size: 24rpx;
                margin-left: 10rpx;
                &:nth-of-type(2) {
                    color: #4a67d6;
                }
            }
            /deep/ .u-checkbox__label {
                display: none;
            }
            /deep/ .u-checkbox {
                display: block;
            }
        }

        /deep/ .u-form-item {
        }
        .register-type {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 30rpx 0;
            color: #8799a3;
            font-size: 24rpx;
            > p {
                position: relative;
                margin: 0;
                &::after {
                    content: "";
                    position: absolute;
                    top: -10rpx;
                    right: -10rpx;
                    bottom: -10rpx;
                    left: -10rpx;
                }
            }
            > span {
                margin: 0 30rpx;
                font-size: 28rpx;
            }
        }
        .type--checked {
            font-size: 28rpx;
            color: #333;
            font-weight: bolder;
            animation: 1s flipInX;
        }
    }

    @keyframes flipInX {
        from {
            -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
            transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
            -webkit-animation-timing-function: ease-in;
            animation-timing-function: ease-in;
            opacity: 0;
        }

        40% {
            -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
            transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
            -webkit-animation-timing-function: ease-in;
            animation-timing-function: ease-in;
        }

        60% {
            -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
            transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
            opacity: 1;
        }

        80% {
            -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
            transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
        }

        to {
            -webkit-transform: perspective(400px);
            transform: perspective(400px);
        }
    }
}
</style>