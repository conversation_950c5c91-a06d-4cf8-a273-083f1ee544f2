<template>
    <div id="agreement">
        <article class="u-p-24">
            <h3>{{appInfo.appName}}用户服务协议</h3>
            <p>注册前请您先仔细阅读并同意以下协议：</p>
            <b>【特别提示】</b><br>
            <b>&nbsp;&nbsp;&nbsp;&nbsp;再次特别提醒您（用户）注册成为{{appInfo.appName}}APP用户之前，请认真阅读《{{appInfo.appName}}APP用户服务协议》（简称：用户注册协议），为确保您充分理解本协议中的条款，请您仔细阅读并选择接受或不接受本协议，您点击确认本协议条款且完成注册程序后才能成为{{appInfo.appName}}APP的正式用户，并享受{{appInfo.appName}}APP的各类服务，您的注册、登录使用等行为将视为对本协议的接受，并同意本协议各项条款的约束。若您不同意本协议，或对本协议中的条款存在任何疑义，请您立即停止{{appInfo.appName}}APP用户注册程序，并可以选择不使用本程序服务。</b><br>
            <b>&nbsp;&nbsp;&nbsp;&nbsp;本协议约定{{appInfo.appName}}APP与用户之间的权利和义务，"用户"是指注册、登录、使用本服务的个人、单位。本协议可由新功能随时更新，更新后的协议条款一旦公布立即代替原来的协议条款，恕不在另行通知，用户可在app中查阅最新条款，若不接受修改后的条款，请立即停止使用{{appInfo.appName}}APP提供的服务，用户继续使用将被视为接受修改后的协议。</b><br>
            <br>
            <b>【协议条款】</b><br>
            <b>一、账号注册</b><br>
            <p>&nbsp;&nbsp;1、用户在使用本服务前需要注册一个手机号账号，账号应当使用手机号码绑定注册，请用户使用尚未与“{{appInfo.appName}}APP”账号绑定的手机号码。</p>
            <p>&nbsp;&nbsp;2、如果注册申请者有被其他APP封禁的先例或涉嫌虚假注册及滥用他人名义注册，及其他不能得到许可的理由，{{appInfo.appName}}APP将拒绝其注册申请。</p>
            <p>&nbsp;&nbsp;3、鉴于“{{appInfo.appName}}APP”账号的绑定方式，您需同意在“{{appInfo.appName}}APP”在注册时，将允许您的手机号码及手机设备识别码等信息用于注册。</p>
            <p>&nbsp;&nbsp;4、用户在注册及使用本服务时，{{appInfo.appName}}APP需要手机能识别用户个人信息以便{{appInfo.appName}}APP可以在必要时联系用户，或为用户提供更好的体验。{{appInfo.appName}}APP识别的信息包括但不限于用户的姓名、地址。{{appInfo.appName}}APP同意对这些信息的使用将受限于第三方用户个人隐私信息保护的约束。</p>
            <br>
            <b>二、账户安全</b><br>
            <p>&nbsp;&nbsp;1、用户一旦注册成功，成为{{appInfo.appName}}APP的用户，将得到一个用户名和密码，并有权利使用自己的用户名及密码随时登录。</p>
            <p>&nbsp;&nbsp;2、用户对用户名和密码的安全负全部责任，同时对以其用户名进行的所有的活动和时间负责。</p>
            <p>&nbsp;&nbsp;3、用户不得以任何形式擅自转让或授权他人使用自己的{{appInfo.appName}}APP用户名。</p>
            <p>&nbsp;&nbsp;4、如果用户泄露了密码，有可能导致不利的法律后果，因此不管任何原因导致用户的密码安全受到威胁，应该立即和{{appInfo.appName}}APP工作人员取得联系，否则后果自负。</p>
            <br>
            <b>三、用户声明与保证</b><br>
            <p>&nbsp;&nbsp;1、用户承诺其为具有完全民事行为能力的民事主体，且具有达成交易履行其义务的能力。</p>
            <p>&nbsp;&nbsp;2、用户有义务提供注册账号的真实资料，并保证如手机号、姓名、所在地区等有效性及安全性，保证{{appInfo.appName}}APP工作人员可以通过上述联系方式与用户去得联系。同时，用户也有义务在相关资料实际变更时及时更新有关注册资料。</p>
            <p>&nbsp;&nbsp;3、用户通过使用{{appInfo.appName}}APP过程中所制作，上传，下载，复制，发布，传播任何内容，包括但不限于头像，名称，用户说明等注册信息及认证资料，或文章，语言，图片，视频，图文等发送、回复和相关链接页面，以及其他使用账号，或本服务所产生的内容，不得违反国家相关法律制度，包含但不限于如下原则：</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(1)违反宪法所确定的基本原则；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(2)危害国家安全，泄露国家机密，颠覆国家政权，破坏国家统一的；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(3)损害国家荣誉和利益的：</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(4)煽动名族仇恨、名族歧视、破坏名族团结的；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(5)破坏国家宗教政策，宣扬邪教和封建迷信的；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(6)散布谣言，扰乱社会秩序，破坏社会稳定的；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(7)散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(8)侮辱或者诽谤他人，侵害他人合法权益的；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(9)含有法律、行政法规禁止的其他内容的。</p>
            <p>&nbsp;&nbsp;4、用户不得利用“{{appInfo.appName}}APP”账号或本服务制作、上传、下载、复制、发布、传播干扰“{{appInfo.appName}}APP”正常运营，以及侵犯其他用户或第三方合法权益的内容：</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(1)含有任何性或性暗示的；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(2)含有辱骂、恐吓、威胁内容的；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(3)含有骚扰、垃圾广告、恶意信息、诱骗信息；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(4)涉及他人隐私、个人信息或资料的；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(5)危害他人名誉权、肖像权、知识产权、商业秘密等合法利益等；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(6)含有其他干扰本服务正常运营和侵犯其他用户或第三方合法权利内容的信息。</p>
            <br>
            <b>四、服务内容</b><br>
            <p>&nbsp;&nbsp;1、{{appInfo.appName}}APP是江苏商互通信息技术有限公司（以下简称商互通）提供的在线创业平台。</p>
            <p>&nbsp;&nbsp;2、{{appInfo.appName}}APP提供的平台网络服务包括收费和免费。收费服务包括但不限于APP的平台使用收费，网络服务需要支付的其他约定费用。对于收费服务，{{appInfo.appName}}APP在用户使用之前给予用户明确的提示，只有用户根据提示确认同意支付相关费用，用户才能使用该等收费服务。如用户未支付相关费用，则{{appInfo.appName}}APP有权不向用户提供该等收费服务。</p>
            <p>&nbsp;&nbsp;3、用户理解{{appInfo.appName}}APP仅提供{{appInfo.appName}}APP明确承诺的网络服务,除此之外与相关网络服务有关的设备(如个人电脑、手机、及其他与接入互联网或移动网有关的装置）及所需的费用(如为接入互联网而支付的电话费及上网费、为使用移动网络而支付的手机费)均应由用户自行负担。</p>
            <br>
            <b>五、隐私保护</b><br>
            <p>&nbsp;&nbsp;1、保护用户隐私是{{appInfo.appName}}APP的一项基本政策，{{appInfo.appName}}APP保证不对外公开或向第三方提供用户的注册资料及用户在使用网络服务时存储在商互通内的非公开内容，但下列情况除外：</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(1)事先获得用户的书面明确授权；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(2)根据有关的法律法规要求：</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(3)按照相关政府主管部门的要求：</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(4)为维护社会公众的利益；</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(5)为维护商互通的合法权益；</p>
            <p>&nbsp;&nbsp;2、为了更好地为用户提供全面服务，用户同意{{appInfo.appName}}APP可将用户注册资料及使用信息提供{{appInfo.appName}}APP关联公司使用。商互通保证前述关联公司同等级地严格遵循本协议第六条第1款之隐私保护责任。</p>
            <p>&nbsp;&nbsp;3、用户同意：{{appInfo.appName}}APP或{{appInfo.appName}}APP运营商的关联公司在必要时有权根据用户注册时或接受服务时所提供的联系信息（包括但不限于电子邮件地址、联系电话、联系地址、即时聊天工具账号等），通过电子邮件、电话、短信、邮寄、即时聊天、弹出页面等方式向用户发送如下信息：</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(1)各类重要通知信息，可能包括但不限于订单、交易单、修改密码提示等重要信息。此类信息可能对用户的权利义务产生重大的有利或不利影响，用户务必及时关注。</p>
            <p>&nbsp;&nbsp;&nbsp;&nbsp;(2)商品和服务广告、促销优惠等商业性信息。若用户不愿意接收此类信息，则可通过告知（口头或书面）的方式通知{{appInfo.appName}}APP或{{appInfo.appName}}APP运营商的关联公司取消发送，亦可通过{{appInfo.appName}}APP或{{appInfo.appName}}APP运营商关联公司所提供的相应退订功能(若有）进行退订。</p>
            <br>
            <b>六、版权申明</b><br>
            <p>&nbsp;&nbsp;{{appInfo.appName}}APP提供的网络服务中包含的任何文本、图片、图形、音频或视频资料均受版权、商标或其它财产所有权法律的保护，未经相关权利人同意，上述资料均不得在任何媒体直接或间接发布、播放、出于播放或发布目的而改写或再发行，或者被用于其他任何商业目的。所有以上资料或资料的任何部分仅可作为私人和非商业用途保存。{{appInfo.appName}}APP不就由上述资料产生或在传送或递交全部或部分上述资料过程中产生的延误、不准确、错误和遗漏或从中产生或由此产生的任何损害赔偿以任何形式，向用户或任何第三方负责。</p>
            <br>
            <b>七、协议的用途、更新和效力</b><br>
            <p>&nbsp;&nbsp;1、本协议之服务条款用以规范用户使用{{appInfo.appName}}APP提供的服务，本协议与{{appInfo.appName}}APP行为准则构成完整的协议。</p>
            <p>&nbsp;&nbsp;2、鉴于国家法律法规不时变化及{{appInfo.appName}}APP运营之需要，{{appInfo.appName}}APP有权对本协议条款不时地进行修改，修改后的协议一旦被公布于商互通上即告生效，并替代原来的协议。用户有义务不时关注并阅读最新版的协议及网站公告。如用户不同意更新后的协议，则应立即停止接受{{appInfo.appName}}APP依据本协议提供的服务；若用户继续使用{{appInfo.appName}}APP提供的服务的，即视为同意更新后的协议。如果本协议中任何一条被视为废止、无效或因任何理由不可执行，该条应视为可分的且并不影响任何其余条款的有效性和可执行性。</p>
            <br>
            <b>本协议最终解释权归{{appInfo.appName}}APP所有，并且保留一切解释和修改的权利。</b>
            <br><br>
        </article>
    </div>
</template>

<script>
export default {
    name: "Agreement",
    data() {
        return {
            appInfo: {
                appName:'商户通开放平台'
            }
        }
    }
}
</script>

<style lang="less" scoped>
#agreement {
    article {
        width: 100%;
        background-color: #f8f8f8;
        min-height: 100%;
    }
}
</style>