<template>
  <div id="addPolicy">
    <main>
      <section>
        <u-form :model="form" ref="uForm" class="u-form">
          <u-form-item label="政策名称" label-width="140" prop="modelName">
            <u-input
              v-model="form.modelName"
              :clearable="false"
              placeholder="请填写政策名称"
            />
          </u-form-item>
          <p class="interval" />
          <u-form-item label="是否聚合支付通道" label-width="240">
            <u-radio-group v-model="form.subChannelStatus">
              <u-radio :name="1">是</u-radio>
              <u-radio :name="0">否</u-radio>
            </u-radio-group>
          </u-form-item>
          <p class="interval" />
          <section v-if="form.subChannelStatus === 0">
            <u-form-item label="借记卡(%)" prop="debitCost">
              <u-input
                type="digit"
                v-model="form.debitCost"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <u-form-item label="借记卡封顶值(元)" prop="debitCapValue">
              <u-input
                type="number"
                v-model="form.debitCapValue"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <p class="interval" />
            <u-form-item label="信用卡(%)" prop="creditCost">
              <u-input
                type="digit"
                v-model="form.creditCost"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <u-form-item
              v-if="userInfo.showCreditDiscountOpenConf"
              label="信用卡特惠(%)"
              prop="creditDiscountCost"
            >
              <u-input
                type="digit"
                v-model="form.creditDiscountCost"
                :clearable="false"
                placeholder="选填"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <u-form-item
              v-if="userInfo.showSecTransOpenConf"
              label="信用卡秒到交易(%)"
              prop="creditSecTransCost"
            >
              <u-input
                type="digit"
                v-model="form.creditSecTransCost"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <p class="interval" />
            <u-form-item label="扫码(%)" prop="scanCost">
              <u-input
                type="digit"
                v-model="form.scanCost"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <u-form-item label="闪付(%)" prop="passCost">
              <u-input
                type="digit"
                v-model="form.passCost"
                :clearable="false"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
            <u-form-item label="支付宝大额(%)" prop="alipayLargerRate">
              <u-input
                type="digit"
                v-model="form.alipayLargerRate"
                :clearable="false"
                placeholder="选填"
                placeholder-style="font-size:24rpx"
              />
            </u-form-item>
          </section>

          <template v-if="form.subChannelStatus === 1">
            <u-field
              label="支付机构"
              placeholder="选择支付机构"
              :value="payOrgCodeText"
              @click="showPayOrgCodes"
              :right-icon="`arrow-${payOrgCodesPicker ? 'up' : 'down'}`"
              disabled
            />
            <u-select
              v-model="payOrgCodesPicker"
              :list="payOrgCodes"
              label-name="name"
              value-name="code"
              @confirm="payOrgCodeConfirm"
              :default-value="
                payOrgCodeText
                  ? [payOrgCodes.findIndex((i) => i.name === payOrgCodeText)]
                  : []
              "
            />
            <template v-if="subChannelList.length">
              <u-tabs
                :list="subChannelList"
                :current="currentSubChannel"
                @change="changeSubChannel"
              ></u-tabs>
              <section
                v-for="(item, key) in form.costList"
                :key="key"
                v-show="currentSubChannel === key"
              >
                <u-form-item label="借记卡(%)" prop="debitCost" :target="item">
                  <u-input
                    type="digit"
                    v-model="item.debitCost"
                    :clearable="false"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <u-form-item
                  label="借记卡封顶值(元)"
                  prop="debitCapValue"
                  :target="item"
                >
                  <u-input
                    type="number"
                    v-model="item.debitCapValue"
                    :clearable="false"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <p class="interval" />
                <u-form-item label="信用卡(%)" prop="creditCost" :target="item">
                  <u-input
                    type="digit"
                    v-model="item.creditCost"
                    :clearable="false"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <p class="interval" />
                <u-form-item label="扫码(%)" prop="scanCost" :target="item">
                  <u-input
                    type="digit"
                    v-model="item.scanCost"
                    :clearable="false"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <u-form-item label="闪付(%)" prop="passCost" :target="item">
                  <u-input
                    type="digit"
                    v-model="item.passCost"
                    :clearable="false"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <u-form-item
                  label="支付宝大额(%)"
                  prop="alipayLargerRate"
                  :target="item"
                >
                  <u-input
                    type="digit"
                    v-model="item.alipayLargerRate"
                    :clearable="false"
                    placeholder="选填"
                    placeholder-style="font-size:24rpx"
                  />
                </u-form-item>
                <!-- <p class="interval" />
                <u-form-item label="无卡费率(贷记)(%)" prop="eposCreditRate" :target="item">
                  <u-input type="digit" v-model="item.eposCreditRate" :clearable="false" placeholder-style="font-size:24rpx" />
                </u-form-item>
                <u-form-item label="无卡费率(借记)(%)" prop="eposDebitRate" :target="item">
                  <u-input type="digit" v-model="item.eposDebitRate" :clearable="false" placeholder-style="font-size:24rpx" />
                </u-form-item> -->
              </section>
            </template>
          </template>
        </u-form>
      </section>
    </main>

    <footer class="custom-button">
      <button @click="submit">提 交</button>
    </footer>
  </div>
</template>

<script>
  import {
    addSettmentPolicy,
    findJhzfChannel,
    findSubChannelList
  } from '../../http/api';
  import { mapState } from 'vuex';

  const pattern = /^0\.\d{0,3}$/;
  const costs = [
    'creditCost',
    'debitCost',
    'scanCost',
    'passCost',
    'creditSecTransCost',
  ];
  const rules = {
    modelName: [{ required: true, message: '必填' }],
    debitCapValue: [{ required: true, message: '必填' }]
  };
  costs.forEach(
    (c) => (rules[c] = [{ pattern, message: '大于0、小于1且不得超过3位小数' }])
  );
  rules.creditDiscountCost = [
    {
      validator: function (rule, val) {
        if (val === '') return true;
        return pattern.test(val);
      },
      message: '大于0、小于1且不得超过3位小数'
    }
  ];
  rules.alipayLargerRate = [
    {
      validator: function (rule, val) {
        if (val === '') return true;
        return pattern.test(val);
      },
      message: '大于0、小于1且不得超过3位小数'
    }
  ];

  function getDefaultCost() {
    return {
      creditCost: '0.000',
      debitCost: '0.000',
      debitCapValue: '0',
      scanCost: '0.000',
      passCost: '0.000',
      creditDiscountCost: '',
      creditSecTransCost: '0.000',
      alipayLargerRate: '',
      // eposCreditRate: '0.000', // 无卡费率(贷记)
      // eposDebitRate: '0.000' // 无卡费率(借记)
    };
  }
  export default {
    name: 'AddPolicy',
    data() {
      return {
        form: {
          modelName: '',
          subChannelStatus: 0,
          ...getDefaultCost(),
          costList: []
        },
        subChannelList: [],
        currentSubChannel: 0,
        payOrgCodesPicker: false,
        payOrgCodes: [],
        payOrgCodeText: ''
      };
    },
    computed: {
      ...mapState(['userInfo'])
    },
    onReady() {
      this.$refs.uForm.setRules(rules);
    },
    methods: {
      showPayOrgCodes() {
        this.payOrgCodes = [];
        findJhzfChannel().then((res) => {
          if (res.code == '00') {
            if (res.data.length != 0) {
              this.payOrgCodes = res.data;
              this.payOrgCodesPicker = true;
            } else {
              this.$u.toast('暂无可选支付机构！');
            }
          }
        });
      },
      async payOrgCodeConfirm([{ label, value }]) {
        this.payOrgCodeText = label;
        this.form.payOrgCode = value;
        this.payOrgCodesPicker = false;

        var { data } = await findSubChannelList({ payOrgCode: value });
        data = data || [];

        this.currentSubChannel = 0;
        this.subChannelList = data.map((i) => {
          return { name: i.subChannelName };
        });

        this.form.costList = data.map((i) => {
          return Object.assign(i, getDefaultCost());
        });
      },
      changeSubChannel(index) {
        this.currentSubChannel = index;
      },
      submit() {
        this.$refs.uForm.validate((valid) => {
          if (valid) {
            if (!this.form.subChannelStatus) {
              this.form.creditDiscountCost =
                this.form.creditDiscountCost === ''
                  ? null
                  : this.form.creditDiscountCost;
            } else {
              if (!this.form.payOrgCode) {
                this.$u.toast('请选择支付机构！');
                return;
              }
            }
            const { modelName, subChannelStatus, payOrgCode, costList } =
              this.form;

            var params = { modelName, subChannelStatus };

            if (subChannelStatus === 1) {
              params.payOrgCode = payOrgCode;
              params.settlRateDTOList = costList;
            } else {
              params = this.form;
            }

            addSettmentPolicy(params).then((res) => {
              if (res.code == '00') {
                uni.showToast({
                  title: '添加成功',
                  icon: 'none'
                });
                setTimeout(() => {
                  this.$Router.back(1);
                }, 1500);
              }
            });
          } else {
            this.$u.toast('信息填写有误, 请检查！');
          }
        });
      }
    }
  };
</script>

<style lang="less" scoped>
  #addPolicy {
    padding-top: 20rpx;
    main {
      background-color: #fff;
      > div {
        padding: 2vw 30rpx;
        background-color: #f3f5f7;
        > h4 {
          margin: 0;
          color: #222222;
          font-weight: 500;
          font-size: 24rpx;
          span {
            font-size: 24rpx;
            font-weight: 400;
            color: #222222;
            opacity: 0.8;
          }
        }
      }
      > section {
        /deep/ .u-form {
          .u-form-item {
            padding: 20rpx 30rpx;
          }
          > section {
            .u-form-item--left {
              flex: 1 !important;
            }
            .u-form-item--right {
              flex: none;
              width: 140rpx;
              border-radius: 6rpx;
              background: #eaeef1;
              input {
                text-align: center;
                color: #4e77d9;
              }
            }
            .u-form-item__message {
              text-align: right;
            }
          }
        }
      }
    }
    .custom-button {
      margin-top: 40rpx;
    }
  }
</style>
