<template>
    <div id="materialStore">
        <main>
            <header>
                <u-tag @click="materialOrderList" text="前往订单列表" shape="circleLeft" />
            </header>
            <section>
                <no-content v-if="show" />

                <view class="list-data" v-show="!show">
                    <section v-for="(i, index) in listData" :key="index" @click="materialDetail(i)">
                        <div class="cover-img">
                            <image :src="i.smallImagUrl || require('../static/images/giftBagIcon.jpg')" mode="widthFix" />
                        </div>
                        <div class="info">
                            <div>
                                <p>{{ i.activityName }}</p>
                                <p><span>¥</span>{{ (i.unitPrice * i.onceMinLimit) | toDecimal2 }}</p>
                            </div>
                            <div class="buyBtn">去购买</div>
                        </div>
                    </section>
                </view>
            </section>
        </main>
    </div>
</template>

<script>
import { getGiftBagList } from '../../http/api-direct'
import { toDecimal2 } from '../../static/utils/date'

export default {
    name: 'MaterialStore',
    filters: {
        toDecimal2
    },
    data() {
        return {
            show: false,
            listData: [],
            finished: false,
            error: false
        }
    },
    onLoad() {
        this.getListData()
    },
    methods: {
        materialOrderList() {
            this.$Router.push({ name: 'MaterialOrderList' })
        },

        materialDetail(item) {
            this.$Router.push({ name: 'MaterialDetail', params: { detail: JSON.stringify(item) } })
        },

        getListData() {
            getGiftBagList().then(res => {
                if (res.code == '00') {
                    if (res.data != null) {
                        if (res.data.length != 0) {
                            this.show = false
                            this.listData = res.data
                            console.log(res.dat[0])
                        } else {
                            this.show = true
                        }
                    } else {
                        this.show = true
                    }
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>
#materialStore {
    > main {
        header {
            text-align: right;
            padding-top: 20rpx;
        }
        > section {
            padding: 0 30rpx;
            > .list-data {
                overflow-x: hidden;
                > section {
                    display: flex;
                    align-items: center;
                    padding: 20rpx 26rpx;
                    margin-top: 20rpx;
                    background-color: #fff;
                    border-radius: 10rpx;
                    .cover-img {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 160rpx;
                        height: 160rpx;
                        border: 1px solid #e5e5e5;
                        border-radius: 8rpx;
                        > image {
                            width: 100%;
                            max-height: 100%;
                        }
                    }
                    .info {
                        flex: 1;
                        display: flex;
                        align-items: center;
                        > div {
                            &:first-of-type {
                                flex: 1;
                                padding: 0 10rpx 0 16rpx;
                                > p {
                                    color: #333;
                                    &:last-of-type {
                                        font-size: 32rpx;
                                        font-weight: 450;
                                        color: rgb(219, 61, 3);
                                        > span {
                                            font-size: 28rpx;
                                            margin-right: 0.2em;
                                        }
                                    }
                                }
                            }
                        }
                        .buyBtn {
                            padding: 10rpx 28rpx;
                            border-radius: 8rpx;
                            font-size: 24rpx;
                            color: #fff;
                            background-color: rgb(221, 6, 6);
                        }
                    }
                }
            }
        }
    }
}
</style>
