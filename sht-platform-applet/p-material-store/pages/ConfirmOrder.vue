<template>
  <div id="confirmOrder">
    <main>
      <!-- 地址相关 -->
      <section class="address-info" @click="toAddress">
        <div v-if="addressInfo.recipientName">
          <p>
            <span>{{ addressInfo.recipientName }}</span> <span>{{ addressInfo.mobilePhone }}</span>
          </p>
          <p>
            {{ addressInfo.address + addressInfo.fullAddress }}
            <u-icon name="arrow-right" />
          </p>
        </div>
        <p v-else>您还没有收货地址, 点击去添加</p>
      </section>
      <div class="divid-line"></div>
      <!-- 礼包相关 -->
      <section class="goods-info">
        <p>
          <span>礼包名称</span> <span>{{ goodsInfo.activityName }}</span>
        </p>
        <p>
          <span>单价</span><span> <span style="margin-right: 0.2em">¥</span>{{ goodsInfo.unitPrice | toDecimal2 }}</span>
        </p>
        <p>
          <span>数量</span>
          <u-number-box
            disabled-input
            :step="goodsInfo.onceMinLimit"
            v-model="ajaxParams.orderQuantity"
            :min="goodsInfo.onceMinLimit"
            :max="goodsInfo.onceMaxLimit"
          />
        </p>

        <div class="divid-line"></div>
        <p><span>配送方式</span><span>快递配送</span></p>
        <p style="padding-right: 0">
          <span>快递类型</span>
          <u-radio-group v-model="ajaxParams.expressType">
            <u-radio :name="1">普通快递</u-radio>
            <u-radio :name="0">顺丰到付</u-radio>
          </u-radio-group>
        </p>
        <p v-if="ajaxParams.expressType === 1">
          <span>快递费用</span>
          <span> {{ Number(goodsInfo.expressFee) > 0 ? `¥${toDecimal2(goodsInfo.expressFee)}` : '包邮' }}</span>
        </p>
        <div class="divid-line"></div>
        <div>
          共 {{ ajaxParams.orderQuantity }} 件商品 小计: <span>{{ totalAmount | toDecimal2 }}元</span>
        </div>
      </section>
      <section class="payType">
        <p>付款方式 :</p>
        <p>
          <u-radio-group v-model="ajaxParams.payMethod">
            <u-radio :name="4">余额支付</u-radio>
            <u-radio :name="0" v-if="currentMode == 2">线下支付</u-radio>
            <u-radio :name="1" v-if="currentMode == 2">兑换券支付</u-radio>
          </u-radio-group>
        </p>
      </section>
      <div class="divid-line"></div>
      <section v-if="ajaxParams.payMethod === 0" class="uploadImg">
        <div>
          <p @click="upload">
            <image class="upload" v-show="showImgIcon" src="../../static/images/common/photo.png" alt="" />
            <span v-show="showImgIcon">转账凭证图片</span>
            <image class="img" v-show="!showImgIcon" :src="'data:image/jpg;base64,' + transferVoucherPic" alt="" />
          </p>
        </div>
      </section>
    </main>
    <footer>
      <div class="toolBar">
        <span>合计金额: {{ totalAmountAll | toDecimal2 }}元</span>
        <span @click="submit"> 提交订单 </span>
      </div>
    </footer>

    <!-- 支付密码键盘 -->
    <u-keyboard
      mode="number"
      :mask="true"
      :mask-close-able="false"
      :dot-enabled="false"
      v-model="showKeyboard"
      :safe-area-inset-bottom="true"
      :tooltip="false"
      @change="onChangeKeyboard"
      @backspace="onBackspace"
    >
      <view class="popup-content">
        <view class="close" data-flag="false" @tap="showPop(false)">
          <u-icon name="close" color="#333333" size="28"></u-icon>
        </view>
        <view class="order-info">
          <text>订单金额</text>
          <p>
            ¥ <text>{{ totalAmountAll | toDecimal2 }}</text>
          </p>
        </view>
        <view class="pay-method">
          <span>付款方式</span>
          <u-dropdown ref="uDropdown">
            <u-dropdown-item :title="filterDropdownTitle">
              <scroll-view scroll-y="true" style="height: 400rpx">
                <u-cell-group>
                  <u-cell-item
                    v-for="(item, key) in walletType"
                    :key="key"
                    center
                    :arrow="false"
                    use-label-slot
                    @click="togglePaymethod(item)"
                  >
                    <template #title>
                      <span>{{ item.label }}</span>
                      <span style="color: #666">&nbsp; (剩余¥{{ item.balance | toDecimal2 }})</span>
                    </template>
                    <template #label>
                      <span v-if="Number(totalAmountAll) > Number(item.balance)" style="color: #666"
                        >余额不足, 剩余{{ item.balance | toDecimal2 }}元</span
                      >
                    </template>
                    <template #right-icon>
                      <u-icon v-if="ajaxParams.walletType === item.value" name="checkbox-mark" color="#2979ff" />
                    </template>
                  </u-cell-item>
                </u-cell-group>
              </scroll-view>
            </u-dropdown-item>
          </u-dropdown>
        </view>
        <view class="pwd-input">
          <u-message-input
            mode="box"
            :maxlength="6"
            :dot-fill="true"
            :breathe="false"
            v-model="password"
            :disabled-keyboard="true"
          ></u-message-input>
        </view>
      </view>
    </u-keyboard>
  </div>
</template>

<script>
import { getExpressInfoList, orderSubmit } from '../../http/api-direct';
import { walletMoney } from '../../http/api';
import { toDecimal2 } from '../../static/utils/date';

export default {
  name: 'ConfirmOrder',
  filters: {
    toDecimal2
  },
  data() {
    return {
      showKeyboard: false,
      password: '',
      showImgIcon: true,
      transferVoucherPic: '',
      goodsInfo: {},
      addressInfo: {},
      ajaxParams: {
        orderAmount: '', // 交易金额 必填
        orderQuantity: '', // 物料数量 必填
        giftBagId: '', // 活动礼包ID 必填
        expressId: '', // 物流ID 必填
        payMethod: 4, //0-线下支付 1-兑换券支付 2-微信APP 3-支付宝APP
        transferVoucherPic: '',
        walletType: 100,
        payPwd: '',

        expressType: 1,
        expressFee: ''
      },
      walletType: [
        { label: '分润余额', value: 100, balance: '0.00', balanceKey: 'profitWallet' },
        { label: '返现余额', value: 200, balance: '0.00', balanceKey: 'cashBackWallet' },
        { label: '奖励余额', value: 603, balance: '0.00', balanceKey: 'rewardWallet' }
      ]
    };
  },
  computed: {
    totalAmount() {
      return this.goodsInfo.unitPrice * this.ajaxParams.orderQuantity;
    },
    totalAmountAll() {
      if (this.ajaxParams.expressType === 0) return this.totalAmount;
      return jsPlus(this.totalAmount, this.goodsInfo.expressFee || '0');
    },
    filterDropdownTitle() {
      const item = this.walletType.find(w => w.value === this.ajaxParams.walletType);
      return item ? item.label : '请选择';
    },
    currentMode() {
      return this.$store.state.currentMode;
    }
  },
  onLoad() {
    this.goodsInfo = JSON.parse(this.$Route.query.detail);
    this.ajaxParams.orderQuantity = this.goodsInfo.onceMinLimit;
    this.ajaxParams.expressFee = this.goodsInfo.expressFee;

    this.getWalletBalance();
  },
  onShow() {
    if (!this.addressInfo.recipientName) {
      this.getAddress();
    }
  },
  onUnload() {
    uni.$off('select-address');
  },
  methods: {
    getWalletBalance() {
      walletMoney().then(res => {
        this.walletType.forEach(w => {
          w.balance = res.data[w.balanceKey] || '0.00';
        });
      });
    },
    togglePaymethod(item) {
      this.ajaxParams.walletType = item.value;
      this.password = '';
      this.$refs.uDropdown.close();
    },
    showPop(flag = true) {
      this.password = '';
      this.showKeyboard = flag;
    },
    onChangeKeyboard(val) {
      if (this.password.length < 6) {
        this.password += val;
      }

      if (this.password.length >= 6) {
        this.submit();
      }
    },
    onBackspace() {
      if (this.password.length > 0) {
        this.password = this.password.substring(0, this.password.length - 1);
      }
    },
    toAddress() {
      this.$Router.push({ name: 'Address', params: { type: this.addressInfo.recipientName ? '0' : '1' } });
      uni.$once('select-address', data => {
        this.addressInfo = data;
      });
    },
    submit() {
      if (!this.addressInfo.recipientName) {
        return uni.showToast({
          title: '请先添加物流地址',
          icon: 'none'
        });
      }
      if (this.ajaxParams.payMethod === 0 && !this.ajaxParams.transferVoucherPic) {
        return uni.showToast({
          title: '请上传转账凭证图片',
          icon: 'none'
        });
      }
      if (this.ajaxParams.payMethod === 4 && !this.password) {
        return this.showPop(true);
      }

      this.ajaxParams.expressId = this.addressInfo.id;
      this.ajaxParams.giftBagId = this.goodsInfo.id;
      this.ajaxParams.orderAmount = this.totalAmountAll;
      this.ajaxParams.payPwd = this.password;

      orderSubmit(this.ajaxParams).then(res => {
        this.showPop(false);
        if (res.code == '00') {
          uni.showToast({
            title: '订单提交成功',
            icon: 'none'
          });

          setTimeout(() => {
            this.$Router.back(2);
          }, 1500);
        }
      });
    },
    toDecimal2,
    getAddress() {
      getExpressInfoList().then(res => {
        if (res.code == '00') {
          if (res.data.length != 0) {
            var defaultItem = res.data.find(i => i.isDefaultAddressFlag == '1');
            // 有默认取默认,无默认取第一项
            if (defaultItem) {
              this.addressInfo = defaultItem;
            } else {
              this.addressInfo = res.data[0];
            }
          } else {
            this.addressInfo = {};
          }
        }
      });
    },
    upload() {
      uni.chooseImage({
        count: 1,
        success: async res => {
          const base64Data = await this.pathToBase64(res.tempFilePaths[0]);
          if (base64Data) {
            const base64DataSplice = base64Data.split(',')[1];
            this.ajaxParams.transferVoucherPic = this.transferVoucherPic = base64DataSplice;
            this.showImgIcon = false;
          }
        }
      });
    },
    pathToBase64(path) {
      return new Promise((resolve, reject) => {
        if (typeof plus === 'object') {
          plus.io.resolveLocalFileSystemURL(
            this.getLocalFilePath(path),
            function (entry) {
              entry.file(
                function (file) {
                  var fileReader = new plus.io.FileReader();
                  fileReader.onload = function (data) {
                    resolve(data.target.result);
                  };
                  fileReader.onerror = function (error) {
                    reject(error);
                  };
                  fileReader.readAsDataURL(file);
                },
                function (error) {
                  reject(error);
                }
              );
            },
            function (error) {
              reject(error);
            }
          );
          return;
        }
        if (typeof wx === 'object' && wx.canIUse('getFileSystemManager')) {
          wx.getFileSystemManager().readFile({
            filePath: path,
            encoding: 'base64',
            success: function (res) {
              resolve('data:image/png;base64,' + res.data);
            },
            fail: function (error) {
              reject(error);
            }
          });
          return;
        }
        reject(new Error('not support'));
      });
    },
    getLocalFilePath(path) {
      if (
        path.indexOf('_www') === 0 ||
        path.indexOf('_doc') === 0 ||
        path.indexOf('_documents') === 0 ||
        path.indexOf('_downloads') === 0
      ) {
        return path;
      }
      if (path.indexOf('file://') === 0) {
        return path;
      }
      if (path.indexOf('/storage/emulated/0/') === 0) {
        return path;
      }
      if (path.indexOf('/') === 0) {
        var localFilePath = plus.io.convertAbsoluteFileSystem(path);
        if (localFilePath !== path) {
          return localFilePath;
        } else {
          path = path.substr(1);
        }
      }
      return '_www/' + path;
    }
  }
};

function jsPlus(num1, num2) {
  let r1, r2, m;
  try {
    r1 = num1.toString().split('.')[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = num2.toString().split('.')[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  return (num1 * m + num2 * m) / m;
}
</script>

<style lang="less" scoped>
#confirmOrder {
  height: 100%;
  padding-top: 20rpx;
  > main {
    background-color: #fff;
    .address-info {
      padding: 20rpx 30rpx;
      > div {
        > p {
          display: flex;
          align-items: center;
          margin: 6rpx 0;
          &:first-of-type {
            > span {
              &:last-of-type {
                margin-left: 0.8em;
                color: #666;
              }
            }
          }
          &:last-of-type {
            justify-content: space-between;
            font-size: 24rpx;
            color: #888;
          }
        }
      }
      > p {
        letter-spacing: 0.05em;
        color: rgb(226, 148, 4);
      }
    }
    .goods-info,
    .payType {
      padding-bottom: 20rpx;
      > p {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0;
        margin-left: 30rpx;
        padding: 20rpx 30rpx 20rpx 0;
        border-bottom: 2rpx solid #f3f5f7;
        > span {
          color: #333;
          &:first-of-type {
            color: #666;
          }
        }
      }
      > div {
        padding: 20rpx 20rpx 0;
        text-align: right;
        color: rgb(221, 6, 6);
      }
      .express {
        color: #999;
        font-size: 26rpx;
      }
    }
  }
  .divid-line {
    width: 100%;
    height: 20rpx;
    background-color: #f3f5f7;
  }

  .payType {
    > p {
      &:first-of-type {
        padding: 14rpx 0;
        margin-left: 0;
        padding-left: 30rpx;
        color: #666;
        font-size: 26rpx;
        background-color: #f3f5f7;
      }
      &:last-of-type {
        padding-bottom: 0;
        border: none;
      }
    }
  }
  .uploadImg {
    padding: 30rpx;
    > div {
      width: 690rpx;
      display: flex;
      justify-content: center;
      > p {
        width: 396rpx;
        height: 240rpx;
        text-align: center;
        border: 4rpx dashed #666666;
        border-radius: 12rpx;
        margin: 30rpx 0;
        position: relative;
        .upload {
          width: 88rpx;
          height: 88rpx;
          margin: 46rpx 0 6rpx;
        }
        > span {
          font-size: 18rpx;
          color: #222222;
          display: block;
        }
        .img {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
        }
      }
    }
  }
  > footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 80rpx;
    background-color: #fff;
    border-top: 2rpx solid #f3f5f7;
    .toolBar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        &:first-of-type {
          color: rgb(221, 6, 6);
          text-indent: 20rpx;
        }
        &:last-of-type {
          padding: 20rpx 40rpx;
          color: #fff;
          background-color: rgb(221, 6, 6);
        }
      }
    }
  }
}

.popup-content {
  position: relative;
  padding-top: 74rpx;
  .close {
    position: absolute;
    top: 30rpx;
    right: 30rpx;
  }
  .order-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    > p {
      margin-top: 10rpx;
      font-weight: 500;
      > text {
        font-size: 48rpx;
      }
    }
  }

  .pay-method {
    position: relative;
    margin-top: 20rpx;
    border-top: 1px solid #f3f5f7;
    > span {
      position: absolute;
      top: 50%;
      left: 20rpx;
      transform: translateY(-50%);
    }

    ::v-deep {
      .u-dropdown__menu__item {
        justify-content: flex-end;
        .u-flex {
          width: 50%;
          justify-content: flex-end;
          margin-right: 30rpx;
        }
      }
    }
  }

  .pwd-input {
    padding-bottom: 30rpx;
  }

  ::v-deep {
    .u-dropdown__content {
      z-index: 1100 !important;
    }
  }
}
</style>
