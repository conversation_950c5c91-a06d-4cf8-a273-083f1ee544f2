<template>
    <div id="materialOrderList">
        <main>
            <header>
                <u-sticky>
                    <view class="subsection">
                        <u-tabs :is-scroll="false" :list="tabList" :current="type" @change="change" />
                    </view>
                </u-sticky>
                <div class="filter-header">
                    <u-dropdown ref="uDropdown" :close-on-click-mask="false" :close-on-click-self="false">
                        <u-dropdown-item title="筛选">
                            <view class="filter-form">
                                <u-field v-model="ajaxParams.agentName" label="代理商名称" placeholder="请输入代理商名称" clearable />
                                <section class="filtrate-time">
                                    <u-field disabled :border-bottom="false" :value="startTime" placeholder="开始时间" @click="showStartPicker = true" />
                                    <span>—</span>
                                    <u-field disabled :border-bottom="false" :value="endTime" placeholder="结束时间" @click="showEndPicker = true" />
                                </section>
                                <div class="btnTools">
                                    <u-button size="medium" @click="toReset">重置</u-button>
                                    <u-button size="medium" color="#004ea9" type="primary" @click="$refs.uDropdown.close(), getListData()">确定</u-button>
                                </div>
                            </view>
                        </u-dropdown-item>
                    </u-dropdown>
                </div>
            </header>
            <section>
                <no-content v-if="show" />

                <view class="list-data" v-show="!show">
                    <section v-for="(i, index) in listData" :key="index" @click="$Router.push({name:'MaterialOrderDetail',params:{id:i.id}})">
                        <p> <span>代理商名称</span> <span>{{ i.agentName }}</span> </p>
                        <p> <span>礼包名称</span> <span>{{ i.activityName }}</span> </p>
                        <p> <span>交易金额</span> <span>¥{{ i.orderAmount | toDecimal2 }}</span> </p>
                        <div>
                            <span>发货状态</span>
                            <div>
                                <span>{{ i.orderStatus == 0 ? "待发货":i.orderStatus == 1 ? "已发货":i.orderStatus == 2 ? "已收货":"" }}</span>
                                <span v-if="i.agentCode === userInfo.agentCode && i.orderStatus == 1" class="confirmBtn" @click.stop="openExamine(i)" style="margin-left:1em">
                                    确认收货
                                </span>
                            </div>
                        </div>
                    </section>
                </view>
            </section>
        </main>
        <!-- 确认收货弹框 -->
        <u-modal ref="uModal" v-model="showExamine" title="提示" show-cancel-button @confirm="confirmOrder">
            <p class="examineContent">确定要收货吗?</p>
        </u-modal>

        <u-picker v-model="showStartPicker" title="开始时间" :show-time-tag="false" mode="time" @confirm="onConfirmStart" />
        <u-picker v-model="showEndPicker" title="结束时间" :show-time-tag="false" mode="time" @confirm="onConfirmEnd" />
    </div>
</template>



<script>
import { listSimpleOrder, reviewOrder } from "../../http/api-direct";
import { toDecimal2, dateFormat, getDate } from '../../static/utils/date'
import { mapState } from 'vuex';

export default {
    name: "MaterialOrderList",
    components: {
    },
    filters: {
        toDecimal2
    },
    data() {
        return {
            type: 0,
            show: false,
            showExamine: false,
            examineInfo: {
                id: '',
                activityName: ''
            },
            showStartPicker: false,
            minDateStart: new Date(2019, 0, 1),
            maxDateStart: new Date(),
            currentDateStart: new Date(),
            showEndPicker: false,
            minDateEnd: new Date(2019, 0, 1),
            maxDateEnd: new Date(),
            currentDateEnd: new Date(),
            startTime: dateFormat(new Date(getDate(-31))).substring(0, 10),
            endTime: dateFormat(new Date()).substring(0, 10),
            oldParams: null,
            isDisabled: false,
            listData: [],
            ajaxParams: {
                orderStatus: '',     // 订单状态 选填
                agentName: '',        // 代理商名称 选填
                startDate: '',        // 开始时间 必填 默认一个月前 2021-04-01 00:00:00
                endDate: '',          // 结束时间 必填 默认当天 "2021-04-30 23:59:59"
            },
            tabList: [{ name: '全部' }, { name: "待发货" }, { name: "已发货" }, { name: '已收货' }]

        };
    },
    computed: {
    ...mapState(['userInfo']),
        agentLevel() {
            return this.$store.state.userInfo.agentLevel
        }
    },
    onLoad() {
        this.getListData()
    },
    methods: {
        change(index) {
            this.oldParams = null;
            this.type = index
            this.getListData()
        },
        openExamine(item) {
            this.examineInfo = {
                id: item.id,
                activityName: item.activityName
            }
            this.showExamine = true;

        },
        toReset() {
            this.ajaxParams.agentName = '';
            this.startTime = dateFormat(new Date(getDate(-31))).substring(0, 10);
            this.endTime = dateFormat(new Date()).substring(0, 10);
        },
        confirmOrder() {
            reviewOrder(this.examineInfo.id).then(res => {
                if (res.code == '00') {
                    var item = this.listData.find(i => i.id == this.examineInfo.id);
                    item.orderStatus = 2;
                }
            })
        },
        getListData() {
            this.ajaxParams.orderStatus = this.type == '0' ? "" : this.type - 1;
            this.ajaxParams.startDate = this.startTime + " 00:00:00";
            this.ajaxParams.endDate = this.endTime + " 23:59:59";
            if (JSON.stringify(this.oldParams) == JSON.stringify(this.ajaxParams)) {
                return;
            }
            listSimpleOrder(this.ajaxParams).then((res) => {
                if (res.code == "00") {
                    if (res.data && res.data.list.length != 0) {
                        this.show = false;
                        this.listData = res.data.list;
                        uni.pageScrollTo({
                            scrollTop: 0
                        });
                        this.finished = true;
                    } else {
                        this.show = true;
                    }
                }
            });
        },
        onConfirmStart(val) {
            this.startTime = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.showStartPicker = false;
        },
        onConfirmEnd(val) {
            this.endTime = dateFormat(val.timestamp * 1000).substring(0, 10);
            this.showEndPicker = false;
        }
    }
};
</script>
<style lang="less">
@import "../../static/css/game.less";
</style>
<style lang="less" scoped>
#materialOrderList {
    height: 100%;
    > main {
        position: relative;
        width: 100%;
        .subsection {
            background-color: #fff;
        }
        > section {
            width: 100vw;
            padding: 20rpx 30rpx 0;
            > .list-data {
                overflow-x: hidden;
                > section {
                    padding: 20rpx 30rpx;
                    margin-bottom: 20rpx;
                    background-color: #fff;
                    border-radius: 16rpx;
                    > p,
                    > div {
                        display: flex;
                        margin: 6rpx 0;
                        color: #666;
                        > span {
                            &:first-of-type {
                                width: 6.2em;
                                color: rgb(134, 134, 134);
                                font-size: 26rpx;
                            }
                        }

                        .loansType1 {
                            color: green;
                        }
                        .loansType2 {
                            color: rgb(219, 61, 4);
                        }

                        .confirmBtn {
                            padding: 2rpx 10rpx;
                            text-align: right;
                            color: #fff;
                            border-radius: 8rpx;
                            background-color: orange;
                        }
                    }
                    > div {
                        justify-content: space-between;
                    }
                    &:last-of-type {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
    .examineContent {
        padding: 30rpx 0;
        text-align: center;
        color: #333;
    }
}
</style>