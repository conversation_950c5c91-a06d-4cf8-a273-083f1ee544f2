<template>
    <div id="editAddress">
        <main>
            <u-form :model="address" ref="uForm" label-width="auto">
                <u-form-item label="收件人" prop="recipientName">
                    <u-input v-model="address.recipientName" placeholder="请填写收件人姓名" />
                </u-form-item>
                <u-form-item label="联系电话" prop="mobilePhone">
                    <u-input v-model="address.mobilePhone" type="tel" placeholder="请填写联系电话" />
                </u-form-item>
                <u-form-item label="收货地址" prop="address">
                    <u-input type="select" :select-open="showSites" placeholder="请选择收货地址" v-model="address.address" @click="showSites = true" />
                </u-form-item>
                <u-picker mode="region" v-model="showSites" @confirm="siteFinish" />

                <u-form-item label="详细地址" prop="fullAddress">
                    <u-input v-model="address.fullAddress" placeholder="详细地址: 例如小区、街道" />
                </u-form-item>
                <u-form-item label="是否设为默认地址">
                    <u-radio-group v-model="address.isDefaultAddressFlag">
                        <u-radio :name="0">否</u-radio>
                        <u-radio :name="1">是</u-radio>
                    </u-radio-group>
                </u-form-item>
            </u-form>

            <view class="submitBtn">
                <u-button type="primary" @click="submit">提交</u-button>
            </view>
        </main>
    </div>
</template>

<script>
import {
    getProvince,
    getCity,
    getCounty,
    addExpress,
    editExpress
} from "../../../http/api-direct";
export default {
    name: "EditAddress",
    data() {
        return {
            site: '',
            siteInfo: "",
            showSites: false,
            sites: [],
            address: {
                recipientName: '',
                mobilePhone: '',
                address: '',
                fullAddress: '',
                isDefaultAddressFlag: 0
            },
            title: '新增收货地址',
            rules: {
                recipientName: [{ required: true, message: '必填' }],
                address: [{ required: true, message: '必选' }],
                fullAddress: [{ required: true, message: '必填' }],
                mobilePhone: [{
                    required: true, pattern: /^1[3456789]\d{9}$/, message: '必填且需输入正确格式'
                }]
            }
        }
    },
    onLoad() {
        if (this.$Route.query.addressInfo) {
            this.address = JSON.parse(this.$Route.query.addressInfo);
            this.title = '编辑收货地址'
        }
    },
    onReady() {
        uni.setNavigationBarTitle({
            title: this.title
        });
        this.$refs.uForm.setRules(this.rules)
    },
    methods: {
        submit() {
            this.$refs.uForm.validate(valid => {
                if (valid) {
                    var subMethod = this.$Route.query.addressInfo ? editExpress : addExpress;
                    subMethod(this.address).then(res => {
                        if (res.code == '00') {
                            uni.showToast({
                                title: res.message,
                                icon: 'none'
                            });
                            setTimeout(() => {
                                this.$Router.back(1);
                            }, 1500);
                        }
                    })
                }
            })

        },
        getSite() {
            getProvince().then((res) => {
                if (res.code == "00") {
                    this.sites = [];
                    res.data.forEach((i) => {
                        this.sites.push({
                            text: i.areaName,
                            value: i.areaCode,
                            children: [],
                        });
                    });
                    this.showSites = true;
                }
            });
        },
        siteChange({ value, selectedOptions, tabIndex }) {
            if (tabIndex == 0) {
                getCity(value).then((res) => {
                    if (res.code == "00") {
                        this.sites[
                            this.sites.findIndex((i) => i.value == value)
                        ].children = [];
                        res.data.forEach((i) => {
                            this.sites[
                                this.sites.findIndex((i) => i.value == value)
                            ].children.push({
                                text: i.areaName,
                                value: i.areaCode,
                                children: []
                            });
                        });
                    }
                });
            }
            if (tabIndex == 1) {
                getCounty(value).then((res) => {
                    if (res.code == "00") {
                        var pIndex = this.sites.findIndex((i) => i.value == selectedOptions[0].value);
                        var cIndex = this.sites[pIndex].children.findIndex((i) => i.value == value);
                        this.sites[pIndex].children[cIndex].children = [];
                        res.data.forEach((i) => {
                            this.sites[pIndex].children[cIndex].children.push({
                                text: i.areaName,
                                value: i.areaCode,
                            });
                        });
                    }
                });
            }
        },
        siteFinish(val) {
            const { province, city, area } = val
            this.address.address = province.label + city.label + area.label
        },

    },
}
</script>

<style lang="less" scoped>
#editAddress {
    height: 100%;
    padding-top: 20rpx;
    main {
        min-height: 100%;
        padding: 0 30rpx;
        background-color: #fff;
    }
    .divid-line {
        width: 100%;
        height: 20rpx;
        background-color: #f3f5f7;
    }
    .submitBtn {
        margin: 200rpx 50rpx;
        color: #fff;
    }
}
</style>