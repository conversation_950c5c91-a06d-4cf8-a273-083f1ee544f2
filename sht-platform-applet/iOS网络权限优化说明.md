# iOS网络权限优化说明

## 问题描述
在iOS应用首次启动时，系统会请求网络权限。如果用户拒绝或还未授权，`checkIosIsPass`方法中的`loginSwitch`接口请求会失败，导致无法正确设置`isIosCheckPass`状态，影响应用的正常功能。

## 优化方案

### 1. 网络状态检测
- 添加`checkNetworkStatus()`方法检测当前网络连接状态
- 在发起网络请求前先检查网络可用性

### 2. 超时处理
- 为`loginSwitch`接口请求添加5秒超时限制
- 使用`Promise.race`实现超时控制

### 3. 错误处理策略
- **网络未连接**：使用默认配置（从本地存储获取上次配置）
- **请求超时**：使用默认配置，并延迟3秒后重试
- **接口异常**：使用默认配置

### 4. 默认配置机制
- `setDefaultIosConfig()`方法从本地存储获取上次的配置
- 如果没有历史配置，默认设置为通过状态（`isIosCheckPass = 1`）

### 5. 延迟重试机制
- 当检测到可能是网络权限问题时，延迟3秒后自动重试
- 重试时会重新检查网络状态和接口调用

### 6. 前台重检机制
- 在`onShow`生命周期中添加`checkIosStatusOnShow()`方法
- 当应用从后台回到前台时，如果满足以下条件会重新检查：
  - 当前状态为null（未初始化）
  - 距离上次检查超过5分钟

### 7. 代码结构优化
- 提取`setTabBarVisibility()`方法统一处理TabBar显示/隐藏
- 添加详细的日志输出便于调试
- 记录检查时间戳避免频繁重复检查

## 主要改进点

1. **容错性增强**：网络请求失败时不会影响应用正常启动
2. **用户体验优化**：即使网络权限未授权，应用也能正常使用
3. **智能重试**：在合适的时机自动重新检查状态
4. **性能优化**：避免频繁的网络请求，添加时间间隔控制

## 使用场景

- **首次安装**：用户首次安装应用时，网络权限弹窗出现前应用能正常启动
- **网络权限拒绝**：用户拒绝网络权限后，应用使用默认配置继续运行
- **网络权限后授权**：用户后续授权网络权限后，应用会在合适时机更新配置
- **网络不稳定**：在网络不稳定的环境下，应用能够优雅降级

这个优化方案确保了iOS应用在各种网络权限状态下都能正常启动和运行，提升了用户体验和应用的稳定性。
