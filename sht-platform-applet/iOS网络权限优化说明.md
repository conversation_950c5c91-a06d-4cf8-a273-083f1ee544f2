# iOS网络权限优化说明

## 问题描述
在iOS应用首次启动时，系统会在应用内弹出网络权限授权弹窗（如"允许'商互通平台'使用无线数据?"）。这个弹窗会阻塞当前的网络请求，直到用户做出选择。如果在`checkIosIsPass`方法执行时弹出此弹窗，会导致：
1. 应用启动被阻塞，用户体验差
2. 用户拒绝网络权限后，应用无法正常设置`isIosCheckPass`状态
3. 网络请求超时或失败，影响应用功能

## 优化方案

### 1. 非阻塞启动策略
- **先设置默认配置**：在网络请求前先设置默认配置，确保应用能立即启动
- **延迟网络请求**：将网络请求延迟1秒执行，让应用先完成基本初始化
- **异步处理**：网络请求在后台异步执行，不阻塞主流程

### 2. 长超时处理
- 将超时时间设置为15秒，给用户充足时间处理网络权限弹窗
- 用户可以从容阅读弹窗内容并做出选择

### 3. 智能错误识别
- 添加`isNetworkPermissionError()`方法识别网络权限相关错误
- 区分网络权限问题和其他类型的网络错误

### 4. 默认配置机制
- `setDefaultIosConfig()`方法从本地存储获取上次的配置
- 如果没有历史配置，默认设置为通过状态（`isIosCheckPass = 1`）
- 确保应用在任何情况下都能正常启动

### 5. 重试标记机制
- 当检测到网络权限问题时，设置`needRetryIosCheck`标记
- 在应用回到前台时检查此标记，决定是否需要重试

### 6. 优化的前台重检
- 检查重试标记、状态和时间间隔（10分钟）
- 清除重试标记避免重复检查
- 失败时重新设置重试标记

## 核心流程

### 启动时流程
1. **立即设置默认配置** → 应用可以正常启动和显示
2. **延迟1秒** → 让应用完成基本初始化
3. **发起网络请求** → 可能触发iOS网络权限弹窗
4. **用户处理弹窗** → 允许/拒绝/仅限局域网
5. **根据结果更新配置** → 如果成功获取到数据则更新UI

### 前台重检流程
1. **检查重试标记** → 是否需要重试上次失败的请求
2. **检查时间间隔** → 避免频繁请求（10分钟间隔）
3. **发起重试请求** → 尝试获取最新配置
4. **更新配置** → 成功时更新，失败时保持重试标记

## 主要改进点

1. **非阻塞启动**：应用启动不会被网络权限弹窗阻塞
2. **用户友好**：给用户充足时间（15秒）处理权限弹窗
3. **智能重试**：失败后在合适时机自动重试
4. **状态保持**：使用本地存储保持配置状态
5. **错误识别**：准确识别网络权限相关错误

## 使用场景

- **首次安装**：应用立即启动，网络权限弹窗不阻塞界面显示
- **用户允许网络**：后台获取正确配置并更新UI
- **用户拒绝网络**：应用使用默认配置正常运行，后续可重试
- **用户选择仅限局域网**：根据实际网络情况处理
- **应用切换**：从后台回到前台时智能重试失败的请求

## 技术要点

```javascript
// 关键优化点
setTimeout(async () => {
  // 延迟执行，避免阻塞启动
  const { data } = await Promise.race([
    loginSwitch({ version: inf.version }),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error('网络权限请求超时')), 15000)
    )
  ]);
}, 1000);
```

这个优化方案完美解决了iOS网络权限弹窗对应用启动的影响，确保用户体验流畅。
