<template>
    <div id="merchantSearch">
        <main>
            <section>
                <div @click="toMerchantList(0)">
                    <span>优质商户</span>
                    <div>
                        <span>{{info.superMerchCount}}</span>
                        <image src="../../static/images/home/<USER>" alt="" />
                    </div>
                </div>
                <p> 昨日交易笔数占团队总笔数≥{{info.merchSummaryTotalScaleNums}}%的商户 </p>
            </section>
            <section>
                <div @click="toMerchantList(1)">
                    <span class="mark__blue">活跃商户</span>
                    <div>
                        <span>{{info.activeAtoMerchCount}}</span>
                        <image src="../../static/images/home/<USER>" alt="" />
                    </div>
                </div>
                <p> 近{{info.merchSummaryActiveDays}}日均有交易的商户, 且日均笔数≥{{info.merchSummaryAverageNums}}笔 </p>
            </section>
            <section>
                <div @click="toMerchantList(2)">
                    <span class="mark__orange">预流失商户</span>
                    <div>
                        <span>{{info.preLossMerchCount}}</span>
                        <image src="../../static/images/home/<USER>" alt="" />
                    </div>
                </div>
                <p> {{info.merchSummaryLossDays}}天以上无任何交易的商户 </p>
            </section>
            <section>
                <div @click="toMerchantList(3)">
                    <span class="mark__red">沉睡商户</span>
                    <div>
                        <span>{{info.sleepMerchCount}}</span>
                        <image src="../../static/images/home/<USER>" alt="" />
                    </div>
                </div>
                <p> {{info.merchSummarySleepDays}}天以上无任何交易的商户 </p>
            </section>
        </main>
    </div>
</template>

<script>
import { getMerchSummaryInfData } from '../../http/api'
export default {
    data() {
        return {
            info: {
                superMerchCount: 0,//优质商户数   
                activeAtoMerchCount: 0,//活跃商户数   
                preLossMerchCount: 0,//预流失商户数
                sleepMerchCount: 0,//沉睡商户数
                /*以下取自对应1代配置*/
                merchSummaryTotalScaleNums: 0,//总占笔数
                merchSummaryActiveDays: 0,//活跃天数
                merchSummaryAverageNums: 0,//日均笔数
                merchSummaryLossDays: 0,//预流失天数
                merchSummarySleepDays: 0//沉睡天数
            }
        };
    },
    onLoad() {
        getMerchSummaryInfData().then(res => {
            if (res.code == '00') {
                this.info = Object.assign(this.info, res.data || {})
            }
        })
    },
    methods: {
        toMerchantList(type) {
            this.$Router.push({ name: 'MerchantTypeList', params: { merch_type: type } })
        }
    }
};
</script>

<style lang="less" scoped>
#merchantSearch {
    > main {
        min-height: 100vh;
        padding: 30rpx;
        background-color: #f3f5f7;
        > section {
            margin-bottom: 30rpx;
            padding: 30rpx 30rpx 30rpx 32rpx;
            border-radius: 8rpx;
            background-color: #fff;
            > div {
                display: flex;
                > span {
                    position: relative;
                    font-size: 30rpx;
                    font-weight: bolder;
                    &::after {
                        content: "";
                        position: absolute;
                        left: -16rpx;
                        top: 50%;
                        width: 8rpx;
                        height: 60%;
                        transform: translateY(-50%);
                        background-color: #00a90e;
                        border-radius: 8rpx;
                    }
                }
                .mark__ {
                    &blue {
                        &::after {
                            background-color: rgb(0, 110, 255);
                        }
                    }
                    &orange {
                        &::after {
                            background-color: rgb(255, 187, 0);
                        }
                    }
                    &red {
                        &::after {
                            background-color: rgb(230, 49, 79);
                        }
                    }
                }
                > div {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    margin-left: 20rpx;
                    > image {
                        width: 20rpx;
                        height: 20rpx;
                        margin-left: 12rpx;
                    }
                }
            }
            > p {
                margin: 30rpx 0 0;
                padding-top: 30rpx;
                border-top: 2rpx solid #e5e5e5;
                font-size: 26rpx;
                color: grey;
            }
        }
    }
}
</style>
