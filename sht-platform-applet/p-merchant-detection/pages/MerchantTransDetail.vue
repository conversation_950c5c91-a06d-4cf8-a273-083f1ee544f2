<template>
    <div id="merchantTransDetail">
        <main>
            <header>
                <div>
                    <span>商户名称:</span>
                    <span>{{merchantName}}</span>
                </div>
                <div>
                    <span>商户编号:</span>
                    <span><text user-select selectable>{{merchantId}} </text></span>
                </div>
            </header>

            <section v-for="(i,key) in list" :key="key">
                <span>{{i.summaryDate}}</span>
                <span>¥{{i.summaryTransAmt | toDecimal2}}</span>
            </section>
            <p v-if="showTip">暂未查询到近期交易</p>
        </main>
    </div>
</template>

<script>
import { getMerchDetailListByMerchNo } from '../../http/api'
import { toDecimal2 } from '../../static/utils/date';
export default {
    filters: {
        toDecimal2
    },
    data() {
        return {
            merchantId: '',
            merchantName: '',
            list: [],
            showTip: false
        };
    },
    onLoad() {
        this.getList()
    },
    methods: {
        async getList() {
            const { data } = await getMerchDetailListByMerchNo(this.$Route.query.merchantId)
            this.list = data.summaryDataList || []
            this.merchantId = data.merchantId || ''
            this.merchantName = data.merchantName || ''
            this.showTip = !this.list.length
        },
    }
};
</script>

<style lang="less" scoped>
#merchantTransDetail {
    > main {
        min-height: 100vh;
        padding: 20rpx 30rpx;
        background-color: #f3f5f7;
        > header {
            padding: 60rpx 40rpx;
            border-radius: 16rpx;
            background: url("../../static/images/common/commonBG.png")
                no-repeat bottom rgb(66, 121, 184);
            background-size: 100% 140rpx;
            box-shadow: 0rpx 0rpx 30rpx 0rpx rgba(0, 0, 0, 0.3);
            > div {
                display: flex;
                color: #fff;
                > span {
                    flex-shrink: 0;
                    &:last-of-type {
                        flex: 1;
                        word-break: break-word;
                        margin-left: 20rpx;
                    }
                }
                & + div {
                    margin-top: 20rpx;
                }
            }
        }

        > section {
            display: flex;
            justify-content: space-between;
            padding: 24rpx;
            margin-top: 20rpx;
            background-color: #fff;
            border-radius: 12rpx;
            box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.1);
        }

        > p {
            margin: 40rpx 0;
            text-align: center;
            color: grey;
        }
    }
}
</style>
