<template>
    <div id="confirmWithdrawMoney">
        <p class="interval" />
        <view class="u-form">
            <section>
                <u-field label="提现金额(元)" :label-width="200" :value="confirmInfo.withdrawAmount" disabled />
                <u-field label="还款比例(%)" :label-width="200" :value="confirmInfo.rate" disabled />
                <u-field label="还款金额(元)" :label-width="200" :value="confirmInfo.repaymentAmount" disabled />
                <u-field label="到账金额(元)" :label-width="200" :value="confirmInfo.realWithdrawAmount" disabled />
                <u-field label="剩余还款(元)" :label-width="200" :value="confirmInfo.remainingRepaymentAmount" disabled />
            </section>
            <footer class="submit-button">
                <u-button type="primary" @click="submit">确认</u-button>
            </footer>
        </view>

        <u-keyboard id="enter-psw" mode="number" v-model="showEnterPsw" mask safe-area-inset-bottom :show-tips="false" @change="inputPsw" @backspace="backspace" @confirm="withdraw">
            <section>
                <u-message-input v-model="payPsw" :maxlength="6" breathe dot-fill disabled-keyboard />
                <p @click="forgetPayPsw">忘记支付密码？</p>
            </section>
        </u-keyboard>
    </div>
</template>

<script>
import { getPayPsw, withdraw } from "../../http/api";
import { toDecimal2 } from '../../static/utils/date';

export default {
    name: "ConfirmWithdrawMoney",
    data() {
        return {
            showEnterPsw: false,
            payPsw: "",
            confirmInfo: {
                walletType: '',
                withdrawAmount: '0',//提现金额
                rate: '',    //还款比例
                repaymentAmount: '0',    //还款金额
                realWithdrawAmount: '0',    //实际提现金额
                remainingRepaymentAmount: '0'    //剩余还款金额
            },
            isLocked: false
        };
    },
    watch: {
        payPsw(val) {
            if (val.length > 6) {
                this.payPsw = val.substr(0, 6);
            }
        },
    },
    onLoad() {
        this.confirmInfo = {
            walletType: this.$Route.query.walletType,
            withdrawAmount: toDecimal2(this.$Route.query.withdrawAmount),
            rate: this.$Route.query.rate,
            repaymentAmount: toDecimal2(this.$Route.query.repaymentAmount),
            realWithdrawAmount: toDecimal2(this.$Route.query.realWithdrawAmount),
            remainingRepaymentAmount: toDecimal2(this.$Route.query.remainingRepaymentAmount)
        }
    },
    methods: {
        inputPsw(val) {
            this.payPsw += val;
        },
        backspace(val) {
            if (this.payPsw.length) this.payPsw = this.payPsw.substr(0, this.payPsw.length - 1);
        },
        setPsw() {
            this.$Router.push({ name: 'SetResetPayPsw', params: { type: 0 } });
        },
        forgetPayPsw() {
            this.$Router.push({ name: 'SetResetPayPsw', params: { type: 1 } });
            this.showEnterPsw = false;
        },
        submit() {
            getPayPsw().then(res => {
                if (res.code == "00") {
                    if (res.data) {
                        this.payPsw = '';
                        this.showEnterPsw = true;
                    } else {
                        uni.showModal({
                            content: '请设置支付密码进行提现',
                            success: (res) => res.confirm && this.setPsw()
                        });
                    }
                }
            })
        },
        withdraw() {
            if (this.payPsw.length <= 6) {
                if (this.isLocked) { return uni.showToast({ title: '处理中, 请勿重复操作', icon: 'none' }) }
                this.isLocked = true
                withdraw({
                    walletType: this.confirmInfo.walletType,
                    withdrawAmount: parseFloat(this.confirmInfo.withdrawAmount),
                    payPwd: this.payPsw
                }).then((res) => {
                    this.isLocked = false
                    this.showEnterPsw = false;
                    if (res.code == "00") {
                        uni.showToast({
                            title: res.message,
                            icon: 'none'
                        });
                        setTimeout(() => {
                            this.$Router.back(1);
                        }, 1500);
                    }
                })
            }
        }
    },
};
</script>

<style lang="less" scoped>
#confirmWithdrawMoney {
    height: 100%;
    background-color: #fff;
    #enter-psw {
        section {
            margin-top: 30rpx;
            > p {
                margin: 20rpx 0;
                text-align: center;
                color: #4e77d9;
            }
        }
    }
    /deep/ #set-psw {
        width: 540rpx;
        height: 280rpx;
        p {
            color: #000000;
            font-size: 26rpx;
        }
    }
    .submit-button {
        padding: 150rpx 60rpx;
    }
}
</style>