<template>
    <view id="withdrawMoney">
        <!-- <u-tag @click="addSettlePrice" text="提现记录" shape="circleLeft" /> -->
        <main>
            <div>
                <template v-if="userInfo.payChannelCode === '1006'">
                    <image src="../static/images/zfb.png" alt="支付宝" mode="widthFix" />
                    <section>
                        <p>支付宝账户</p>
                        <p>
                            <span>{{ userInfo.accountName + ' ' + (userInfo.receiveType === 1 ? userInfo.email : userInfo.mobile) }}</span>
                            <span>提现到支付宝余额</span>
                        </p>
                    </section>
                </template>
                <template v-else>
                    <image src="../static/images/bankCard.png" alt="银行卡" mode="widthFix" />
                    <section>
                        <p>{{ bankCard.bankName }}</p>
                        <p>
                            <span>尾号{{ bankCard.bankCardNoMask.substring(bankCard.bankCardNoMask.length - 4) }}储蓄卡</span>
                            <span>到账银行卡</span>
                        </p>
                    </section>
                </template>
            </div>
            <div>
                <div>
                    <span>{{ kind == '增值' ? '增值返佣' : kind }}余额</span>
                    <view @click="showMoneyType = true">
                        <image src="../static/images/more.png" alt="" />
                    </view>
                </div>
                <section>
                    <div>
                        <p>
                            <image src="../../static/images/home/<USER>" alt="" />
                            <span>可用余额(元)</span>
                        </p>
                        <p>
                            {{ kind == '分润' ? toDecimal2(wallet.profit.profitWallet) : kind == '返现' ? toDecimal2(wallet.cashBack.cashBackWallet) : kind == '增值' ? toDecimal2(wallet.game.gameProfitWallet) : kind == '东山岛海产品返佣' ? toDecimal2(Number(wallet.join03.joinProfitWallet)) : kind == '青田泽济农资返佣' ? toDecimal2(Number(wallet.join04.joinProfitWallet)) : kind == '鹏农商城返佣' ? toDecimal2(Number(wallet.join05.joinProfitWallet)) : kind == '理财POS分润钱包' ? toDecimal2(Number(wallet.finance.financeProfitWallet)) : toDecimal2(Number(wallet.reward.rewardWallet)) }}
                        </p>
                    </div>
                    <div>
                        <p>
                            <image src="../../static/images/home/<USER>" alt="" />
                            <span>不可用余额(元)</span>
                        </p>
                        <p>
                            {{ kind == '分润' ? toDecimal2(wallet.profit.profitFreezeWallet) : kind == '返现' ? toDecimal2(wallet.cashBack.cashBackFreezeWallet) : kind == '增值' ? toDecimal2(wallet.game.gameProfitFreezeT1Wallet) : kind == '东山岛海产品返佣' ? toDecimal2(Number(wallet.join03.joinFreezeWallet)) : kind == '青田泽济农资返佣' ? toDecimal2(Number(wallet.join04.joinFreezeWallet)) : kind == '鹏农商城返佣' ? toDecimal2(Number(wallet.join05.joinFreezeWallet)) : kind == '理财POS分润钱包' ? toDecimal2(Number(wallet.finance.financeProfitFreezeWallet)) : toDecimal2(Number(wallet.reward.rewardFreezeWallet)) }}
                        </p>
                    </div>
                </section>
            </div>
            <div>
                <p>
                    <span v-show="withdrawStatus">
                        提现金额<span>(不得低于{{ minAmount }}元，提现秒到时间{{ startTime }}-{{ endTime }})</span>
                    </span>
                    <span v-show="!withdrawStatus">提现功能临时关闭</span>
                </p>
                <view class="all-withdraw">
                    <image src="../static/images/cash.png" alt="" />
                    <view>
                        <u-field type="digit" v-model="money" :disabled="!withdrawStatus" placeholder="请输入现金金额" :clearable="false">
                            <u-button :ripple="true" ripple-bg-color="#f8f8f8" size="mini" slot="right" type="primary" @click="all">全部提现</u-button>
                        </u-field>
                    </view>
                </view>

                <p>
                    <span>可提现金额：</span>
                    <span>{{ kind == '分润' ? toDecimal2(wallet.profit.profitWallet) : kind == '返现' ? toDecimal2(wallet.cashBack.cashBackWallet) : kind == '增值' ? toDecimal2(wallet.game.gameProfitWallet) : kind == '东山岛海产品返佣' ? toDecimal2(Number(wallet.join03.joinProfitWallet)) : kind == '青田泽济农资返佣' ? toDecimal2(Number(wallet.join04.joinProfitWallet)) : kind == '鹏农商城返佣' ? toDecimal2(Number(wallet.join05.joinProfitWallet)) : kind == '理财POS分润钱包' ? toDecimal2(Number(wallet.finance.financeProfitWallet)) : toDecimal2(Number(wallet.reward.rewardWallet)) }}元</span>
                </p>
            </div>
            <section v-if="showProtocol">
                <p class="u-flex u-row-center">
                    <u-checkbox v-model="agreement" />
                    <span>我已阅读并同意</span><span @click="protocol">《服务合作协议》</span>
                </p>
            </section>
            <view class="withdraw-btn">
                <u-button @click="submit" :disabled="!withdrawStatus" type="primary">去提现</u-button>
            </view>
            <view class="record" @click="$Router.push({ name: 'WithdrawRecord' })"> 提现记录 </view>
            <picker-view v-if="visible" :indicator-style="iewindicatorStyle" :value="value" @change="bindChange">
                <picker-view-column>
                    <view class="item" v-for="(item, index) in years" :key="index">{{ item }}年</view>
                </picker-view-column>
            </picker-view>
        </main>

        <!-- 选择余额类型 -->
        <u-select v-model="showMoneyType" :list="moneyTypes" value-name="val" label-name="val" @confirm="moneyTypeConfirm" />
        <u-select v-model="showJoinType" title="请选择加盟返佣类型" :list="joinTypes" value-name="val" label-name="val" @confirm="moneyTypeConfirm" />

        <u-keyboard id="enter-psw" mode="number" v-model="showEnterPsw" mask safe-area-inset-bottom :show-tips="false" @change="inputPsw" @backspace="backspace" @confirm="withdraw">
            <section>
                <u-message-input v-model="payPsw" :maxlength="6" breathe dot-fill disabled-keyboard />
                <p @click="forgetPayPsw">忘记支付密码？</p>
            </section>
        </u-keyboard>
    </view>
</template>

<script>
import { walletMoney, getSettleCard, getPayPsw, queryLoans, withdraw, getRegisterStatus } from '../../http/api'

const seriesMap = {
    东山岛海产品返佣: '03',
    青田泽济农资返佣: '04',
    鹏农商城返佣: '05'
}

export default {
    name: 'WithdrawMoney',
    data() {
        return {
            kind: '分润',
            showMoneyType: false,
            moneyTypes: [{ val: '分润余额' }, { val: '返现余额' }, { val: '奖励钱包' }, { val: '增值返佣' }, { val: '加盟返佣' }],
            showJoinType: false,
            joinTypes: [{ val: '东山岛海产品' }, { val: '青田泽济农资' }, { val: '鹏农商城' }],
            wallet: {
                profit: {
                    profitWallet: '0.00',
                    profitFreezeWallet: '0.00'
                },
                cashBack: {
                    cashBackWallet: '0.00',
                    cashBackFreezeWallet: '0.00'
                },
                game: {
                    gameProfitWallet: '0.00',
                    gameProfitFreezeT1Wallet: '0.00'
                },
                join03: {
                    joinProfitWallet: '0.00',
                    joinFreezeWallet: '0.00'
                },
                join04: {
                    joinProfitWallet: '0.00',
                    joinFreezeWallet: '0.00'
                },
                join05: {
                    joinProfitWallet: '0.00',
                    joinFreezeWallet: '0.00'
                },
                finance: {
                    financeProfitWallet: '0.00',
                    financeProfitFreezeWallet: '0.00'
                },
                reward: {
                    rewardWallet: '0.00',
                    rewardFreezeWallet: '0.00'
                }
            },
            bankCard: {
                bankName: '',
                bankCardNoMask: ''
            },
            withdrawStatus: true,
            minAmount: '',
            startTime: '',
            endTime: '',
            money: null,
            showProtocol: false,
            agreement: false,
            showEnterPsw: false,
            payPsw: '',
            isLocked: false
        }
    },
    computed: {
        userInfo() {
            return this.$store.state.userInfo
        }
    },
    watch: {
        payPsw(val) {
            if (val.length > 6) {
                this.payPsw = val.substr(0, 6)
            }
        }
    },
    onShow() {
        // this.showProtocol = this.$store.state.userInfo.payTaxType == 1
        walletMoney().then(res => {
            if (res.code == '00') {
                this.wallet = {
                    profit: {
                        profitWallet: res.data.profitWallet,
                        profitFreezeWallet: res.data.profitFreezeWallet
                    },
                    cashBack: {
                        cashBackWallet: res.data.cashBackWallet,
                        cashBackFreezeWallet: res.data.cashBackFreezeWallet
                    },
                    game: {
                        gameProfitWallet: res.data.gameProfitWallet,
                        gameProfitFreezeT1Wallet: res.data.gameProfitFreezeWallet + res.data.gameProfitT1Wallet
                    },
                    join03: {
                        joinProfitWallet: res.data['03JoinProfitD0Wallet'] || 0,
                        joinFreezeWallet: res.data['03JoinProfitFreezeWallet'] + res.data['03JoinProfitT1Wallet'] || 0
                    },
                    join04: {
                        joinProfitWallet: res.data['04JoinProfitD0Wallet'] || 0,
                        joinFreezeWallet: res.data['04JoinProfitFreezeWallet'] + res.data['04JoinProfitT1Wallet'] || 0
                    },
                    join05: {
                        joinProfitWallet: res.data['05JoinProfitD0Wallet'] || 0,
                        joinFreezeWallet: res.data['05JoinProfitFreezeWallet'] + res.data['05JoinProfitT1Wallet'] || 0
                    },
                    finance: {
                        financeProfitWallet: res.data.financeProfitWallet,
                        financeProfitFreezeWallet: res.data.financeProfitFreezeWallet
                    },
                    reward: {
                        rewardWallet: res.data.rewardWallet,
                        rewardFreezeWallet: res.data.rewardFreezeWallet
                    }
                }
                this.withdrawStatus = res.data.withdrawStatus
                this.startTime = res.data.startTime
                this.endTime = res.data.endTime
                this.minAmount = res.data.handWithdrawMinAmt
            }
        })
        if (this.userInfo.payChannelCode === '1006') return
        getSettleCard().then(res => {
            if (res.code == '00') {
                this.bankCard = {
                    bankName: res.data.bankName,
                    bankCardNoMask: res.data.bankCardNoMask
                }
            }
        })
    },
    methods: {
        inputPsw(val) {
            this.payPsw += val
        },
        backspace(val) {
            if (this.payPsw.length) this.payPsw = this.payPsw.substr(0, this.payPsw.length - 1)
        },
        moneyTypeConfirm(val) {
            switch (val[0].value) {
                case '分润余额':
                    this.kind = '分润'
                    if (parseFloat(this.money) == parseFloat(this.wallet.profit.profitWallet)) {
                        this.money = this.wallet.profit.profitWallet != 0 ? this.wallet.profit.profitWallet : null
                    }
                    break
                case '返现余额':
                    this.kind = '返现'
                    if (parseFloat(this.money) == parseFloat(this.wallet.cashBack.cashBackWallet)) {
                        this.money = this.wallet.cashBack.cashBackWallet != 0 ? this.wallet.cashBack.cashBackWallet : null
                    }
                    break
                case '理财POS分润钱包':
                    this.kind = '理财POS分润钱包'
                    if (parseFloat(this.money) == parseFloat(this.wallet.finance.financeProfitWallet)) {
                        this.money = this.wallet.finance.financeProfitWallet != 0 ? this.wallet.finance.financeProfitWallet : null
                    }
                    break
                case '奖励钱包':
                    this.kind = '奖励钱包'
                    if (parseFloat(this.money) == parseFloat(this.wallet.reward.rewardWallet)) {
                        this.money = this.wallet.reward.rewardWallet != 0 ? this.wallet.reward.rewardWallet : null
                    }
                    break
                case '增值返佣':
                    this.kind = '增值'
                    if (parseFloat(this.money) == parseFloat(this.wallet.game.gameProfitWallet)) {
                        this.money = this.wallet.game.gameProfitWallet != 0 ? this.wallet.game.gameProfitWallet : null
                    }
                    break
                case '加盟返佣':
                    this.showJoinType = true
                    break
                case '东山岛海产品':
                    this.kind = '东山岛海产品返佣'
                    this.showJoinType = false
                    if (parseFloat(this.money) == parseFloat(this.wallet.join03.joinProfitWallet)) {
                        this.money = this.wallet.join03.joinProfitWallet != 0 ? this.wallet.join03.joinProfitWallet : null
                    }
                    break
                case '青田泽济农资':
                    this.kind = '青田泽济农资返佣'
                    this.showJoinType = false
                    if (parseFloat(this.money) == parseFloat(this.wallet.join04.joinProfitWallet)) {
                        this.money = this.wallet.join04.joinProfitWallet != 0 ? this.wallet.join04.joinProfitWallet : null
                    }
                    break
                case '鹏农商城':
                    this.kind = '鹏农商城返佣'
                    this.showJoinType = false
                    if (parseFloat(this.money) == parseFloat(this.wallet.join05.joinProfitWallet)) {
                        this.money = this.wallet.join05.joinProfitWallet != 0 ? this.wallet.join05.joinProfitWallet : null
                    }
                    break
                default:
            }
            this.showMoneyType = false
        },
        toDecimal2(x) {
            var f = parseFloat(x)
            if (isNaN(f)) {
                return false
            }
            var f = Math.round(x * 100) / 100,
                s = f.toString(),
                rs = s.indexOf('.')
            if (rs < 0) {
                rs = s.length
                s += '.'
            }
            while (s.length <= rs + 2) {
                s += '0'
            }
            return s
        },
        bill() {
            this.$Router.push({ name: 'Bill' })
        },
        all() {
            switch (this.kind) {
                case '分润':
                    this.money = this.wallet.profit.profitWallet != 0 ? this.wallet.profit.profitWallet : null
                    break
                case '返现':
                    this.money = this.wallet.cashBack.cashBackWallet != 0 ? this.wallet.cashBack.cashBackWallet : null
                    break
                case '理财POS分润钱包':
                    this.money = this.wallet.finance.financeProfitWallet != 0 ? this.wallet.finance.financeProfitWallet : null
                    break
                case '奖励钱包':
                    this.money = this.wallet.reward.rewardWallet != 0 ? this.wallet.reward.rewardWallet : null
                    break
                case '增值':
                    this.money = this.wallet.game.gameProfitWallet != 0 ? this.wallet.game.gameProfitWallet : null
                    break
                case '东山岛海产品返佣':
                    this.money = this.wallet.join03.joinProfitWallet != 0 ? this.wallet.join03.joinProfitWallet : null
                    break
                case '青田泽济农资返佣':
                    this.money = this.wallet.join04.joinProfitWallet != 0 ? this.wallet.join04.joinProfitWallet : null
                    break
                case '鹏农商城返佣':
                    this.money = this.wallet.join05.joinProfitWallet != 0 ? this.wallet.join05.joinProfitWallet : null
                    break
                default:
            }
        },
        protocol() {
            this.$Router.push({ name: 'Protocol' })
        },
        closeSecret() {
            this.showEnterPsw = false
            this.payPsw = ''
        },
        temporalInterval(startTime, endTime) {
            let date = new Date()
            let hour = date.getHours() >= 10 ? date.getHours() : '0' + date.getHours()
            let minute = date.getMinutes() >= 10 ? date.getMinutes() : '0' + date.getMinutes()
            let time = new Date('2020/05/20 ' + hour + ':' + minute)
            let start = new Date('2020/05/20 ' + startTime)
            let end = new Date('2020/05/20 ' + endTime)
            if (time >= start && time <= end) {
                return true
            } else {
                return false
            }
        },
        async submit() {
            if (this.temporalInterval(this.startTime, this.endTime)) {
                if (/^([1-9]\d{0,}|(0\.\d{0,1}[1-9])|([1-9]\d{0,}\.\d{0,1}[1-9]))$/.test(this.money)) {
                    if (parseFloat(this.money) <= parseFloat(this.kind == '分润' ? this.wallet.profit.profitWallet : this.kind == '返现' ? this.wallet.cashBack.cashBackWallet : this.kind == '增值' ? this.wallet.game.gameProfitWallet : this.kind == '东山岛海产品返佣' ? this.wallet.join03.joinProfitWallet : this.kind == '青田泽济农资返佣' ? this.wallet.join04.joinProfitWallet : this.kind == '鹏农商城返佣' ? this.wallet.join05.joinProfitWallet : this.kind == '理财POS分润钱包' ? this.wallet.finance.financeProfitWallet : this.wallet.reward.rewardWallet)) {
                        if (parseFloat(this.money) >= parseFloat(this.minAmount)) {
                            // if (this.showProtocol && !this.agreement) {
                            //     uni.showToast({ title: '请勾选《服务合作协议》', icon: 'none' })
                            // } else {
                                let { payChannelCode, cloudOpenStatus, taxationRegisterStatus ,dlgSignStatue, hkpaySignStatue} = this.$store.state.userInfo
                                if (cloudOpenStatus === 0 || taxationRegisterStatus === 0) {
                                    const { data = {} } = await getRegisterStatus()
                                    if (data.isRegister) {
                                        if (payChannelCode === '1004' && data.registerType) {
                                            return uni.showModal({
                                                content: `请先完成云户通开户`,
                                                confirmColor: '#004ea9',
                                                success: res => {
                                                    if (res.confirm) {
                                                        this.$Router.push({ name: 'CloudOpen' })
                                                    }
                                                }
                                            })
                                        }
                                        if (payChannelCode !== '1004' && !data.registerType) {
                                            return uni.showModal({
                                                content: `请先完成税筹注册`,
                                                confirmColor: '#004ea9',
                                                success: res => {
                                                    if (res.confirm) {
                                                        this.$Router.push({ name: 'TaxRaiseRegister' })
                                                    }
                                                }
                                            })
                                        }
                                    }
                                }
                                if (payChannelCode === '1007' && dlgSignStatue === 0) {
                                            return uni.showModal({
                                                content: `请先完成出款签约`,
                                                confirmColor: '#004ea9',
                                                success: res => {
                                                    if (res.confirm) {
                                                        this.$Router.push({ name: 'DlgSignStatue' })
                                                    }
                                                }
                                            })
                                        }
                                    if( payChannelCode === '1009' &&  hkpaySignStatue === 0) {
                                            return uni.showModal({
                                                content: `请先完成出款签约`,
                                                confirmColor: '#004ea9',
                                                success: res => {
                                                    if (res.confirm) {
                                                        this.$Router.push({ name: 'HkpaySign' })
                                                    }
                                                }
                                            })
                                    }
                                const params = {
                                    walletType: this.kind == '分润' ? 100 : this.kind == '返现' ? 200 : this.kind == '增值' ? 504 : this.kind == '理财POS分润钱包' ? 601 : this.kind == '奖励钱包' ? 603 : 508,
                                    withdrawAmount: parseFloat(this.money)
                                }
                                queryLoans(params).then(res => {
                                    if (res.code == '00') {
                                        if (res.data == null) {
                                            getPayPsw().then(res => {
                                                if (res.code == '00') {
                                                    if (res.data) {
                                                        this.payPsw = ''
                                                        this.showEnterPsw = true
                                                    } else {
                                                        uni.showModal({
                                                            content: '请设置支付密码进行提现',
                                                            success: res => res.confirm && this.setPsw()
                                                        })
                                                    }
                                                }
                                            })
                                        } else {
                                            const routerParams = {
                                                walletType: this.kind == '分润' ? 100 : this.kind == '返现' ? 200 : this.kind == '增值' ? 504 : this.kind == '理财POS分润钱包' ? 601 : this.kind == '奖励钱包' ? 603 : 508,
                                                ...res.data
                                            }
                                            this.$Router.push({
                                                name: 'ConfirmWithdrawMoney',
                                                params: routerParams
                                            })
                                        }
                                    }
                                })
                            // }
                        } else {
                            uni.showToast({ title: '提现金额不得低于最低提现金额！', icon: 'none' })
                        }
                    } else {
                        uni.showToast({ title: '提现金额不得大于可提现金额！', icon: 'none' })
                    }
                } else {
                    uni.showToast({ title: '提现金额不能为空且不得超过2位小数！', icon: 'none' })
                }
            } else {
                uni.showToast({ title: '超出提现时间范围！', icon: 'none' })
            }
        },
        setPsw() {
            this.$Router.push({ name: 'SetResetPayPsw', params: { type: 0 } })
        },
        forgetPayPsw() {
            this.$Router.push({ name: 'SetResetPayPsw', params: { type: 1 } })
            this.showEnterPsw = false
        },
        withdraw() {
            if (this.payPsw.length <= 6) {
                const params = {
                    walletType: this.kind == '分润' ? 100 : this.kind == '返现' ? 200 : this.kind == '增值' ? 504 : this.kind == '理财POS分润钱包' ? 601 : this.kind == '奖励钱包' ? 603 : 508,
                    withdrawAmount: parseFloat(this.money),
                    payPwd: this.payPsw
                }
                if (params.walletType == 508) {
                    params.withdrawSeriesNo = seriesMap[this.kind]
                }
                if (this.isLocked) {
                    return uni.showToast({ title: '处理中, 请勿重复操作', icon: 'none' })
                }
                this.isLocked = true
                withdraw(params).then(res => {
                    this.isLocked = false
                    if (res.code == '00') {
                        uni.showToast({ title: res.message, icon: 'none' })

                        setTimeout(() => {
                            walletMoney().then(res => {
                                this.showEnterPsw = false
                                if (res.code == '00') {
                                    this.wallet = {
                                        profit: {
                                            profitWallet: res.data.profitWallet,
                                            profitFreezeWallet: res.data.profitFreezeWallet
                                        },
                                        cashBack: {
                                            cashBackWallet: res.data.cashBackWallet,
                                            cashBackFreezeWallet: res.data.cashBackFreezeWallet
                                        },
                                        game: {
                                            gameProfitWallet: res.data.gameProfitWallet,
                                            gameProfitFreezeT1Wallet: res.data.gameProfitFreezeWallet + res.data.gameProfitT1Wallet
                                        },
                                        join03: {
                                            joinProfitWallet: res.data['03JoinProfitD0Wallet'] || 0,
                                            joinFreezeWallet: res.data['03JoinProfitFreezeWallet'] + res.data['03JoinProfitT1Wallet'] || 0
                                        },
                                        join04: {
                                            joinProfitWallet: res.data['04JoinProfitD0Wallet'] || 0,
                                            joinFreezeWallet: res.data['04JoinProfitFreezeWallet'] + res.data['04JoinProfitT1Wallet'] || 0
                                        },
                                        join05: {
                                            joinProfitWallet: res.data['05JoinProfitD0Wallet'] || 0,
                                            joinFreezeWallet: res.data['05JoinProfitFreezeWallet'] + res.data['05JoinProfitT1Wallet'] || 0
                                        },
                                        finance: {
                                            financeProfitWallet: res.data.financeProfitWallet,
                                            financeProfitFreezeWallet: res.data.financeProfitFreezeWallet
                                        },
                                        reward: {
                                            rewardWallet: res.data.rewardWallet,
                                            rewardFreezeWallet: res.data.rewardFreezeWallet
                                        }
                                    }
                                    this.money = null
                                    this.agreement = false
                                }
                            })
                        }, 1500)
                    }
                })
            }
        }
    }
}
</script>

<style lang="scss" scoped>
#withdrawMoney {
    padding-top: 20rpx;
    height: 100%;
    > header {
        text-align: right;
    }
    > main {
        height: 100%;
        background-color: #fff;
        padding: 20rpx 30rpx;
        > div {
            &:nth-of-type(1) {
                height: 128rpx;
                background-image: url('../../static/images/common/bankBgm.png');
                background-size: 100% 100%;
                margin-bottom: 20rpx;
                display: flex;
                > image {
                    width: 52rpx;
                    height: 52rpx;
                    margin: 40rpx 20rpx 38rpx 30rpx;
                }
                > section {
                    width: 100%;
                    color: white;
                    > p {
                        margin: 0;
                        &:nth-of-type(1) {
                            margin: 30rpx 0 10rpx;
                        }
                        &:nth-of-type(2) {
                            font-size: 24rpx;
                            display: flex;
                            justify-content: space-between;
                            > span {
                                &:nth-of-type(2) {
                                    margin-right: 30rpx;
                                }
                            }
                        }
                    }
                }
            }
            &:nth-of-type(2) {
                background: #f3f5f7;
                border-radius: 20rpx;
                > div {
                    padding: 26rpx 30rpx;
                    position: relative;
                    > span {
                        font-size: 26rpx;
                        color: #222222;
                    }
                    > view {
                        display: flex;
                        align-items: center;
                        position: absolute;
                        top: 32rpx;
                        right: 30rpx;
                        image {
                            width: 32rpx;
                            height: 8rpx;
                        }

                        &::after {
                            content: '';
                            position: absolute;
                            top: -10rpx;
                            left: 0;
                            width: 60rpx;
                            height: 60rpx;
                        }
                    }
                }
                > section {
                    margin: 0 30rpx;
                    border-top: 2rpx solid #e5e5e5;
                    display: flex;
                    > div {
                        flex: 1;
                        > p {
                            margin: 0;
                            &:nth-of-type(1) {
                                > image {
                                    width: 12rpx;
                                    height: 12rpx;
                                    margin: 42rpx 12rpx 0 22rpx;
                                }
                                > span {
                                    font-size: 24rpx;
                                    color: #666666;
                                    position: relative;
                                    top: 6rpx;
                                }
                            }
                            &:nth-of-type(2) {
                                font-size: 36rpx;
                                color: #ce0010;
                                margin: 20rpx 0 38rpx 46rpx;
                            }
                        }
                    }
                }
            }
            &:nth-of-type(3) {
                background: #f3f5f7;
                border-radius: 20rpx;
                padding: 42rpx 30rpx 22rpx;
                margin: 20rpx 0 12rpx;
                > p {
                    margin: 0;
                    &:nth-of-type(1) {
                        > span {
                            color: #222222;
                            &:nth-of-type(1) {
                                > span {
                                    font-size: 24rpx;
                                    color: #ce0010;
                                }
                            }
                            &:nth-of-type(2) {
                                color: #ce0010;
                            }
                        }
                    }
                    &:last-of-type {
                        margin-top: 22rpx;
                        > span {
                            font-size: 24rpx;
                            &:nth-of-type(1) {
                                color: #666666;
                            }
                            &:nth-of-type(2) {
                                color: #222222;
                            }
                        }
                    }
                }
                .all-withdraw {
                    display: flex;
                    align-items: center;
                    margin-top: 16rpx;
                    > image {
                        width: 40rpx;
                        height: 48rpx;
                        margin-right: 20rpx;
                    }
                    > view {
                        flex: 1;
                        /deep/ .u-label {
                            display: none;
                        }
                        /deep/ .u-btn--primary {
                            padding: 0 10rpx;
                            border-color: none;
                            background-color: transparent;
                            color: $theme-color;
                            font-size: 28rpx;
                        }
                    }
                }
            }
        }
        > section {
            > p {
                color: #666666;
                position: relative;
                margin: 0;
                margin-top: 30rpx;
                > span {
                    font-size: 24rpx;
                    margin-left: 10rpx;
                    position: relative;
                    &:nth-of-type(2) {
                        color: #004ea9;
                    }
                }
                /deep/ .u-checkbox__label {
                    display: none;
                }
                /deep/ .u-checkbox {
                    display: block;
                }
            }
        }
        > .withdraw-btn {
            width: 570rpx;
            height: 78rpx;
            font-size: 26rpx;
            color: white;
            border-radius: 10rpx;
            margin: 100rpx auto 40rpx;
        }
        .record {
            width: 570rpx;
            height: 78rpx;
            font-size: 26rpx;
            color: #8799a3;
            margin: 0 auto;
            text-align: center;
            line-height: 78rpx;
            border: 2rpx solid #8799a3;
            border-radius: 78rpx;
        }
    }
    #enter-psw {
        section {
            margin-top: 30rpx;
            > p {
                margin: 20rpx 0;
                text-align: center;
                color: #4e77d9;
            }
        }
    }
    /deep/ #set-psw {
        width: 540rpx;
        height: 280rpx;
        p {
            color: #000000;
            font-size: 26rpx;
        }
    }
}
</style>
