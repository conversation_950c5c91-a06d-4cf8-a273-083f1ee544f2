<template>
  <div id="rechargeList">
    <main>
      <u-sticky>
        <header class="filter-header">
          <u-dropdown ref="uDropdown" :close-on-click-mask="false" :close-on-click-self="false" @open="openFilterHeader">
            <u-dropdown-item title="筛选">
              <view class="filter-form">
                <u-field v-model="params.chlOrderNo" label="渠道订单号" placeholder="请输入渠道订单号" />
                <u-field v-model="params.agentCode" label="代理商编号" placeholder="请输入代理商编号" />
                <u-field v-model="params.agentName" label="代理商名称" placeholder="请输入代理商名称" />
                <u-field v-model="params.merchantCode" label="商户编号" placeholder="请输入商户编号" />
                <u-field v-model="params.merchantName" label="商户名称" placeholder="请输入商户名称" />
                <u-field
                  label="支付通道"
                  placeholder="选择支付通道"
                  :value="payOrgCode"
                  @click="showPayOrgCodes"
                  :right-icon="`arrow-${payOrgCodesPicker ? 'up' : 'down'}`"
                  disabled
                />
                <u-radio-group v-model="params.cashbackStatus" shape="square">
                  <u-radio :name="null">全部状态</u-radio>
                  <u-radio :name="0">未返现</u-radio>
                  <u-radio :name="2">返现成功</u-radio>
                  <u-radio :name="4">无需返现</u-radio>
                  <u-radio :name="1">返现中</u-radio>
                  <u-radio :name="3">返现失败</u-radio>
                </u-radio-group>
                <section class="filtrate-time">
                  <u-field disabled :border-bottom="false" :value="startTime" placeholder="开始时间" @click="showStartPicker = true" />
                  <span>—</span>
                  <u-field disabled :border-bottom="false" :value="endTime" placeholder="结束时间" @click="showEndPicker = true" />
                </section>
                <div class="btnTools">
                  <u-button size="medium" @click="toReset">重置</u-button>
                  <u-button size="medium" color="#004ea9" type="primary" @click="$refs.uDropdown.close(), getPageData(true)">确定</u-button>
                </div>
              </view>
            </u-dropdown-item>
          </u-dropdown>
        </header>
      </u-sticky>

      <no-content v-if="show" />

      <view class="list-data" v-show="!show">
        <div class="card" v-for="(m, index) in pageData" :key="index" @click="toDetail(m.id)">
          <p v-if="m.payOrgCode">
            支付通道: <span>{{ m.payOrgCode | orgCodeFormat }}</span>
          </p>
          <p>
            <span>代理商编号: </span> <span>{{ m.agentCode }}</span>
          </p>
          <p>
            <span>代理商名称: </span> <span>{{ m.agentName }}</span>
          </p>
          <p>
            <span>商户编号: </span> <span>{{ m.merchantCode }}</span>
          </p>
          <p>
            <span>商户名称: </span> <span>{{ m.merchantName }}</span>
          </p>
          <p>
            <span>渠道订单号: </span> <span>{{ m.chlOrderNo }}</span>
          </p>
          <!-- <p><span>提现金额: </span> <span>{{ m.withdrawAmount }} 元</span></p> -->
          <p>
            <span>金额: </span> <span>{{ m.withdrawFee }} 元</span>
          </p>
          <p>
            <span>返现政策: </span> <span>{{ m.modelName }}</span>
          </p>
          <p>
            <span>返现状态: </span>
            <span style="color: darkslategrey">{{
              m.returnStatus == 0
                ? '未返现'
                : m.returnStatus == 1
                ? '返现中'
                : m.returnStatus == 2
                ? '返现成功'
                : m.returnStatus == 3
                ? '返现失败'
                : m.returnStatus == 4
                ? '无需返现'
                : ''
            }}</span>
          </p>
          <p v-if="m.returnStatus === 2">
            <span>返现时间: </span> <span>{{ m.returnTime ? dateFormat(m.returnTime) : '--' }}</span>
          </p>
          <p>
            <span>创建时间: </span> <span>{{ m.createTime ? dateFormat(m.createTime) : '--' }}</span>
          </p>
          <p v-if="m.failMsg">
            <span>异常信息: </span> <span style="color: red">{{ m.failMsg }}</span>
          </p>
        </div>
      </view>
      <u-loadmore v-if="!show" :status="status" @loadmore="getPageData(false)" />
    </main>

    <!-- 选择时间 proup -->
    <u-picker v-model="showStartPicker" title="开始时间" :show-time-tag="false" mode="time" @confirm="onConfirmStart" />
    <u-picker v-model="showEndPicker" title="结束时间" :show-time-tag="false" mode="time" @confirm="onConfirmEnd" />

    <u-select
      v-model="payOrgCodesPicker"
      :list="payOrgCodes"
      label-name="name"
      value-name="code"
      @confirm="payOrgCodeConfirm"
      :default-value="payOrgCode ? [payOrgCodes.findIndex(i => i.name === payOrgCode)] : []"
    />
  </div>
</template>

<script>
import { dateFormat, toDecimal2 } from '../../static/utils/date';
import { queryMerRateRecordByPage, getChannel } from '../../http/api';

export default {
  name: 'MerchantWithdrawalFee',
  filters: {
    toDecimal2
  },
  data() {
    return {
      payOrgCodesPicker: false,
      payOrgCodes: [],
      payOrgCode: '',
      total: null,
      show: false,
      showStartPicker: false,
      showEndPicker: false,
      startTime: dateFormat(new Date()).substring(0, 10),
      endTime: dateFormat(new Date()).substring(0, 10),
      oldParams: null,
      params: {
        chlOrderNo: '', //渠道订单号
        agentCode: '', //代理商编号
        agentName: '', //代理商名称
        merchantCode: '', //商户编号
        merchantName: '', //商户名称
        cashbackStatus: null, //返现状态  0 未返现 1 返现中 2 返现成功 3 返现失败 4 无需返现
        startTime: '',
        endTime: '',
        payOrgCode: '',
        pageNo: 1,
        pageSize: 10
      },
      pageData: [],
      status: 'loading',
      dateFormat: dateFormat
    };
  },
  onLoad() {
    this.getPageData(true);
  },
  onReachBottom() {
    this.getPageData(false);
  },
  methods: {
    showPayOrgCodes() {
      this.payOrgCodes = [];
      getChannel().then(res => {
        if (res.code == '00') {
          if (res.data.length != 0) {
            this.payOrgCodes = [{ name: '全部', code: '' }, ...res.data];
            this.payOrgCodesPicker = true;
          } else {
            this.$store.commit('showTip', {
              show: true,
              content: '暂无可选支付通道！'
            });
          }
        }
      });
    },
    payOrgCodeConfirm({ name, code = '' }) {
      this.payOrgCode = name === '全部' ? '' : name;
      this.params.payOrgCode = code;
      this.payOrgCodesPicker = false;
    },
    toDetail(id) {
      this.$Router.push({ name: 'MerchantWithdrawalFeeDetail', params: { id } });
    },
    openFilterHeader() {
      this.oldParams = JSON.parse(JSON.stringify(this.params));
    },
    toReset() {
      var oldData = this.$options.data();
      this.params = oldData.params;
      this.startTime = dateFormat(new Date()).substring(0, 10);
      this.endTime = dateFormat(new Date()).substring(0, 10);
      this.payOrgCode = '';
    },
    onConfirmStart(val) {
      this.startTime = dateFormat(val.timestamp * 1000).substring(0, 10);
      this.showStartPicker = false;
    },
    onConfirmEnd(val) {
      this.endTime = dateFormat(val.timestamp * 1000).substring(0, 10);
      this.showEndPicker = false;
    },
    getPageData(isInquire) {
      this.params.startTime = this.startTime == '' ? null : this.startTime + ' 00:00:00';
      this.params.endTime = this.endTime == '' ? null : this.endTime + ' 23:59:59';

      if (!isInquire && this.status == 'nomore') return;
      if (isInquire && JSON.stringify(this.oldParams) == JSON.stringify(this.params)) {
        return;
      }
      this.status = 'loading';

      this.params.pageNo = isInquire ? 1 : this.params.pageNo + 1;
      queryMerRateRecordByPage(this.params).then(res => {
        if (res.code == '00') {
          if (res.data.list.length != 0) {
            this.show = false;
            this.total = res.data.total;
            !isInquire
              ? res.data.list.forEach(i => {
                  this.pageData.push(i);
                })
              : (this.pageData = res.data.list);
            isInquire &&
              uni.pageScrollTo({
                scrollTop: 0
              });
          } else {
            this.show = true;
          }

          if (this.pageData.length >= this.total) {
            // 数据全部加载完成
            this.status = 'nomore';
          } else {
            this.status = 'loadmore';
          }
        }
      });
    }
  }
};
</script>

<style lang="less">
@import '../../static/css/game.less';

#rechargeList {
  main {
    .list-data {
      padding: 20rpx 30rpx 0;
      .card {
        font-size: 24rpx;
        background: #fff;
        border-radius: 20rpx;
        padding: 32rpx 32rpx 2rpx;
        margin-bottom: 20rpx;
        position: relative;
        > p {
          color: #666666;
          margin: 0 0 10rpx 0;
          > span {
            &:first-child {
              display: inline-block;
              min-width: 136rpx;
            }
            &:last-child {
              color: #222222;
            }
          }
        }
      }
    }

    /deep/ .u-radio-group {
      padding: 20rpx 28rpx;
    }
  }
}
</style>
